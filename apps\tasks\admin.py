"""
异步任务管理后台配置
"""
from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from django.utils.html import format_html
from django.urls import reverse, path
from django.http import JsonResponse
from django.shortcuts import get_object_or_404
from unfold.admin import ModelAdmin
from unfold.contrib.filters.admin import RangeDateFilter, ChoicesDropdownFilter
from .models import TaskQueue, TaskSchedule, TaskResult
from .executor import task_executor


class TaskResultInline(admin.TabularInline):
    """任务执行结果内联"""
    model = TaskResult
    extra = 0
    readonly_fields = ('worker_id', 'started_at', 'completed_at', 'duration', 'success', 'result', 'error_message')
    
    def has_add_permission(self, request, obj=None):
        return False


@admin.register(TaskQueue)
class TaskQueueAdmin(ModelAdmin):
    """任务队列管理"""
    
    list_display = (
        'name', 'task_type', 'status', 'priority', 'progress_display',
        'created_by', 'created_at', 'started_at', 'completed_at'
    )
    
    list_filter = (
        ('status', ChoicesDropdownFilter),
        ('priority', ChoicesDropdownFilter),
        ('task_type', ChoicesDropdownFilter),
        ('created_at', RangeDateFilter),
        ('completed_at', RangeDateFilter),
    )
    
    search_fields = ('name', 'task_function', 'created_by__username')
    
    readonly_fields = (
        'task_id', 'worker_id', 'started_at', 'completed_at',
        'result', 'error_message', 'traceback', 'created_at', 'updated_at'
    )
    
    fieldsets = (
        (_('基本信息'), {
            'fields': ('task_id', 'name', 'description', 'task_type', 'task_function')
        }),
        (_('任务参数'), {
            'fields': ('args', 'kwargs'),
            'classes': ('collapse',)
        }),
        (_('状态信息'), {
            'fields': ('status', 'priority', 'progress', 'progress_message')
        }),
        (_('执行信息'), {
            'fields': ('worker_id', 'started_at', 'completed_at')
        }),
        (_('重试配置'), {
            'fields': ('max_retries', 'retry_count', 'retry_delay'),
            'classes': ('collapse',)
        }),
        (_('结果信息'), {
            'fields': ('result', 'error_message', 'traceback'),
            'classes': ('collapse',)
        }),
        (_('其他信息'), {
            'fields': ('created_by', 'expires_at', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    inlines = [TaskResultInline]
    
    actions = ['cancel_tasks', 'retry_tasks', 'delete_completed_tasks']
    
    def progress_display(self, obj):
        """显示进度"""
        if obj.status == 'running':
            return format_html(
                '<div style="width: 100px; background: #f0f0f0; border-radius: 3px;">'
                '<div style="width: {}%; background: #409EFF; height: 20px; border-radius: 3px; '
                'text-align: center; color: white; line-height: 20px; font-size: 12px;">'
                '{}%</div></div>',
                obj.progress, obj.progress
            )
        elif obj.status == 'completed':
            return format_html('<span style="color: #67C23A;">✓ 完成</span>')
        elif obj.status == 'failed':
            return format_html('<span style="color: #F56C6C;">✗ 失败</span>')
        else:
            return obj.get_status_display()
    progress_display.short_description = _('进度')
    
    def cancel_tasks(self, request, queryset):
        """取消任务"""
        updated = 0
        for task in queryset.filter(status__in=['pending', 'running']):
            task.status = 'cancelled'
            task.save(update_fields=['status', 'updated_at'])
            updated += 1
        
        self.message_user(request, f'成功取消 {updated} 个任务')
    cancel_tasks.short_description = _('取消选中的任务')
    
    def retry_tasks(self, request, queryset):
        """重试任务"""
        updated = 0
        for task in queryset.filter(status='failed'):
            if task.can_retry():
                task.status = 'pending'
                task.retry_count += 1
                task.error_message = ''
                task.traceback = ''
                task.save(update_fields=['status', 'retry_count', 'error_message', 'traceback', 'updated_at'])
                updated += 1
        
        self.message_user(request, f'成功重试 {updated} 个任务')
    retry_tasks.short_description = _('重试失败的任务')
    
    def delete_completed_tasks(self, request, queryset):
        """删除已完成的任务"""
        completed_tasks = queryset.filter(status__in=['completed', 'failed', 'cancelled'])
        count = completed_tasks.count()
        completed_tasks.delete()
        
        self.message_user(request, f'成功删除 {count} 个已完成的任务')
    delete_completed_tasks.short_description = _('删除已完成的任务')
    
    def get_urls(self):
        """添加自定义URL"""
        urls = super().get_urls()
        custom_urls = [
            path('task-stats/', self.admin_site.admin_view(self.task_stats_view), name='task_stats'),
        ]
        return custom_urls + urls
    
    def task_stats_view(self, request):
        """任务统计视图"""
        from django.db.models import Count
        
        stats = TaskQueue.objects.aggregate(
            total=Count('id'),
            pending=Count('id', filter=models.Q(status='pending')),
            running=Count('id', filter=models.Q(status='running')),
            completed=Count('id', filter=models.Q(status='completed')),
            failed=Count('id', filter=models.Q(status='failed')),
            cancelled=Count('id', filter=models.Q(status='cancelled')),
        )
        
        return JsonResponse(stats)


@admin.register(TaskSchedule)
class TaskScheduleAdmin(ModelAdmin):
    """定时任务管理"""
    
    list_display = (
        'name', 'schedule_type', 'is_active', 'total_runs',
        'success_runs', 'failed_runs', 'last_run_at', 'next_run_at'
    )
    
    list_filter = (
        ('schedule_type', ChoicesDropdownFilter),
        'is_active',
        ('last_run_at', RangeDateFilter),
        ('next_run_at', RangeDateFilter),
    )
    
    search_fields = ('name', 'task_function', 'description')
    
    readonly_fields = (
        'total_runs', 'success_runs', 'failed_runs',
        'last_run_at', 'next_run_at', 'created_at', 'updated_at'
    )
    
    fieldsets = (
        (_('基本信息'), {
            'fields': ('name', 'description', 'task_function')
        }),
        (_('调度配置'), {
            'fields': ('schedule_type', 'schedule_config')
        }),
        (_('任务参数'), {
            'fields': ('args', 'kwargs'),
            'classes': ('collapse',)
        }),
        (_('状态'), {
            'fields': ('is_active',)
        }),
        (_('统计信息'), {
            'fields': ('total_runs', 'success_runs', 'failed_runs'),
            'classes': ('collapse',)
        }),
        (_('时间信息'), {
            'fields': ('last_run_at', 'next_run_at', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    actions = ['activate_schedules', 'deactivate_schedules', 'run_now']
    
    def activate_schedules(self, request, queryset):
        """激活定时任务"""
        updated = queryset.update(is_active=True)
        
        # 重新计算下次执行时间
        for schedule in queryset:
            schedule.calculate_next_run()
        
        self.message_user(request, f'成功激活 {updated} 个定时任务')
    activate_schedules.short_description = _('激活选中的定时任务')
    
    def deactivate_schedules(self, request, queryset):
        """停用定时任务"""
        updated = queryset.update(is_active=False)
        self.message_user(request, f'成功停用 {updated} 个定时任务')
    deactivate_schedules.short_description = _('停用选中的定时任务')
    
    def run_now(self, request, queryset):
        """立即执行"""
        created = 0
        for schedule in queryset:
            task = TaskQueue.objects.create(
                name=f"[手动执行] {schedule.name}",
                description=schedule.description,
                task_type='manual',
                task_function=schedule.task_function,
                args=schedule.args,
                kwargs=schedule.kwargs,
                priority='high',
                created_by=request.user
            )
            created += 1
        
        self.message_user(request, f'成功创建 {created} 个立即执行任务')
    run_now.short_description = _('立即执行选中的任务')


@admin.register(TaskResult)
class TaskResultAdmin(ModelAdmin):
    """任务执行结果管理"""
    
    list_display = (
        'task', 'worker_id', 'success', 'duration',
        'started_at', 'completed_at'
    )
    
    list_filter = (
        'success',
        ('started_at', RangeDateFilter),
        ('completed_at', RangeDateFilter),
    )
    
    search_fields = ('task__name', 'worker_id', 'error_message')
    
    readonly_fields = (
        'task', 'worker_id', 'started_at', 'completed_at', 'duration',
        'success', 'result', 'error_message', 'traceback',
        'memory_usage', 'cpu_usage', 'created_at'
    )
    
    fieldsets = (
        (_('任务信息'), {
            'fields': ('task', 'worker_id')
        }),
        (_('执行时间'), {
            'fields': ('started_at', 'completed_at', 'duration')
        }),
        (_('执行结果'), {
            'fields': ('success', 'result', 'error_message', 'traceback')
        }),
        (_('资源使用'), {
            'fields': ('memory_usage', 'cpu_usage'),
            'classes': ('collapse',)
        }),
        (_('其他'), {
            'fields': ('created_at',),
            'classes': ('collapse',)
        }),
    )
    
    def has_add_permission(self, request):
        return False
    
    def has_change_permission(self, request, obj=None):
        return False
