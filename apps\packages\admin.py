"""
套餐系统管理后台
"""
from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from django.utils.html import format_html
from unfold.admin import ModelAdmin, TabularInline
from unfold.contrib.filters.admin import RangeDateFilter, ChoicesDropdownFilter

from .models import PackageCategory, Package, PackageFeature, PackageFeatureRelation


class PackageFeatureRelationInline(TabularInline):
    """套餐功能关联内联编辑"""
    model = PackageFeatureRelation
    extra = 0
    fields = ('feature', 'is_included', 'limit_value', 'custom_description', 'sort_order')
    ordering = ('sort_order', 'feature__sort_order')


@admin.register(PackageCategory)
class PackageCategoryAdmin(ModelAdmin):
    """套餐分类管理"""

    list_display = ('name', 'sort_order', 'is_active', 'package_count', 'created_at')

    list_filter = ('is_active', ('created_at', RangeDateFilter))

    search_fields = ('name', 'description')

    readonly_fields = ('created_at', 'updated_at', 'package_count')

    fieldsets = (
        (_('基本信息'), {
            'fields': ('name', 'description')
        }),
        (_('设置'), {
            'fields': ('sort_order', 'is_active')
        }),
        (_('统计信息'), {
            'fields': ('package_count', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    ordering = ('sort_order', 'name')

    def package_count(self, obj):
        """关联的套餐数量"""
        return obj.packages.count()
    package_count.short_description = _('套餐数量')


@admin.register(Package)
class PackageAdmin(ModelAdmin):
    """套餐管理"""

    list_display = (
        'name', 'category', 'package_type', 'price_display', 'computing_power_display',
        'is_recommended', 'is_active', 'sales_count', 'created_at'
    )

    list_filter = (
        'category', ('package_type', ChoicesDropdownFilter), 'is_active', 'is_recommended',
        ('duration_type', ChoicesDropdownFilter), ('created_at', RangeDateFilter)
    )

    search_fields = ('name', 'description')

    readonly_fields = ('sales_count', 'created_at', 'updated_at', 'total_computing_power')

    fieldsets = (
        (_('基本信息'), {
            'fields': ('category', 'name', 'description', 'package_type')
        }),
        (_('价格信息'), {
            'fields': ('price', 'original_price')
        }),
        (_('套餐内容'), {
            'fields': (
                'computing_power_amount', 'bonus_computing_power', 'total_computing_power',
                'synthesis_duration', 'voice_clone_count', 'avatar_count'
            )
        }),
        (_('有效期'), {
            'fields': ('duration_type', 'duration_value')
        }),
        (_('特性和状态'), {
            'fields': ('features', 'is_active', 'is_recommended', 'sort_order')
        }),
        (_('统计信息'), {
            'fields': ('sales_count', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    ordering = ('sort_order', '-created_at')

    inlines = [PackageFeatureRelationInline]

    actions = ['activate_packages', 'deactivate_packages', 'set_recommended', 'unset_recommended']

    def price_display(self, obj):
        """价格显示"""
        if obj.original_price and obj.original_price > obj.price:
            return format_html(
                '<span style="color: red; font-weight: bold;">¥{}</span> '
                '<span style="text-decoration: line-through; color: gray;">¥{}</span>',
                obj.price, obj.original_price
            )
        return f'¥{obj.price}'
    price_display.short_description = _('价格')

    def computing_power_display(self, obj):
        """算力显示"""
        if obj.bonus_computing_power > 0:
            return format_html(
                '<span style="color: blue; font-weight: bold;">{}</span> '
                '<span style="color: green;">(+{}赠送)</span>',
                obj.computing_power_amount, obj.bonus_computing_power
            )
        return str(obj.computing_power_amount)
    computing_power_display.short_description = _('算力')

    def activate_packages(self, request, queryset):
        """批量启用套餐"""
        updated = queryset.update(is_active=True)
        self.message_user(request, f'成功启用 {updated} 个套餐')
    activate_packages.short_description = _('启用选中的套餐')

    def deactivate_packages(self, request, queryset):
        """批量停用套餐"""
        updated = queryset.update(is_active=False)
        self.message_user(request, f'成功停用 {updated} 个套餐')
    deactivate_packages.short_description = _('停用选中的套餐')

    def set_recommended(self, request, queryset):
        """设为推荐套餐"""
        updated = queryset.update(is_recommended=True)
        self.message_user(request, f'成功将 {updated} 个套餐设为推荐')
    set_recommended.short_description = _('设为推荐套餐')

    def unset_recommended(self, request, queryset):
        """取消推荐套餐"""
        updated = queryset.update(is_recommended=False)
        self.message_user(request, f'成功取消 {updated} 个推荐套餐')
    unset_recommended.short_description = _('取消推荐套餐')


@admin.register(PackageFeature)
class PackageFeatureAdmin(ModelAdmin):
    """套餐功能管理"""

    list_display = (
        'name', 'feature_type', 'icon', 'is_highlight', 'sort_order',
        'is_active', 'package_count', 'created_at'
    )

    list_filter = (
        ('feature_type', ChoicesDropdownFilter), 'is_highlight', 'is_active',
        ('created_at', RangeDateFilter)
    )

    search_fields = ('name', 'description')

    readonly_fields = ('created_at', 'updated_at', 'package_count')

    fieldsets = (
        (_('基本信息'), {
            'fields': ('name', 'description', 'feature_type', 'icon')
        }),
        (_('设置'), {
            'fields': ('is_highlight', 'sort_order', 'is_active')
        }),
        (_('统计信息'), {
            'fields': ('package_count', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    ordering = ('sort_order', 'name')

    def package_count(self, obj):
        """关联的套餐数量"""
        return obj.package_relations.count()
    package_count.short_description = _('套餐数量')



