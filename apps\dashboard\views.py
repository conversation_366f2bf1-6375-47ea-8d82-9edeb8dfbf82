"""
控制台视图
"""
from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.db.models import Count, Sum, Q, Avg
from django.utils import timezone
from datetime import datetime, timedelta
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import permissions

from apps.users.models import User, ComputingPowerLog
from apps.avatars.models import DigitalAvatar
from apps.videos.models import VideoProduction
from apps.voices.models import VoiceModel
from apps.copywriting.models import Copywriting
from apps.packages.models import Package
from .models import SystemStatistics, DailyStatistics


@login_required
def dashboard_view(request):
    """控制台主页"""
    return render(request, 'dashboard/dashboard.html')


class DashboardStatsAPI(APIView):
    """控制台统计数据API"""
    permission_classes = [permissions.IsAdminUser]
    
    def get(self, request):
        """获取控制台统计数据"""
        # 计算时间范围
        now = timezone.now()
        today = now.date()
        yesterday = today - timedelta(days=1)
        week_ago = today - timedelta(days=7)
        month_ago = today - timedelta(days=30)
        
        # 用户统计
        total_users = User.objects.count()
        active_users = User.objects.filter(last_login__date__gte=week_ago).count()
        new_users_today = User.objects.filter(date_joined__date=today).count()
        new_users_yesterday = User.objects.filter(date_joined__date=yesterday).count()
        
        # 数字人统计
        total_avatars = DigitalAvatar.objects.count()
        public_avatars = DigitalAvatar.objects.filter(is_public=True).count()
        private_avatars = DigitalAvatar.objects.filter(is_public=False).count()
        
        # 视频统计
        total_videos = VideoProduction.objects.count()
        videos_today = VideoProduction.objects.filter(created_at__date=today).count()
        videos_completed = VideoProduction.objects.filter(status='completed').count()
        
        # 语音统计
        total_voices = VoiceModel.objects.count()
        public_voices = VoiceModel.objects.filter(is_public=True).count()
        
        # 文案统计
        total_copywriting = Copywriting.objects.count()
        copywriting_today = Copywriting.objects.filter(created_at__date=today).count()
        
        # 算力统计
        total_computing_consumed = ComputingPowerLog.objects.filter(
            operation_type='consume'
        ).aggregate(total=Sum('amount'))['total'] or 0
        
        computing_today = ComputingPowerLog.objects.filter(
            operation_type='consume',
            created_at__date=today
        ).aggregate(total=Sum('amount'))['total'] or 0
        
        # 套餐统计
        total_packages = Package.objects.count()
        active_packages = Package.objects.filter(is_active=True).count()
        
        # 计算增长率
        def calculate_growth_rate(today_value, yesterday_value):
            if yesterday_value == 0:
                return 100 if today_value > 0 else 0
            return round(((today_value - yesterday_value) / yesterday_value) * 100, 2)
        
        stats = {
            'overview': {
                'total_users': total_users,
                'active_users': active_users,
                'total_avatars': total_avatars,
                'total_videos': total_videos,
                'total_computing_consumed': total_computing_consumed,
                'total_packages': total_packages,
            },
            'today': {
                'new_users': new_users_today,
                'videos_created': videos_today,
                'copywriting_created': copywriting_today,
                'computing_consumed': computing_today,
            },
            'growth': {
                'user_growth': calculate_growth_rate(new_users_today, new_users_yesterday),
            },
            'breakdown': {
                'avatars': {
                    'public': public_avatars,
                    'private': private_avatars,
                },
                'voices': {
                    'total': total_voices,
                    'public': public_voices,
                },
                'videos': {
                    'total': total_videos,
                    'completed': videos_completed,
                    'completion_rate': round((videos_completed / total_videos * 100), 2) if total_videos > 0 else 0,
                },
                'packages': {
                    'total': total_packages,
                    'active': active_packages,
                }
            }
        }
        
        return Response(stats)


class UserGrowthAPI(APIView):
    """用户增长统计API"""
    permission_classes = [permissions.IsAdminUser]
    
    def get(self, request):
        """获取用户增长数据"""
        days = int(request.GET.get('days', 30))
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=days)
        
        # 按日期统计新增用户
        daily_users = []
        current_date = start_date
        
        while current_date <= end_date:
            new_users = User.objects.filter(date_joined__date=current_date).count()
            daily_users.append({
                'date': current_date.strftime('%Y-%m-%d'),
                'new_users': new_users,
                'total_users': User.objects.filter(date_joined__date__lte=current_date).count()
            })
            current_date += timedelta(days=1)
        
        return Response({
            'period': f'{days}天',
            'data': daily_users
        })


class RevenueStatsAPI(APIView):
    """收入统计API"""
    permission_classes = [permissions.IsAdminUser]
    
    def get(self, request):
        """获取收入统计数据"""
        # 这里应该从实际的订单/支付表中获取数据
        # 暂时返回模拟数据
        
        today = timezone.now().date()
        month_ago = today - timedelta(days=30)
        
        # 模拟收入数据
        daily_revenue = []
        current_date = month_ago
        
        while current_date <= today:
            # 这里应该查询实际的收入数据
            revenue = 0  # 暂时设为0
            daily_revenue.append({
                'date': current_date.strftime('%Y-%m-%d'),
                'revenue': revenue
            })
            current_date += timedelta(days=1)
        
        total_revenue = sum(item['revenue'] for item in daily_revenue)
        
        return Response({
            'total_revenue': total_revenue,
            'daily_revenue': daily_revenue,
            'currency': 'CNY'
        })


class UsageAnalyticsAPI(APIView):
    """使用情况分析API"""
    permission_classes = [permissions.IsAdminUser]
    
    def get(self, request):
        """获取使用情况分析数据"""
        # 最受欢迎的数字人
        popular_avatars = DigitalAvatar.objects.filter(
            usage_count__gt=0
        ).order_by('-usage_count')[:10].values('name', 'usage_count')
        
        # 最受欢迎的语音
        popular_voices = VoiceModel.objects.filter(
            usage_count__gt=0
        ).order_by('-usage_count')[:10].values('name', 'usage_count')
        
        # 用户活跃度分析
        week_ago = timezone.now() - timedelta(days=7)
        active_users_week = User.objects.filter(last_login__gte=week_ago).count()
        total_users = User.objects.count()
        activity_rate = round((active_users_week / total_users * 100), 2) if total_users > 0 else 0
        
        # 功能使用统计
        feature_usage = {
            'video_production': VideoProduction.objects.count(),
            'voice_synthesis': VoiceModel.objects.aggregate(total=Sum('usage_count'))['total'] or 0,
            'copywriting': Copywriting.objects.count(),
            'avatar_usage': DigitalAvatar.objects.aggregate(total=Sum('usage_count'))['total'] or 0,
        }
        
        return Response({
            'popular_avatars': list(popular_avatars),
            'popular_voices': list(popular_voices),
            'user_activity': {
                'active_users_week': active_users_week,
                'total_users': total_users,
                'activity_rate': activity_rate
            },
            'feature_usage': feature_usage
        })


class SystemHealthAPI(APIView):
    """系统健康状态API"""
    permission_classes = [permissions.IsAdminUser]
    
    def get(self, request):
        """获取系统健康状态"""
        from .models import ServiceStatus, SystemMonitor
        
        # 服务状态
        services = ServiceStatus.objects.all().values(
            'service_name', 'status', 'response_time', 'uptime_percentage', 'last_check'
        )
        
        # 最新的系统监控数据
        latest_monitors = {}
        for monitor_type in ['cpu', 'memory', 'disk']:
            latest = SystemMonitor.objects.filter(
                monitor_type=monitor_type
            ).order_by('-created_at').first()
            
            if latest:
                latest_monitors[monitor_type] = {
                    'value': latest.value,
                    'unit': latest.unit,
                    'status': latest.status,
                    'created_at': latest.created_at
                }
        
        # 计算整体健康分数
        online_services = len([s for s in services if s['status'] == 'online'])
        total_services = len(services)
        health_score = round((online_services / total_services * 100), 2) if total_services > 0 else 0
        
        return Response({
            'health_score': health_score,
            'services': list(services),
            'system_monitors': latest_monitors,
            'status': 'healthy' if health_score >= 80 else 'warning' if health_score >= 60 else 'critical'
        })
