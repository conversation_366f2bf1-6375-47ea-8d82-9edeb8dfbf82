"""
异步任务定义
"""
import time
import logging
from django.core.mail import send_mail
from django.conf import settings
from django.utils import timezone
from .executor import task, task_registry

logger = logging.getLogger(__name__)


@task('send_email')
def send_email_task(to_email, subject, message, from_email=None):
    """发送邮件任务"""
    try:
        from_email = from_email or settings.DEFAULT_FROM_EMAIL
        
        send_mail(
            subject=subject,
            message=message,
            from_email=from_email,
            recipient_list=[to_email],
            fail_silently=False
        )
        
        logger.info(f"邮件发送成功: {to_email}")
        return {'success': True, 'to_email': to_email}
        
    except Exception as e:
        logger.error(f"邮件发送失败: {str(e)}")
        raise


@task('generate_video')
def generate_video_task(video_id, user_id, avatar_id, voice_id, script):
    """生成视频任务"""
    try:
        logger.info(f"开始生成视频: {video_id}")
        
        # 模拟视频生成过程
        steps = [
            "准备数字人模型",
            "加载语音模型", 
            "生成语音文件",
            "渲染数字人动画",
            "合成最终视频",
            "上传到存储服务"
        ]
        
        for i, step in enumerate(steps):
            logger.info(f"视频生成进度: {step}")
            
            # 更新进度
            from .models import TaskQueue
            task = TaskQueue.objects.filter(
                task_function='generate_video',
                kwargs__video_id=video_id
            ).first()
            
            if task:
                progress = int((i + 1) / len(steps) * 100)
                task.update_progress(progress, step)
            
            # 模拟处理时间
            time.sleep(2)
        
        # 更新视频状态
        from apps.videos.models import VideoProduction
        video = VideoProduction.objects.get(id=video_id)
        video.status = 'completed'
        video.completed_at = timezone.now()
        video.save()
        
        logger.info(f"视频生成完成: {video_id}")
        return {
            'success': True,
            'video_id': video_id,
            'video_url': f'/media/videos/{video_id}.mp4'
        }
        
    except Exception as e:
        logger.error(f"视频生成失败: {str(e)}")
        
        # 更新视频状态为失败
        try:
            from apps.videos.models import VideoProduction
            video = VideoProduction.objects.get(id=video_id)
            video.status = 'failed'
            video.error_message = str(e)
            video.save()
        except:
            pass
        
        raise


@task('clone_voice')
def clone_voice_task(voice_id, user_id, audio_file_path, voice_name):
    """克隆语音任务"""
    try:
        logger.info(f"开始克隆语音: {voice_id}")
        
        # 模拟语音克隆过程
        steps = [
            "分析音频文件",
            "提取语音特征",
            "训练语音模型",
            "验证模型质量",
            "保存模型文件"
        ]
        
        for i, step in enumerate(steps):
            logger.info(f"语音克隆进度: {step}")
            
            # 更新进度
            from .models import TaskQueue
            task = TaskQueue.objects.filter(
                task_function='clone_voice',
                kwargs__voice_id=voice_id
            ).first()
            
            if task:
                progress = int((i + 1) / len(steps) * 100)
                task.update_progress(progress, step)
            
            # 模拟处理时间
            time.sleep(3)
        
        # 更新语音模型状态
        from apps.voices.models import VoiceModel
        voice = VoiceModel.objects.get(id=voice_id)
        voice.status = 'completed'
        voice.model_path = f'/models/voices/{voice_id}.pth'
        voice.save()
        
        logger.info(f"语音克隆完成: {voice_id}")
        return {
            'success': True,
            'voice_id': voice_id,
            'model_path': voice.model_path
        }
        
    except Exception as e:
        logger.error(f"语音克隆失败: {str(e)}")
        
        # 更新语音模型状态为失败
        try:
            from apps.voices.models import VoiceModel
            voice = VoiceModel.objects.get(id=voice_id)
            voice.status = 'failed'
            voice.error_message = str(e)
            voice.save()
        except:
            pass
        
        raise


@task('generate_copywriting')
def generate_copywriting_task(copywriting_id, user_id, prompt, style):
    """生成文案任务"""
    try:
        logger.info(f"开始生成文案: {copywriting_id}")
        
        # 模拟AI文案生成
        time.sleep(5)
        
        # 生成示例文案
        generated_content = f"""
        基于您的需求"{prompt}"，我们为您生成了以下{style}风格的文案：
        
        这是一个精心制作的文案内容，结合了您的要求和{style}的特点。
        内容既符合您的品牌调性，又能够有效传达核心信息。
        
        希望这个文案能够满足您的需求！
        """
        
        # 更新文案状态
        from apps.copywriting.models import Copywriting
        copywriting = Copywriting.objects.get(id=copywriting_id)
        copywriting.content = generated_content.strip()
        copywriting.status = 'completed'
        copywriting.completed_at = timezone.now()
        copywriting.save()
        
        logger.info(f"文案生成完成: {copywriting_id}")
        return {
            'success': True,
            'copywriting_id': copywriting_id,
            'content': generated_content.strip()
        }
        
    except Exception as e:
        logger.error(f"文案生成失败: {str(e)}")
        
        # 更新文案状态为失败
        try:
            from apps.copywriting.models import Copywriting
            copywriting = Copywriting.objects.get(id=copywriting_id)
            copywriting.status = 'failed'
            copywriting.error_message = str(e)
            copywriting.save()
        except:
            pass
        
        raise


@task('backup_database')
def backup_database_task():
    """数据库备份任务"""
    try:
        logger.info("开始数据库备份")
        
        import subprocess
        import os
        from datetime import datetime
        
        # 生成备份文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_file = f"backup_{timestamp}.sql"
        backup_path = os.path.join(settings.MEDIA_ROOT, 'backups', backup_file)
        
        # 确保备份目录存在
        os.makedirs(os.path.dirname(backup_path), exist_ok=True)
        
        # 执行备份命令（这里使用SQLite示例）
        db_path = settings.DATABASES['default']['NAME']
        
        # 对于SQLite，直接复制文件
        import shutil
        shutil.copy2(db_path, backup_path)
        
        logger.info(f"数据库备份完成: {backup_path}")
        return {
            'success': True,
            'backup_file': backup_file,
            'backup_path': backup_path,
            'file_size': os.path.getsize(backup_path)
        }
        
    except Exception as e:
        logger.error(f"数据库备份失败: {str(e)}")
        raise


@task('cleanup_temp_files')
def cleanup_temp_files_task():
    """清理临时文件任务"""
    try:
        logger.info("开始清理临时文件")
        
        import os
        import glob
        from datetime import datetime, timedelta
        
        # 清理7天前的临时文件
        cutoff_time = datetime.now() - timedelta(days=7)
        temp_dirs = [
            os.path.join(settings.MEDIA_ROOT, 'temp'),
            os.path.join(settings.MEDIA_ROOT, 'uploads', 'temp'),
        ]
        
        total_deleted = 0
        total_size = 0
        
        for temp_dir in temp_dirs:
            if not os.path.exists(temp_dir):
                continue
            
            for file_path in glob.glob(os.path.join(temp_dir, '*')):
                try:
                    file_time = datetime.fromtimestamp(os.path.getmtime(file_path))
                    if file_time < cutoff_time:
                        file_size = os.path.getsize(file_path)
                        os.remove(file_path)
                        total_deleted += 1
                        total_size += file_size
                        logger.debug(f"删除临时文件: {file_path}")
                except Exception as e:
                    logger.warning(f"删除文件失败: {file_path} - {str(e)}")
        
        logger.info(f"临时文件清理完成: 删除 {total_deleted} 个文件，释放 {total_size} 字节")
        return {
            'success': True,
            'deleted_files': total_deleted,
            'freed_space': total_size
        }
        
    except Exception as e:
        logger.error(f"清理临时文件失败: {str(e)}")
        raise


@task('send_notification')
def send_notification_task(user_id, title, message, notification_type='info'):
    """发送通知任务"""
    try:
        logger.info(f"发送通知给用户: {user_id}")
        
        from apps.notifications.models import Notification
        from django.contrib.auth import get_user_model
        
        User = get_user_model()
        user = User.objects.get(id=user_id)
        
        # 创建通知
        notification = Notification.objects.create(
            user=user,
            title=title,
            message=message,
            type=notification_type
        )
        
        # 如果用户在线，通过WebSocket发送实时通知
        from apps.notifications.utils import send_realtime_notification
        send_realtime_notification(user, {
            'id': notification.id,
            'title': title,
            'message': message,
            'type': notification_type,
            'created_at': notification.created_at.isoformat()
        })
        
        logger.info(f"通知发送成功: {notification.id}")
        return {
            'success': True,
            'notification_id': notification.id,
            'user_id': user_id
        }
        
    except Exception as e:
        logger.error(f"发送通知失败: {str(e)}")
        raise


@task('clone_avatar')
def clone_avatar_task(task_id, name, video_file_id, description, user_id):
    """形象克隆任务 - 百度曦灵API集成"""
    try:
        logger.info(f"开始形象克隆任务: {task_id}")

        # 获取任务记录
        from .models import TaskQueue
        task = TaskQueue.objects.get(id=task_id)

        # 更新任务状态为进行中
        task.status = 'running'
        task.save()

        # 导入百度曦灵服务
        from apps.videos.services.digital_human_service import digital_human_service
        xiling_service = digital_human_service.baidu_xiling

        # 检查百度曦灵服务是否可用
        if not xiling_service.is_available():
            raise Exception("百度曦灵服务不可用，请检查API密钥配置")

        # 步骤1: 检查视频文件
        task.update_progress(10, "正在检查视频文件...")
        logger.info("检查视频文件")

        # 这里应该从数据库或文件系统获取实际的视频文件路径
        # 暂时使用占位符，实际应该根据video_file_id获取文件路径
        video_file_path = f"/tmp/video_{video_file_id}.mp4"  # 占位符

        # 步骤2: 上传视频到百度曦灵
        task.update_progress(25, "正在上传视频文件...")
        logger.info("上传视频文件到百度曦灵")

        # 上传视频文件用于2D小样本数字人制作
        upload_result = xiling_service.upload_file(
            file_path=video_file_path,
            provider_type="2D小样本数字人制作"
        )

        if not upload_result.get('success'):
            raise Exception(f"上传视频文件失败: {upload_result.get('error')}")

        template_video_id = upload_result.get('file_id')
        logger.info(f"视频文件上传成功，文件ID: {template_video_id}")

        # 步骤3: 创建2D小样本数字人
        task.update_progress(45, "正在创建数字人形象...")
        logger.info("创建百度曦灵2D数字人")

        # 根据描述推断性别，默认为男性
        gender = "male"
        if description and any(word in description.lower() for word in ['女', 'female', '小姐姐', '美女']):
            gender = "female"

        create_result = xiling_service.create_2d_avatar(
            name=name,
            gender=gender,
            template_video_id=template_video_id
        )

        if not create_result.get('success'):
            raise Exception(f"创建百度曦灵数字人失败: {create_result.get('error')}")

        figure_id = create_result.get('figure_id')
        logger.info(f"数字人创建任务提交成功，Figure ID: {figure_id}")

        # 步骤4: 等待数字人训练完成
        task.update_progress(65, "正在训练数字人模型...")
        logger.info(f"等待百度曦灵数字人训练完成: {figure_id}")

        # 轮询训练状态
        max_wait_time = 600  # 最多等待10分钟
        wait_time = 0

        import time
        while wait_time < max_wait_time:
            status_result = xiling_service.get_avatar_status(figure_id)

            if status_result.get('success'):
                status = status_result.get('status')

                if status == 'SUCCESS':
                    logger.info("百度曦灵数字人训练完成")
                    break
                elif status == 'FAILED':
                    error_msg = status_result.get('failed_message', '训练失败')
                    raise Exception(f"百度曦灵数字人训练失败: {error_msg}")
                elif status in ['LINE_UP', 'GENERATING']:
                    # 继续等待
                    time.sleep(15)  # 每15秒查询一次
                    wait_time += 15
                    progress = 65 + (wait_time / max_wait_time) * 25
                    status_msg = "排队中..." if status == 'LINE_UP' else "训练中..."
                    task.update_progress(progress, f"正在{status_msg} ({wait_time}s)")
                else:
                    # 未知状态，继续等待
                    time.sleep(15)
                    wait_time += 15
            else:
                logger.warning(f"获取训练状态失败: {status_result.get('error')}")
                time.sleep(15)
                wait_time += 15

        if wait_time >= max_wait_time:
            raise Exception("数字人训练超时，请稍后在管理后台查看结果")

        # 步骤5: 保存结果
        task.update_progress(95, "正在保存数字人信息...")

        logger.info("保存数字人形象记录")

        # 创建数字人形象记录
        from apps.avatars.models import DigitalAvatar
        from django.contrib.auth import get_user_model

        User = get_user_model()
        user = User.objects.get(id=user_id)

        avatar = DigitalAvatar.objects.create(
            name=name,
            description=description,
            model_version='百度曦灵2D数字人',
            scene_type='自定义',
            is_public=False,
            owner=user,
            computing_power_cost=15,  # 百度曦灵成本更低
            # 保存百度曦灵相关信息
            video_url=f"baidu_xiling://figure/{figure_id}",  # 使用特殊格式标识百度曦灵头像
            thumbnail=f"baidu_xiling://figure/{figure_id}/thumbnail"
        )

        # 步骤6: 完成
        task.update_progress(100, "形象克隆完成！")

        # 更新任务状态为完成
        task.status = 'completed'
        task.result = {
            'avatar_id': avatar.id,
            'avatar_name': avatar.name,
            'baidu_figure_id': figure_id,
            'template_video_id': template_video_id,
            'message': '🎉 形象克隆成功完成！您的专属数字人已准备就绪，可以用于视频生成。'
        }
        task.save()

        logger.info(f"形象克隆任务完成: {task_id}, 百度曦灵 Figure ID: {figure_id}")

        return {
            'success': True,
            'message': '形象克隆完成',
            'avatar_id': avatar.id,
            'baidu_figure_id': figure_id,
            'task_id': task_id
        }

    except Exception as e:
        logger.error(f"形象克隆失败: {e}")

        # 更新任务状态为失败
        try:
            task = TaskQueue.objects.get(id=task_id)
            task.status = 'failed'
            task.error_message = str(e)
            task.save()
        except:
            pass

        return {
            'success': False,
            'error': str(e)
        }


# 注册所有任务
@task('generate_baidu_video')
def generate_baidu_video_task(task_id, figure_id, text, voice_id, user_id):
    """百度曦灵视频生成任务"""
    try:
        logger.info(f"开始百度曦灵视频生成任务: {task_id}")

        # 获取任务记录
        from .models import TaskQueue
        task = TaskQueue.objects.get(id=task_id)

        # 更新任务状态为进行中
        task.status = 'running'
        task.save()

        # 导入百度曦灵服务
        from apps.videos.services.digital_human_service import digital_human_service
        xiling_service = digital_human_service.baidu_xiling

        # 检查百度曦灵服务是否可用
        if not xiling_service.is_available():
            raise Exception("百度曦灵服务不可用，请检查API密钥配置")

        # 步骤1: 提交视频生成任务
        task.update_progress(20, "正在提交视频生成任务...")
        logger.info("提交百度曦灵视频生成任务")

        generate_result = xiling_service.generate_video(
            figure_id=figure_id,
            text=text,
            voice_id=voice_id
        )

        if not generate_result.get('success'):
            raise Exception(f"提交视频生成任务失败: {generate_result.get('error')}")

        video_task_id = generate_result.get('task_id')
        logger.info(f"视频生成任务提交成功，任务ID: {video_task_id}")

        # 步骤2: 等待视频生成完成
        task.update_progress(40, "正在生成数字人视频...")
        logger.info(f"等待百度曦灵视频生成完成: {video_task_id}")

        # 轮询视频生成状态
        max_wait_time = 300  # 最多等待5分钟
        wait_time = 0

        import time
        while wait_time < max_wait_time:
            status_result = xiling_service.get_video_status(video_task_id)

            if status_result.get('success'):
                status = status_result.get('status')

                if status == 'SUCCESS':
                    video_url = status_result.get('video_url')
                    logger.info(f"百度曦灵视频生成完成: {video_url}")

                    # 步骤3: 保存视频记录
                    task.update_progress(90, "正在保存视频...")

                    from apps.videos.models import VideoGeneration
                    from django.contrib.auth import get_user_model

                    User = get_user_model()
                    user = User.objects.get(id=user_id)

                    video = VideoGeneration.objects.create(
                        title=f"数字人视频_{task_id}",
                        script=text,
                        avatar_id=figure_id,
                        voice_id=voice_id,
                        video_url=video_url,
                        status='completed',
                        user=user,
                        provider='baidu_xiling',
                        computing_power_cost=5  # 百度曦灵视频生成成本
                    )

                    # 完成任务
                    task.update_progress(100, "视频生成完成！")
                    task.status = 'completed'
                    task.result = {
                        'video_id': video.id,
                        'video_url': video_url,
                        'baidu_task_id': video_task_id,
                        'message': '🎉 数字人视频生成成功！'
                    }
                    task.save()

                    logger.info(f"视频生成任务完成: {task_id}")

                    return {
                        'success': True,
                        'message': '视频生成完成',
                        'video_id': video.id,
                        'video_url': video_url
                    }

                elif status == 'FAILED':
                    error_msg = status_result.get('failed_message', '视频生成失败')
                    raise Exception(f"百度曦灵视频生成失败: {error_msg}")

                elif status in ['SUBMIT', 'GENERATING']:
                    # 继续等待
                    time.sleep(10)  # 每10秒查询一次
                    wait_time += 10
                    progress = 40 + (wait_time / max_wait_time) * 45
                    status_msg = "已提交" if status == 'SUBMIT' else "生成中"
                    task.update_progress(progress, f"视频{status_msg}... ({wait_time}s)")
                else:
                    # 未知状态，继续等待
                    time.sleep(10)
                    wait_time += 10
            else:
                logger.warning(f"获取视频生成状态失败: {status_result.get('error')}")
                time.sleep(10)
                wait_time += 10

        if wait_time >= max_wait_time:
            raise Exception("视频生成超时，请稍后查看结果")

    except Exception as e:
        logger.error(f"百度曦灵视频生成失败: {e}")

        # 更新任务状态为失败
        try:
            task = TaskQueue.objects.get(id=task_id)
            task.status = 'failed'
            task.error_message = str(e)
            task.save()
        except:
            pass

        return {
            'success': False,
            'error': str(e)
        }


def register_all_tasks():
    """注册所有任务函数"""
    tasks = [
        send_email_task,
        generate_video_task,
        clone_voice_task,
        generate_copywriting_task,
        backup_database_task,
        cleanup_temp_files_task,
        send_notification_task,
        clone_avatar_task,
        generate_baidu_video_task,
    ]

    for task_func in tasks:
        if hasattr(task_func, '__name__'):
            task_registry.register(task_func.__name__, task_func)

    logger.info(f"已注册 {len(tasks)} 个任务函数")


# 自动注册任务
register_all_tasks()
