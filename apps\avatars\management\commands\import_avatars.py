"""
批量导入数字人形象管理命令
"""
import csv
import json
from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from apps.avatars.models import DigitalAvatar, AvatarCategory, AvatarTag


class Command(BaseCommand):
    help = '批量导入数字人形象数据'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--file',
            type=str,
            required=True,
            help='CSV文件路径'
        )
        parser.add_argument(
            '--format',
            type=str,
            choices=['csv', 'json'],
            default='csv',
            help='文件格式 (csv 或 json)'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='仅验证数据，不实际导入'
        )
    
    def handle(self, *args, **options):
        file_path = options['file']
        file_format = options['format']
        dry_run = options['dry_run']
        
        try:
            if file_format == 'csv':
                self.import_from_csv(file_path, dry_run)
            elif file_format == 'json':
                self.import_from_json(file_path, dry_run)
        except Exception as e:
            raise CommandError(f'导入失败: {str(e)}')
    
    def import_from_csv(self, file_path, dry_run=False):
        """从CSV文件导入"""
        self.stdout.write('开始从CSV文件导入数字人形象...')
        
        imported_count = 0
        error_count = 0
        
        with open(file_path, 'r', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)
            
            # 验证必需的列
            required_fields = ['name', 'description', 'model_version']
            missing_fields = [field for field in required_fields if field not in reader.fieldnames]
            if missing_fields:
                raise CommandError(f'CSV文件缺少必需的列: {", ".join(missing_fields)}')
            
            with transaction.atomic():
                for row_num, row in enumerate(reader, start=2):
                    try:
                        if dry_run:
                            self.validate_row(row)
                        else:
                            self.create_avatar_from_row(row)
                        imported_count += 1
                    except Exception as e:
                        error_count += 1
                        self.stdout.write(
                            self.style.ERROR(f'第{row_num}行导入失败: {str(e)}')
                        )
                
                if dry_run:
                    self.stdout.write(
                        self.style.SUCCESS(f'验证完成: {imported_count} 行有效, {error_count} 行有错误')
                    )
                    # 回滚事务
                    transaction.set_rollback(True)
                else:
                    self.stdout.write(
                        self.style.SUCCESS(f'导入完成: {imported_count} 个数字人形象, {error_count} 个失败')
                    )
    
    def import_from_json(self, file_path, dry_run=False):
        """从JSON文件导入"""
        self.stdout.write('开始从JSON文件导入数字人形象...')
        
        with open(file_path, 'r', encoding='utf-8') as jsonfile:
            data = json.load(jsonfile)
        
        if not isinstance(data, list):
            raise CommandError('JSON文件应包含数字人形象数组')
        
        imported_count = 0
        error_count = 0
        
        with transaction.atomic():
            for index, item in enumerate(data):
                try:
                    if dry_run:
                        self.validate_json_item(item)
                    else:
                        self.create_avatar_from_json(item)
                    imported_count += 1
                except Exception as e:
                    error_count += 1
                    self.stdout.write(
                        self.style.ERROR(f'第{index+1}项导入失败: {str(e)}')
                    )
            
            if dry_run:
                self.stdout.write(
                    self.style.SUCCESS(f'验证完成: {imported_count} 项有效, {error_count} 项有错误')
                )
                transaction.set_rollback(True)
            else:
                self.stdout.write(
                    self.style.SUCCESS(f'导入完成: {imported_count} 个数字人形象, {error_count} 个失败')
                )
    
    def validate_row(self, row):
        """验证CSV行数据"""
        required_fields = ['name', 'description', 'model_version']
        for field in required_fields:
            if not row.get(field, '').strip():
                raise ValueError(f'必需字段 {field} 不能为空')
        
        # 验证数值字段
        if row.get('computing_power_cost'):
            try:
                int(row['computing_power_cost'])
            except ValueError:
                raise ValueError('computing_power_cost 必须是整数')
        
        if row.get('sort_order'):
            try:
                int(row['sort_order'])
            except ValueError:
                raise ValueError('sort_order 必须是整数')
    
    def validate_json_item(self, item):
        """验证JSON项数据"""
        required_fields = ['name', 'description', 'model_version']
        for field in required_fields:
            if not item.get(field, '').strip():
                raise ValueError(f'必需字段 {field} 不能为空')
    
    def create_avatar_from_row(self, row):
        """从CSV行创建数字人形象"""
        # 检查是否已存在
        if DigitalAvatar.objects.filter(name=row['name']).exists():
            raise ValueError(f'数字人形象 "{row["name"]}" 已存在')
        
        # 创建数字人形象
        avatar = DigitalAvatar.objects.create(
            name=row['name'].strip(),
            description=row['description'].strip(),
            model_version=row['model_version'].strip(),
            scene_type=row.get('scene_type', '').strip() or 'general',
            computing_power_cost=int(row.get('computing_power_cost', 10)),
            sort_order=int(row.get('sort_order', 0)),
            is_public=row.get('is_public', '').lower() in ['true', '1', 'yes'],
            is_active=row.get('is_active', '').lower() in ['true', '1', 'yes'],
            thumbnail=row.get('thumbnail', '').strip(),
            video_preview=row.get('video_preview', '').strip(),
            video_url=row.get('video_url', '').strip(),
        )
        
        # 处理分类
        if row.get('categories'):
            category_names = [name.strip() for name in row['categories'].split(',')]
            for category_name in category_names:
                if category_name:
                    category, _ = AvatarCategory.objects.get_or_create(
                        name=category_name,
                        defaults={'description': f'{category_name}分类'}
                    )
                    avatar.categories.add(category)
        
        # 处理标签
        if row.get('tags'):
            tag_names = [name.strip() for name in row['tags'].split(',')]
            for tag_name in tag_names:
                if tag_name:
                    tag, _ = AvatarTag.objects.get_or_create(
                        name=tag_name,
                        defaults={'color': '#007bff'}
                    )
                    avatar.tags.add(tag)
        
        return avatar
    
    def create_avatar_from_json(self, item):
        """从JSON项创建数字人形象"""
        # 检查是否已存在
        if DigitalAvatar.objects.filter(name=item['name']).exists():
            raise ValueError(f'数字人形象 "{item["name"]}" 已存在')
        
        # 创建数字人形象
        avatar = DigitalAvatar.objects.create(
            name=item['name'].strip(),
            description=item['description'].strip(),
            model_version=item['model_version'].strip(),
            scene_type=item.get('scene_type', 'general'),
            computing_power_cost=item.get('computing_power_cost', 10),
            sort_order=item.get('sort_order', 0),
            is_public=item.get('is_public', False),
            is_active=item.get('is_active', True),
            thumbnail=item.get('thumbnail', ''),
            video_preview=item.get('video_preview', ''),
            video_url=item.get('video_url', ''),
        )
        
        # 处理分类
        if item.get('categories'):
            for category_name in item['categories']:
                if category_name:
                    category, _ = AvatarCategory.objects.get_or_create(
                        name=category_name,
                        defaults={'description': f'{category_name}分类'}
                    )
                    avatar.categories.add(category)
        
        # 处理标签
        if item.get('tags'):
            for tag_name in item['tags']:
                if tag_name:
                    tag, _ = AvatarTag.objects.get_or_create(
                        name=tag_name,
                        defaults={'color': '#007bff'}
                    )
                    avatar.tags.add(tag)
        
        return avatar
