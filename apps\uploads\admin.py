"""
文件上传管理后台配置
"""
from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from django.utils.html import format_html
from unfold.admin import ModelAdmin
from unfold.contrib.filters.admin import RangeDateFilter, ChoicesDropdownFilter
from .models import UploadFile, ChunkedUpload, UploadChunk


@admin.register(UploadFile)
class UploadFileAdmin(ModelAdmin):
    """上传文件管理"""
    
    list_display = (
        'original_filename', 'user', 'upload_type', 'file_size_display',
        'status', 'progress', 'created_at'
    )
    
    list_filter = (
        ('upload_type', ChoicesDropdownFilter),
        ('status', ChoicesDropdownFilter),
        ('created_at', RangeDateFilter),
    )
    
    search_fields = ('original_filename', 'user__username', 'file_type')
    
    readonly_fields = (
        'file_hash', 'file_size', 'file_type', 'created_at', 
        'updated_at', 'completed_at', 'file_preview'
    )
    
    fieldsets = (
        (_('基本信息'), {
            'fields': ('user', 'upload_type', 'original_filename', 'file')
        }),
        (_('文件信息'), {
            'fields': ('file_size', 'file_type', 'file_hash', 'file_preview')
        }),
        (_('状态'), {
            'fields': ('status', 'progress', 'error_message')
        }),
        (_('元数据'), {
            'fields': ('metadata',),
            'classes': ('collapse',)
        }),
        (_('时间信息'), {
            'fields': ('created_at', 'updated_at', 'completed_at'),
            'classes': ('collapse',)
        }),
    )
    
    ordering = ('-created_at',)
    
    def file_size_display(self, obj):
        """文件大小显示"""
        if obj.file_size:
            size_mb = obj.file_size / (1024 * 1024)
            if size_mb < 1:
                return f"{obj.file_size / 1024:.1f} KB"
            return f"{size_mb:.1f} MB"
        return "0 B"
    file_size_display.short_description = _('文件大小')
    
    def file_preview(self, obj):
        """文件预览"""
        if obj.file and obj.upload_type == 'image':
            return format_html(
                '<img src="{}" style="max-width: 200px; max-height: 200px;" />',
                obj.file.url
            )
        elif obj.file:
            return format_html(
                '<a href="{}" target="_blank">查看文件</a>',
                obj.file.url
            )
        return "无文件"
    file_preview.short_description = _('文件预览')


@admin.register(ChunkedUpload)
class ChunkedUploadAdmin(ModelAdmin):
    """分块上传管理"""
    
    list_display = (
        'filename', 'user', 'upload_type', 'total_size_display',
        'progress_display', 'is_completed', 'created_at'
    )
    
    list_filter = (
        ('upload_type', ChoicesDropdownFilter),
        'is_completed',
        ('created_at', RangeDateFilter),
    )
    
    search_fields = ('filename', 'user__username', 'upload_id')
    
    readonly_fields = (
        'upload_id', 'uploaded_size', 'created_at', 
        'updated_at', 'progress_display'
    )
    
    fieldsets = (
        (_('基本信息'), {
            'fields': ('user', 'upload_id', 'filename', 'upload_type')
        }),
        (_('上传信息'), {
            'fields': (
                'total_size', 'chunk_size', 'uploaded_size', 
                'progress_display', 'is_completed'
            )
        }),
        (_('时间信息'), {
            'fields': ('created_at', 'updated_at', 'expires_at'),
            'classes': ('collapse',)
        }),
    )
    
    ordering = ('-created_at',)
    
    def total_size_display(self, obj):
        """总大小显示"""
        size_mb = obj.total_size / (1024 * 1024)
        return f"{size_mb:.1f} MB"
    total_size_display.short_description = _('总大小')
    
    def progress_display(self, obj):
        """进度显示"""
        return f"{obj.progress_percentage}%"
    progress_display.short_description = _('进度')


@admin.register(UploadChunk)
class UploadChunkAdmin(ModelAdmin):
    """上传块管理"""
    
    list_display = (
        'chunked_upload', 'chunk_number', 'chunk_size_display', 
        'checksum', 'created_at'
    )
    
    list_filter = (
        ('created_at', RangeDateFilter),
    )
    
    search_fields = ('chunked_upload__filename', 'checksum')
    
    readonly_fields = ('chunk_data', 'created_at')
    
    ordering = ('chunked_upload', 'chunk_number')
    
    def chunk_size_display(self, obj):
        """块大小显示"""
        size_kb = obj.chunk_size / 1024
        return f"{size_kb:.1f} KB"
    chunk_size_display.short_description = _('块大小')
    
    def has_add_permission(self, request):
        return False
    
    def has_change_permission(self, request, obj=None):
        return False
