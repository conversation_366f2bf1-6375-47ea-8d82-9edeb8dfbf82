"""
AI工具管理后台配置
"""
from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from .models import AIToolUsageLog, AIServiceConfig, AIConversation, AIImageGeneration


@admin.register(AIToolUsageLog)
class AIToolUsageLogAdmin(admin.ModelAdmin):
    """AI工具使用日志管理"""
    
    list_display = [
        'user', 'tool_name', 'computing_power_consumed', 
        'is_fallback', 'response_time', 'created_at'
    ]
    list_filter = [
        'tool_name', 'is_fallback', 'created_at'
    ]
    search_fields = [
        'user__username', 'user__email'
    ]
    readonly_fields = [
        'user', 'tool_name', 'input_data', 'output_data',
        'computing_power_consumed', 'is_fallback', 'response_time',
        'error_message', 'created_at'
    ]
    ordering = ['-created_at']
    date_hierarchy = 'created_at'
    
    fieldsets = (
        ('基本信息', {
            'fields': ('user', 'tool_name', 'created_at')
        }),
        ('使用数据', {
            'fields': ('input_data', 'output_data'),
            'classes': ('collapse',)
        }),
        ('性能信息', {
            'fields': ('computing_power_consumed', 'response_time', 'is_fallback')
        }),
        ('错误信息', {
            'fields': ('error_message',),
            'classes': ('collapse',)
        }),
    )
    
    def has_add_permission(self, request):
        """禁止添加，只允许查看"""
        return False
    
    def has_change_permission(self, request, obj=None):
        """禁止修改，只允许查看"""
        return False


@admin.register(AIServiceConfig)
class AIServiceConfigAdmin(admin.ModelAdmin):
    """AI服务配置管理"""
    
    list_display = [
        'service_name', 'model_name', 'is_active', 
        'priority', 'max_tokens', 'temperature', 'updated_at'
    ]
    list_filter = [
        'service_name', 'is_active', 'created_at'
    ]
    list_editable = [
        'is_active', 'priority', 'max_tokens', 'temperature'
    ]
    search_fields = [
        'service_name', 'model_name'
    ]
    ordering = ['-priority', 'service_name']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('service_name', 'model_name', 'is_active', 'priority')
        }),
        ('API配置', {
            'fields': ('api_key', 'base_url'),
            'description': 'API密钥等敏感信息将被隐藏显示'
        }),
        ('模型参数', {
            'fields': ('max_tokens', 'temperature')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    readonly_fields = ['created_at', 'updated_at']
    
    def get_form(self, request, obj=None, **kwargs):
        """自定义表单，隐藏API密钥"""
        form = super().get_form(request, obj, **kwargs)
        if obj and obj.api_key:
            # 隐藏API密钥的显示
            form.base_fields['api_key'].help_text = f"当前密钥: {'*' * 20}...{obj.api_key[-4:]}"
        return form


@admin.register(AIConversation)
class AIConversationAdmin(admin.ModelAdmin):
    """AI对话记录管理"""
    
    list_display = [
        'user', 'title', 'conversation_id', 'total_tokens',
        'total_computing_power', 'is_active', 'updated_at'
    ]
    list_filter = [
        'is_active', 'created_at', 'updated_at'
    ]
    search_fields = [
        'user__username', 'title', 'conversation_id'
    ]
    readonly_fields = [
        'conversation_id', 'total_tokens', 'total_computing_power',
        'created_at', 'updated_at'
    ]
    ordering = ['-updated_at']
    date_hierarchy = 'updated_at'
    
    fieldsets = (
        ('基本信息', {
            'fields': ('user', 'title', 'conversation_id', 'is_active')
        }),
        ('对话内容', {
            'fields': ('messages',),
            'classes': ('collapse',)
        }),
        ('统计信息', {
            'fields': ('total_tokens', 'total_computing_power')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def get_queryset(self, request):
        """优化查询"""
        return super().get_queryset(request).select_related('user')


@admin.register(AIImageGeneration)
class AIImageGenerationAdmin(admin.ModelAdmin):
    """AI图像生成记录管理"""
    
    list_display = [
        'user', 'prompt_preview', 'style', 'size',
        'computing_power_consumed', 'is_fallback', 'created_at'
    ]
    list_filter = [
        'style', 'size', 'is_fallback', 'created_at'
    ]
    search_fields = [
        'user__username', 'prompt', 'revised_prompt'
    ]
    readonly_fields = [
        'user', 'prompt', 'revised_prompt', 'style', 'size',
        'image_url', 'local_path', 'computing_power_consumed',
        'generation_time', 'is_fallback', 'created_at'
    ]
    ordering = ['-created_at']
    date_hierarchy = 'created_at'
    
    fieldsets = (
        ('基本信息', {
            'fields': ('user', 'created_at')
        }),
        ('生成参数', {
            'fields': ('prompt', 'revised_prompt', 'style', 'size')
        }),
        ('结果信息', {
            'fields': ('image_url', 'local_path')
        }),
        ('性能信息', {
            'fields': ('computing_power_consumed', 'generation_time', 'is_fallback')
        }),
    )
    
    def prompt_preview(self, obj):
        """提示词预览"""
        if len(obj.prompt) > 50:
            return obj.prompt[:50] + '...'
        return obj.prompt
    prompt_preview.short_description = '提示词'
    
    def has_add_permission(self, request):
        """禁止添加，只允许查看"""
        return False
    
    def has_change_permission(self, request, obj=None):
        """禁止修改，只允许查看"""
        return False
    
    def get_queryset(self, request):
        """优化查询"""
        return super().get_queryset(request).select_related('user')


# 自定义管理后台标题
admin.site.site_header = 'AI数字人SaaS平台管理后台'
admin.site.site_title = 'AI数字人管理'
admin.site.index_title = '欢迎使用AI数字人SaaS平台管理后台'
