"""
语音系统模型
基于对https://hm.umi6.com/平台的逆向工程分析
"""
from django.db import models
from django.conf import settings
from django.utils.translation import gettext_lazy as _


class VoiceCategory(models.Model):
    """语音分类"""

    name = models.CharField(_('分类名称'), max_length=50, unique=True)
    description = models.TextField(_('分类描述'), blank=True)
    icon = models.CharField(_('图标'), max_length=50, blank=True, help_text='Material Icons图标名称')
    sort_order = models.IntegerField(_('排序'), default=0)
    is_active = models.BooleanField(_('是否启用'), default=True)
    created_at = models.DateTimeField(_('创建时间'), auto_now_add=True)

    class Meta:
        verbose_name = _('语音分类')
        verbose_name_plural = _('语音分类')
        ordering = ['sort_order', 'name']

    def __str__(self):
        return self.name


class VoiceTag(models.Model):
    """语音标签"""

    name = models.CharField(_('标签名称'), max_length=30, unique=True)
    color = models.CharField(_('标签颜色'), max_length=7, default='#007bff', help_text='十六进制颜色值')
    usage_count = models.IntegerField(_('使用次数'), default=0)
    created_at = models.DateTimeField(_('创建时间'), auto_now_add=True)

    class Meta:
        verbose_name = _('语音标签')
        verbose_name_plural = _('语音标签')
        ordering = ['-usage_count', 'name']

    def __str__(self):
        return self.name

    def increment_usage(self):
        """增加使用次数"""
        self.usage_count += 1
        self.save(update_fields=['usage_count'])


class VoiceModel(models.Model):
    """语音模型"""
    
    LANGUAGE_CHOICES = [
        ('zh-cn', '中文（简体）'),
        ('zh-tw', '中文（繁体）'),
        ('en-us', '英语（美式）'),
        ('en-gb', '英语（英式）'),
        ('ja-jp', '日语'),
        ('ko-kr', '韩语'),
        ('es-es', '西班牙语'),
        ('pt-br', '葡萄牙语'),
        ('id-id', '印尼语'),
        ('yue', '粤语'),
        ('wu', '上海话'),
        ('sichuan', '四川话'),
        ('hunan', '湖南话'),
        ('henan', '河南话'),
        ('tianjin', '天津话'),
        ('hubei', '湖北话'),
        ('dongbei', '东北话'),
        ('guangxi', '广西话'),
        ('chongqing', '重庆话'),
    ]
    
    GENDER_CHOICES = [
        ('male', '男声'),
        ('female', '女声'),
        ('child', '童声'),
    ]
    
    VOICE_TYPE_CHOICES = [
        ('standard', '标准'),
        ('news', '新闻播报'),
        ('magnetic', '磁性'),
        ('sweet', '甜美'),
        ('lively', '活泼'),
        ('gentle', '温柔'),
        ('intellectual', '知性'),
        ('cute', '可爱'),
        ('dramatic', '戏剧'),
        ('audiobook', '有声阅读'),
        ('education', '教育'),
        ('customer_service', '客服'),
        ('dialect', '方言'),
    ]
    
    name = models.CharField(
        _('语音名称'),
        max_length=100,
        help_text=_('语音模型的名称')
    )
    
    description = models.TextField(
        _('语音描述'),
        blank=True,
        help_text=_('语音模型的详细描述')
    )
    
    language = models.CharField(
        _('语言'),
        max_length=20,
        choices=LANGUAGE_CHOICES,
        help_text=_('语音模型支持的语言')
    )
    
    gender = models.CharField(
        _('性别'),
        max_length=10,
        choices=GENDER_CHOICES,
        help_text=_('语音的性别特征')
    )
    
    voice_type = models.CharField(
        _('语音类型'),
        max_length=30,
        choices=VOICE_TYPE_CHOICES,
        default='standard',
        help_text=_('语音的风格类型')
    )
    
    audio_sample = models.FileField(
        _('音频样本'),
        upload_to='voices/samples/',
        null=True,
        blank=True,
        help_text=_('语音模型的音频样本文件')
    )
    
    audio_sample_url = models.URLField(
        _('音频样本链接'),
        null=True,
        blank=True,
        help_text=_('语音模型的在线音频样本链接')
    )
    
    is_cloned = models.BooleanField(
        _('是否为克隆语音'),
        default=False,
        help_text=_('是否为用户克隆的语音')
    )
    
    is_public = models.BooleanField(
        _('是否公开'),
        default=True,
        help_text=_('是否为公共语音模型，所有用户可用')
    )
    
    is_active = models.BooleanField(
        _('是否启用'),
        default=True,
        help_text=_('是否启用此语音模型')
    )
    
    owner = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='owned_voices',
        null=True,
        blank=True,
        verbose_name=_('所有者'),
        help_text=_('私有语音模型的所有者，公共语音此字段为空')
    )
    
    computing_power_cost = models.PositiveIntegerField(
        _('算力消耗'),
        default=5,
        help_text=_('使用此语音模型合成语音所需的算力点数（每分钟）')
    )
    
    sort_order = models.PositiveIntegerField(
        _('排序'),
        default=0,
        help_text=_('显示排序，数字越小越靠前')
    )
    
    usage_count = models.PositiveIntegerField(
        _('使用次数'),
        default=0,
        help_text=_('此语音模型被使用的总次数')
    )
    
    # 语音参数配置
    default_volume = models.FloatField(
        _('默认音量'),
        default=50.0,
        help_text=_('默认音量大小（0-100）')
    )
    
    default_pitch = models.FloatField(
        _('默认音调'),
        default=1.0,
        help_text=_('默认音调高低（0-2）')
    )
    
    default_speed = models.FloatField(
        _('默认语速'),
        default=1.0,
        help_text=_('默认语速快慢（0-2）')
    )

    # 分类和标签
    categories = models.ManyToManyField(
        VoiceCategory,
        related_name='voices',
        blank=True,
        verbose_name=_('分类'),
        help_text=_('语音模型所属的分类')
    )

    tags = models.ManyToManyField(
        VoiceTag,
        related_name='voices',
        blank=True,
        verbose_name=_('标签'),
        help_text=_('语音模型的标签')
    )

    created_at = models.DateTimeField(
        _('创建时间'),
        auto_now_add=True
    )

    updated_at = models.DateTimeField(
        _('更新时间'),
        auto_now=True
    )

    class Meta:
        verbose_name = _('语音模型')
        verbose_name_plural = _('语音模型')
        db_table = 'voice_models'
        ordering = ['sort_order', '-created_at']
        indexes = [
            models.Index(fields=['language', 'gender']),
            models.Index(fields=['is_public', 'is_active']),
            models.Index(fields=['voice_type']),
            models.Index(fields=['owner']),
        ]

    def __str__(self):
        return f"{self.name} ({self.get_language_display()} {self.get_gender_display()})"

    def increment_usage(self):
        """增加使用次数"""
        self.usage_count += 1
        self.save(update_fields=['usage_count'])

    @property
    def display_info(self):
        """获取语音显示信息"""
        return f"{self.get_language_display()} {self.get_gender_display()}"


class VoiceCloneRequest(models.Model):
    """语音克隆请求"""
    
    STATUS_CHOICES = [
        ('pending', '待处理'),
        ('processing', '处理中'),
        ('completed', '已完成'),
        ('failed', '失败'),
        ('cancelled', '已取消'),
    ]
    
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='voice_clone_requests',
        verbose_name=_('用户')
    )
    
    name = models.CharField(
        _('语音名称'),
        max_length=100,
        help_text=_('克隆语音的名称')
    )
    
    description = models.TextField(
        _('语音描述'),
        blank=True,
        help_text=_('克隆语音的描述')
    )
    
    source_audio = models.FileField(
        _('源音频文件'),
        upload_to='voices/clone_sources/',
        help_text=_('用于克隆的源音频文件')
    )
    
    audio_duration = models.PositiveIntegerField(
        _('音频时长'),
        null=True,
        blank=True,
        help_text=_('源音频文件的时长（秒）')
    )
    
    status = models.CharField(
        _('处理状态'),
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending',
        help_text=_('语音克隆的处理状态')
    )
    
    progress = models.PositiveIntegerField(
        _('处理进度'),
        default=0,
        help_text=_('处理进度百分比（0-100）')
    )
    
    error_message = models.TextField(
        _('错误信息'),
        blank=True,
        help_text=_('处理失败时的错误信息')
    )
    
    result_voice = models.ForeignKey(
        VoiceModel,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='clone_requests',
        verbose_name=_('生成的语音模型'),
        help_text=_('克隆成功后生成的语音模型')
    )
    
    computing_power_consumed = models.PositiveIntegerField(
        _('消耗算力'),
        default=0,
        help_text=_('本次克隆消耗的算力点数')
    )
    
    created_at = models.DateTimeField(
        _('创建时间'),
        auto_now_add=True
    )
    
    updated_at = models.DateTimeField(
        _('更新时间'),
        auto_now=True
    )
    
    completed_at = models.DateTimeField(
        _('完成时间'),
        null=True,
        blank=True,
        help_text=_('克隆完成的时间')
    )

    class Meta:
        verbose_name = _('语音克隆请求')
        verbose_name_plural = _('语音克隆请求')
        db_table = 'voice_clone_requests'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'status']),
            models.Index(fields=['status', 'created_at']),
        ]

    def __str__(self):
        return f"{self.user.display_name} - {self.name} ({self.get_status_display()})"


class VoiceSynthesisTask(models.Model):
    """语音合成任务"""
    
    STATUS_CHOICES = [
        ('pending', '待处理'),
        ('processing', '处理中'),
        ('completed', '已完成'),
        ('failed', '失败'),
        ('cancelled', '已取消'),
    ]
    
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='voice_synthesis_tasks',
        verbose_name=_('用户')
    )
    
    voice_model = models.ForeignKey(
        VoiceModel,
        on_delete=models.CASCADE,
        related_name='synthesis_tasks',
        verbose_name=_('语音模型')
    )
    
    text_content = models.TextField(
        _('文本内容'),
        help_text=_('需要合成语音的文本内容')
    )
    
    # 语音参数
    volume = models.FloatField(
        _('音量'),
        default=50.0,
        help_text=_('音量大小（0-100）')
    )
    
    pitch = models.FloatField(
        _('音调'),
        default=1.0,
        help_text=_('音调高低（0-2）')
    )
    
    speed = models.FloatField(
        _('语速'),
        default=1.0,
        help_text=_('语速快慢（0-2）')
    )
    
    status = models.CharField(
        _('处理状态'),
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending',
        help_text=_('语音合成的处理状态')
    )
    
    progress = models.PositiveIntegerField(
        _('处理进度'),
        default=0,
        help_text=_('处理进度百分比（0-100）')
    )
    
    error_message = models.TextField(
        _('错误信息'),
        blank=True,
        help_text=_('处理失败时的错误信息')
    )
    
    result_audio = models.FileField(
        _('合成音频'),
        upload_to='voices/synthesis_results/',
        null=True,
        blank=True,
        help_text=_('合成生成的音频文件')
    )
    
    result_audio_url = models.URLField(
        _('合成音频链接'),
        null=True,
        blank=True,
        help_text=_('合成音频的在线链接')
    )
    
    audio_duration = models.PositiveIntegerField(
        _('音频时长'),
        null=True,
        blank=True,
        help_text=_('合成音频的时长（秒）')
    )
    
    computing_power_consumed = models.PositiveIntegerField(
        _('消耗算力'),
        default=0,
        help_text=_('本次合成消耗的算力点数')
    )
    
    created_at = models.DateTimeField(
        _('创建时间'),
        auto_now_add=True
    )
    
    updated_at = models.DateTimeField(
        _('更新时间'),
        auto_now=True
    )
    
    completed_at = models.DateTimeField(
        _('完成时间'),
        null=True,
        blank=True,
        help_text=_('合成完成的时间')
    )

    class Meta:
        verbose_name = _('语音合成任务')
        verbose_name_plural = _('语音合成任务')
        db_table = 'voice_synthesis_tasks'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'status']),
            models.Index(fields=['voice_model', 'status']),
            models.Index(fields=['status', 'created_at']),
        ]

    def __str__(self):
        return f"{self.user.display_name} - {self.voice_model.name} ({self.get_status_display()})"
