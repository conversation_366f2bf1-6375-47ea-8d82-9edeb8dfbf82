from django.core.management.base import BaseCommand
from ai_tools.models import ToolCategory, AITool


class Command(BaseCommand):
    help = '填充AI工具示例数据'

    def handle(self, *args, **options):
        self.stdout.write('开始填充AI工具数据...')

        # 创建工具分类
        categories_data = [
            {
                'name': '全部工具',
                'key': 'all',
                'icon': 'MagicStick',
                'color': '#667eea',
                'sort_order': 0
            },
            {
                'name': '文本处理',
                'key': 'text',
                'icon': 'ChatDotRound',
                'color': '#10b981',
                'sort_order': 1
            },
            {
                'name': '图像生成',
                'key': 'image',
                'icon': 'PictureFilled',
                'color': '#f59e0b',
                'sort_order': 2
            },
            {
                'name': '音频处理',
                'key': 'audio',
                'icon': 'Microphone',
                'color': '#8b5cf6',
                'sort_order': 3
            },
            {
                'name': '视频工具',
                'key': 'video',
                'icon': 'VideoCamera',
                'color': '#ef4444',
                'sort_order': 4
            }
        ]

        for cat_data in categories_data:
            category, created = ToolCategory.objects.get_or_create(
                key=cat_data['key'],
                defaults=cat_data
            )
            if created:
                self.stdout.write(f'创建分类: {category.name}')

        # 获取分类对象
        text_cat = ToolCategory.objects.get(key='text')
        image_cat = ToolCategory.objects.get(key='image')
        audio_cat = ToolCategory.objects.get(key='audio')
        video_cat = ToolCategory.objects.get(key='video')

        # 创建AI工具
        tools_data = [
            {
                'name': 'AI智能对话',
                'description': '与AI进行自然对话，获得智能回答和建议',
                'icon': 'ChatDotRound',
                'gradient': 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                'category': text_cat,
                'tags': ['对话', '问答', '助手'],
                'usage_count': 1250,
                'rating': 4.8,
                'status': 'available',
                'is_new': False,
                'is_pro': False,
                'sort_order': 1,
                'route_path': '/ai-tools/chat',
                'api_endpoint': '/api/v1/ai-tools/chat/'
            },
            {
                'name': 'AI图像生成',
                'description': '根据文字描述生成高质量的AI图像',
                'icon': 'PictureFilled',
                'gradient': 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
                'category': image_cat,
                'tags': ['图像', '生成', '创意'],
                'usage_count': 890,
                'rating': 4.7,
                'status': 'available',
                'is_new': True,
                'is_pro': False,
                'sort_order': 2,
                'route_path': '/ai-tools/image-generator',
                'api_endpoint': '/api/v1/ai-tools/image-generator/'
            },
            {
                'name': 'AI音乐创作',
                'description': '智能创作各种风格的背景音乐',
                'icon': 'Microphone',
                'gradient': 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
                'category': audio_cat,
                'tags': ['音乐', '创作', '背景'],
                'usage_count': 456,
                'rating': 4.6,
                'status': 'available',
                'is_new': False,
                'is_pro': True,
                'sort_order': 3,
                'route_path': '/ai-tools/music-creator',
                'api_endpoint': '/api/v1/ai-tools/music-creator/'
            },
            {
                'name': '智能翻译',
                'description': '多语言智能翻译，支持100+种语言',
                'icon': 'ChatDotRound',
                'gradient': 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
                'category': text_cat,
                'tags': ['翻译', '多语言', '国际化'],
                'usage_count': 2340,
                'rating': 4.9,
                'status': 'coming_soon',
                'is_new': False,
                'is_pro': False,
                'sort_order': 4,
                'route_path': '/ai-tools/translator',
                'api_endpoint': '/api/v1/ai-tools/translator/'
            },
            {
                'name': '视频剪辑助手',
                'description': 'AI辅助视频剪辑和特效处理',
                'icon': 'VideoCamera',
                'gradient': 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
                'category': video_cat,
                'tags': ['视频', '剪辑', '特效'],
                'usage_count': 678,
                'rating': 4.5,
                'status': 'coming_soon',
                'is_new': True,
                'is_pro': True,
                'sort_order': 5,
                'route_path': '/ai-tools/video-editor',
                'api_endpoint': '/api/v1/ai-tools/video-editor/'
            },
            {
                'name': '语音识别',
                'description': '高精度语音转文字，支持多种方言',
                'icon': 'Microphone',
                'gradient': 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',
                'category': audio_cat,
                'tags': ['语音', '识别', '转录'],
                'usage_count': 1120,
                'rating': 4.4,
                'status': 'available',
                'is_new': False,
                'is_pro': False,
                'sort_order': 6,
                'route_path': '/ai-tools/speech-recognition',
                'api_endpoint': '/api/v1/ai-tools/speech-recognition/'
            }
        ]

        for tool_data in tools_data:
            tool, created = AITool.objects.get_or_create(
                name=tool_data['name'],
                defaults=tool_data
            )
            if created:
                self.stdout.write(f'创建工具: {tool.name}')

        self.stdout.write(self.style.SUCCESS('AI工具数据填充完成！'))
