from django.db import models
from django.utils.translation import gettext_lazy as _
from django.contrib.auth import get_user_model

User = get_user_model()


class TimeStampedModel(models.Model):
    """带时间戳的抽象模型"""
    created_at = models.DateTimeField(_('创建时间'), auto_now_add=True)
    updated_at = models.DateTimeField(_('更新时间'), auto_now=True)
    
    class Meta:
        abstract = True


class SystemLog(models.Model):
    """系统日志模型"""
    
    LEVEL_CHOICES = [
        ('DEBUG', _('调试')),
        ('INFO', _('信息')),
        ('WARNING', _('警告')),
        ('ERROR', _('错误')),
        ('CRITICAL', _('严重错误')),
    ]
    
    # 基本信息
    level = models.CharField(_('日志级别'), max_length=20, choices=LEVEL_CHOICES)
    logger = models.CharField(_('日志记录器'), max_length=100)
    message = models.TextField(_('日志消息'))
    
    # 代码位置
    module = models.CharField(_('模块'), max_length=100, blank=True)
    function = models.CharField(_('函数'), max_length=100, blank=True)
    line_number = models.IntegerField(_('行号'), null=True, blank=True)
    
    # 请求信息
    request_id = models.CharField(_('请求ID'), max_length=100, blank=True)
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, verbose_name=_('用户'))
    ip_address = models.GenericIPAddressField(_('IP地址'), null=True, blank=True)
    user_agent = models.TextField(_('用户代理'), blank=True)
    url = models.URLField(_('请求URL'), blank=True)
    method = models.CharField(_('请求方法'), max_length=10, blank=True)
    status_code = models.IntegerField(_('状态码'), null=True, blank=True)
    
    # 异常信息
    exception_type = models.CharField(_('异常类型'), max_length=100, blank=True)
    exception_message = models.TextField(_('异常消息'), blank=True)
    traceback = models.TextField(_('异常堆栈'), blank=True)
    
    # 额外数据
    extra_data = models.JSONField(_('额外数据'), default=dict, blank=True)
    
    # 时间
    timestamp = models.DateTimeField(_('时间戳'), auto_now_add=True)
    
    class Meta:
        verbose_name = _('系统日志')
        verbose_name_plural = _('系统日志')
        ordering = ['-timestamp']
        indexes = [
            models.Index(fields=['level', 'timestamp']),
            models.Index(fields=['logger', 'timestamp']),
            models.Index(fields=['user', 'timestamp']),
            models.Index(fields=['request_id']),
        ]
    
    def __str__(self):
        return f'{self.level} - {self.message[:50]}'


class UserActionLog(models.Model):
    """用户操作日志"""
    
    ACTION_CHOICES = [
        ('login', _('登录')),
        ('logout', _('登出')),
        ('register', _('注册')),
        ('profile_update', _('更新资料')),
        ('password_change', _('修改密码')),
        ('avatar_create', _('创建数字人')),
        ('avatar_update', _('更新数字人')),
        ('avatar_delete', _('删除数字人')),
        ('voice_create', _('创建语音')),
        ('voice_update', _('更新语音')),
        ('voice_delete', _('删除语音')),
        ('video_create', _('创建视频')),
        ('video_update', _('更新视频')),
        ('video_delete', _('删除视频')),
        ('package_purchase', _('购买套餐')),
        ('recharge', _('充值')),
        ('other', _('其他')),
    ]
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name=_('用户'))
    action = models.CharField(_('操作类型'), max_length=50, choices=ACTION_CHOICES)
    description = models.TextField(_('操作描述'))
    
    # 请求信息
    ip_address = models.GenericIPAddressField(_('IP地址'), null=True, blank=True)
    user_agent = models.TextField(_('用户代理'), blank=True)
    
    # 操作对象
    object_type = models.CharField(_('对象类型'), max_length=50, blank=True)
    object_id = models.CharField(_('对象ID'), max_length=100, blank=True)
    
    # 额外数据
    extra_data = models.JSONField(_('额外数据'), default=dict, blank=True)
    
    # 时间
    timestamp = models.DateTimeField(_('时间戳'), auto_now_add=True)
    
    class Meta:
        verbose_name = _('用户操作日志')
        verbose_name_plural = _('用户操作日志')
        ordering = ['-timestamp']
        indexes = [
            models.Index(fields=['user', 'timestamp']),
            models.Index(fields=['action', 'timestamp']),
            models.Index(fields=['object_type', 'object_id']),
        ]
    
    def __str__(self):
        return f'{self.user.username} - {self.get_action_display()}'


class SecurityLog(models.Model):
    """安全日志"""
    
    EVENT_CHOICES = [
        ('login_success', _('登录成功')),
        ('login_failure', _('登录失败')),
        ('permission_denied', _('权限拒绝')),
        ('suspicious_activity', _('可疑活动')),
        ('rate_limit_exceeded', _('频率限制超出')),
        ('invalid_token', _('无效令牌')),
        ('sql_injection_attempt', _('SQL注入尝试')),
        ('xss_attempt', _('XSS攻击尝试')),
        ('csrf_failure', _('CSRF验证失败')),
        ('file_upload_blocked', _('文件上传被阻止')),
        ('other', _('其他')),
    ]
    
    SEVERITY_CHOICES = [
        ('low', _('低')),
        ('medium', _('中')),
        ('high', _('高')),
        ('critical', _('严重')),
    ]
    
    event_type = models.CharField(_('事件类型'), max_length=50, choices=EVENT_CHOICES)
    severity = models.CharField(_('严重程度'), max_length=20, choices=SEVERITY_CHOICES, default='medium')
    description = models.TextField(_('事件描述'))
    
    # 用户信息
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, verbose_name=_('用户'))
    username = models.CharField(_('用户名'), max_length=150, blank=True)
    
    # 请求信息
    ip_address = models.GenericIPAddressField(_('IP地址'), null=True, blank=True)
    user_agent = models.TextField(_('用户代理'), blank=True)
    url = models.URLField(_('请求URL'), blank=True)
    method = models.CharField(_('请求方法'), max_length=10, blank=True)
    
    # 额外数据
    extra_data = models.JSONField(_('额外数据'), default=dict, blank=True)
    
    # 处理状态
    is_resolved = models.BooleanField(_('已处理'), default=False)
    resolved_by = models.ForeignKey(
        User, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True, 
        related_name='resolved_security_logs',
        verbose_name=_('处理人')
    )
    resolved_at = models.DateTimeField(_('处理时间'), null=True, blank=True)
    resolution_notes = models.TextField(_('处理说明'), blank=True)
    
    # 时间
    timestamp = models.DateTimeField(_('时间戳'), auto_now_add=True)
    
    class Meta:
        verbose_name = _('安全日志')
        verbose_name_plural = _('安全日志')
        ordering = ['-timestamp']
        indexes = [
            models.Index(fields=['event_type', 'timestamp']),
            models.Index(fields=['severity', 'timestamp']),
            models.Index(fields=['user', 'timestamp']),
            models.Index(fields=['ip_address', 'timestamp']),
            models.Index(fields=['is_resolved']),
        ]
    
    def __str__(self):
        return f'{self.get_event_type_display()} - {self.description[:50]}'
