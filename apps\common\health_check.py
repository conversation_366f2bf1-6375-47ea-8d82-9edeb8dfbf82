"""
系统健康检查
"""
import time
import logging
from django.db import connection
from django.core.cache import cache
from django.conf import settings
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from .performance_monitor import PerformanceMonitor, GlobalPerformanceStats
from apps.config.services import api_config

logger = logging.getLogger(__name__)


class SystemHealthCheck:
    """系统健康检查"""
    
    @staticmethod
    def check_database():
        """检查数据库连接"""
        try:
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                return {'status': 'healthy', 'response_time': 0.001}
        except Exception as e:
            return {'status': 'unhealthy', 'error': str(e)}
    
    @staticmethod
    def check_cache():
        """检查缓存系统"""
        try:
            test_key = 'health_check_test'
            test_value = str(time.time())
            
            cache.set(test_key, test_value, 10)
            cached_value = cache.get(test_key)
            cache.delete(test_key)
            
            if cached_value == test_value:
                return {'status': 'healthy'}
            else:
                return {'status': 'unhealthy', 'error': 'Cache value mismatch'}
        except Exception as e:
            return {'status': 'unhealthy', 'error': str(e)}
    
    @staticmethod
    def check_third_party_apis():
        """检查第三方API配置"""
        try:
            checks = {}
            
            # 检查OpenAI配置
            openai_config = api_config.get_openai_config()
            checks['openai'] = {
                'configured': bool(openai_config.get('api_key')),
                'base_url': openai_config.get('base_url', 'Not configured')
            }
            
            # 检查Azure语音配置
            azure_config = api_config.get_azure_speech_config()
            checks['azure_speech'] = {
                'configured': bool(azure_config.get('api_key')),
                'region': azure_config.get('region', 'Not configured')
            }
            
            # 检查ElevenLabs配置
            elevenlabs_config = api_config.get_elevenlabs_config()
            checks['elevenlabs'] = {
                'configured': bool(elevenlabs_config.get('api_key'))
            }
            
            return {'status': 'healthy', 'apis': checks}
        except Exception as e:
            return {'status': 'unhealthy', 'error': str(e)}
    
    @staticmethod
    def check_system_resources():
        """检查系统资源"""
        try:
            system_info = PerformanceMonitor.get_system_info()
            
            # 健康阈值
            cpu_threshold = 80
            memory_threshold = 85
            disk_threshold = 90
            
            warnings = []
            if system_info.get('cpu_percent', 0) > cpu_threshold:
                warnings.append(f"High CPU usage: {system_info['cpu_percent']}%")
            
            if system_info.get('memory_percent', 0) > memory_threshold:
                warnings.append(f"High memory usage: {system_info['memory_percent']}%")
            
            if system_info.get('disk_usage', 0) > disk_threshold:
                warnings.append(f"High disk usage: {system_info['disk_usage']}%")
            
            return {
                'status': 'healthy' if not warnings else 'warning',
                'system_info': system_info,
                'warnings': warnings
            }
        except Exception as e:
            return {'status': 'unhealthy', 'error': str(e)}
    
    @classmethod
    def full_health_check(cls):
        """完整健康检查"""
        start_time = time.time()
        
        checks = {
            'database': cls.check_database(),
            'cache': cls.check_cache(),
            'third_party_apis': cls.check_third_party_apis(),
            'system_resources': cls.check_system_resources(),
        }
        
        # 整体状态评估
        overall_status = 'healthy'
        for check_name, check_result in checks.items():
            if check_result['status'] == 'unhealthy':
                overall_status = 'unhealthy'
                break
            elif check_result['status'] == 'warning' and overall_status == 'healthy':
                overall_status = 'warning'
        
        # 性能统计
        performance_stats = GlobalPerformanceStats.get_stats()
        
        end_time = time.time()
        
        return {
            'overall_status': overall_status,
            'timestamp': time.time(),
            'check_duration': round(end_time - start_time, 4),
            'checks': checks,
            'performance_stats': performance_stats,
            'version': getattr(settings, 'VERSION', '1.0.0'),
            'environment': getattr(settings, 'ENVIRONMENT', 'development'),
        }


class HealthCheckView(APIView):
    """健康检查API视图"""
    
    permission_classes = []  # 公开访问
    
    def get(self, request):
        """获取系统健康状态"""
        try:
            health_data = SystemHealthCheck.full_health_check()
            
            # 根据健康状态返回相应的HTTP状态码
            if health_data['overall_status'] == 'healthy':
                return Response(health_data, status=status.HTTP_200_OK)
            elif health_data['overall_status'] == 'warning':
                return Response(health_data, status=status.HTTP_200_OK)
            else:
                return Response(health_data, status=status.HTTP_503_SERVICE_UNAVAILABLE)
                
        except Exception as e:
            logger.error(f"健康检查失败: {e}")
            return Response({
                'overall_status': 'unhealthy',
                'error': str(e),
                'timestamp': time.time()
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class QuickHealthCheckView(APIView):
    """快速健康检查API视图"""
    
    permission_classes = []  # 公开访问
    
    def get(self, request):
        """快速健康检查（仅检查数据库）"""
        try:
            db_check = SystemHealthCheck.check_database()
            
            if db_check['status'] == 'healthy':
                return Response({
                    'status': 'healthy',
                    'timestamp': time.time(),
                    'database': db_check
                }, status=status.HTTP_200_OK)
            else:
                return Response({
                    'status': 'unhealthy',
                    'timestamp': time.time(),
                    'database': db_check
                }, status=status.HTTP_503_SERVICE_UNAVAILABLE)
                
        except Exception as e:
            logger.error(f"快速健康检查失败: {e}")
            return Response({
                'status': 'unhealthy',
                'error': str(e),
                'timestamp': time.time()
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class SystemStatsView(APIView):
    """系统统计API视图"""
    
    permission_classes = []  # 可以根据需要添加权限
    
    def get(self, request):
        """获取系统统计信息"""
        try:
            stats = {
                'performance': GlobalPerformanceStats.get_stats(),
                'system': PerformanceMonitor.get_system_info(),
                'database': PerformanceMonitor.get_database_info(),
                'timestamp': time.time()
            }
            
            return Response(stats, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"获取系统统计失败: {e}")
            return Response({
                'error': str(e),
                'timestamp': time.time()
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def delete(self, request):
        """重置性能统计"""
        try:
            GlobalPerformanceStats.reset_stats()
            return Response({
                'message': '性能统计已重置',
                'timestamp': time.time()
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"重置性能统计失败: {e}")
            return Response({
                'error': str(e),
                'timestamp': time.time()
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
