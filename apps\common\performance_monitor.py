"""
性能监控工具
"""
import time
import logging
from functools import wraps
from django.db import connection
from django.conf import settings
from typing import Dict, Any
import psutil
import os

logger = logging.getLogger(__name__)


class PerformanceMonitor:
    """性能监控器"""
    
    @staticmethod
    def get_system_info() -> Dict[str, Any]:
        """获取系统信息"""
        try:
            return {
                'cpu_percent': psutil.cpu_percent(interval=1),
                'memory_percent': psutil.virtual_memory().percent,
                'disk_usage': psutil.disk_usage('/').percent,
                'process_count': len(psutil.pids()),
                'load_average': os.getloadavg() if hasattr(os, 'getloadavg') else None,
            }
        except Exception as e:
            logger.error(f"获取系统信息失败: {e}")
            return {}
    
    @staticmethod
    def get_database_info() -> Dict[str, Any]:
        """获取数据库信息"""
        try:
            db_queries = len(connection.queries)
            return {
                'query_count': db_queries,
                'queries': connection.queries[-5:] if db_queries > 0 else [],  # 最近5个查询
            }
        except Exception as e:
            logger.error(f"获取数据库信息失败: {e}")
            return {}
    
    @staticmethod
    def log_performance(func_name: str, duration: float, **kwargs):
        """记录性能日志"""
        log_data = {
            'function': func_name,
            'duration': round(duration, 4),
            'timestamp': time.time(),
            **kwargs
        }
        
        # 根据执行时间选择日志级别
        if duration > 5.0:
            logger.warning(f"慢查询警告: {log_data}")
        elif duration > 2.0:
            logger.info(f"性能监控: {log_data}")
        else:
            logger.debug(f"性能监控: {log_data}")


def monitor_performance(include_system=False, include_db=False):
    """性能监控装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            start_queries = len(connection.queries) if include_db else 0
            
            try:
                result = func(*args, **kwargs)
                success = True
                error = None
            except Exception as e:
                result = None
                success = False
                error = str(e)
                raise
            finally:
                end_time = time.time()
                duration = end_time - start_time
                
                # 收集监控数据
                monitor_data = {
                    'success': success,
                    'error': error,
                }
                
                if include_db:
                    end_queries = len(connection.queries)
                    monitor_data['db_queries'] = end_queries - start_queries
                
                if include_system:
                    monitor_data['system_info'] = PerformanceMonitor.get_system_info()
                
                # 记录性能日志
                PerformanceMonitor.log_performance(
                    func.__name__, 
                    duration, 
                    **monitor_data
                )
            
            return result
        return wrapper
    return decorator


def monitor_api_performance(view_func):
    """API性能监控装饰器"""
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        start_time = time.time()
        start_queries = len(connection.queries)
        
        try:
            response = view_func(request, *args, **kwargs)
            success = True
            status_code = getattr(response, 'status_code', 200)
            error = None
        except Exception as e:
            response = None
            success = False
            status_code = 500
            error = str(e)
            raise
        finally:
            end_time = time.time()
            duration = end_time - start_time
            end_queries = len(connection.queries)
            
            # API性能日志
            api_log = {
                'method': request.method,
                'path': request.path,
                'user': str(request.user) if hasattr(request, 'user') else 'Anonymous',
                'duration': round(duration, 4),
                'db_queries': end_queries - start_queries,
                'status_code': status_code,
                'success': success,
                'error': error,
                'timestamp': time.time(),
            }
            
            # 根据性能选择日志级别
            if duration > 3.0 or not success:
                logger.warning(f"API性能警告: {api_log}")
            elif duration > 1.0:
                logger.info(f"API性能监控: {api_log}")
            else:
                logger.debug(f"API性能监控: {api_log}")
        
        return response
    return wrapper


class DatabaseQueryMonitor:
    """数据库查询监控"""
    
    @staticmethod
    def analyze_queries():
        """分析数据库查询"""
        queries = connection.queries
        if not queries:
            return {}
        
        total_time = sum(float(q['time']) for q in queries)
        slow_queries = [q for q in queries if float(q['time']) > 0.1]
        
        return {
            'total_queries': len(queries),
            'total_time': round(total_time, 4),
            'slow_queries': len(slow_queries),
            'average_time': round(total_time / len(queries), 4),
            'slowest_query': max(queries, key=lambda q: float(q['time'])) if queries else None,
        }
    
    @staticmethod
    def log_query_analysis():
        """记录查询分析"""
        analysis = DatabaseQueryMonitor.analyze_queries()
        if analysis:
            logger.info(f"数据库查询分析: {analysis}")


def log_slow_queries(threshold=0.1):
    """记录慢查询装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_queries = len(connection.queries)
            
            result = func(*args, **kwargs)
            
            end_queries = len(connection.queries)
            new_queries = connection.queries[start_queries:end_queries]
            
            slow_queries = [q for q in new_queries if float(q['time']) > threshold]
            if slow_queries:
                logger.warning(f"慢查询检测 - 函数: {func.__name__}, 慢查询数: {len(slow_queries)}")
                for query in slow_queries:
                    logger.warning(f"慢查询: {query['time']}s - {query['sql'][:200]}...")
            
            return result
        return wrapper
    return decorator


# 全局性能统计
class GlobalPerformanceStats:
    """全局性能统计"""
    
    _stats = {
        'api_calls': 0,
        'total_response_time': 0.0,
        'error_count': 0,
        'slow_requests': 0,
    }
    
    @classmethod
    def record_api_call(cls, duration: float, success: bool):
        """记录API调用"""
        cls._stats['api_calls'] += 1
        cls._stats['total_response_time'] += duration
        
        if not success:
            cls._stats['error_count'] += 1
        
        if duration > 2.0:
            cls._stats['slow_requests'] += 1
    
    @classmethod
    def get_stats(cls) -> Dict[str, Any]:
        """获取统计信息"""
        stats = cls._stats.copy()
        if stats['api_calls'] > 0:
            stats['average_response_time'] = round(
                stats['total_response_time'] / stats['api_calls'], 4
            )
            stats['error_rate'] = round(
                stats['error_count'] / stats['api_calls'] * 100, 2
            )
            stats['slow_request_rate'] = round(
                stats['slow_requests'] / stats['api_calls'] * 100, 2
            )
        
        return stats
    
    @classmethod
    def reset_stats(cls):
        """重置统计"""
        cls._stats = {
            'api_calls': 0,
            'total_response_time': 0.0,
            'error_count': 0,
            'slow_requests': 0,
        }
