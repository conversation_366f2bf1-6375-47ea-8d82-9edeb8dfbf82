# Generated by Django 5.2.1 on 2025-07-16 13:01

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="SystemConfig",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "key",
                    models.CharField(max_length=100, unique=True, verbose_name="配置键"),
                ),
                ("name", models.CharField(max_length=200, verbose_name="配置名称")),
                ("description", models.TextField(blank=True, verbose_name="配置描述")),
                (
                    "config_type",
                    models.CharField(
                        choices=[
                            ("general", "通用配置"),
                            ("computing", "算力配置"),
                            ("payment", "支付配置"),
                            ("ai_service", "AI服务配置"),
                            ("storage", "存储配置"),
                            ("notification", "通知配置"),
                            ("security", "安全配置"),
                            ("feature", "功能开关"),
                        ],
                        max_length=20,
                        verbose_name="配置类型",
                    ),
                ),
                ("value", models.TextField(verbose_name="配置值")),
                (
                    "value_type",
                    models.CharField(
                        choices=[
                            ("string", "字符串"),
                            ("integer", "整数"),
                            ("float", "浮点数"),
                            ("boolean", "布尔值"),
                            ("json", "JSON对象"),
                            ("text", "长文本"),
                        ],
                        default="string",
                        max_length=10,
                        verbose_name="值类型",
                    ),
                ),
                ("default_value", models.TextField(blank=True, verbose_name="默认值")),
                (
                    "validation_rule",
                    models.TextField(
                        blank=True, help_text="JSON格式的验证规则", verbose_name="验证规则"
                    ),
                ),
                ("is_active", models.BooleanField(default=True, verbose_name="启用")),
                (
                    "is_sensitive",
                    models.BooleanField(
                        default=False, help_text="敏感信息在界面上会被隐藏", verbose_name="敏感信息"
                    ),
                ),
                ("is_readonly", models.BooleanField(default=False, verbose_name="只读")),
                ("sort_order", models.IntegerField(default=0, verbose_name="排序")),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
            ],
            options={
                "verbose_name": "系统配置",
                "verbose_name_plural": "系统配置",
                "ordering": ["config_type", "sort_order", "key"],
            },
        ),
        migrations.CreateModel(
            name="ConfigHistory",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("old_value", models.TextField(blank=True, verbose_name="原值")),
                ("new_value", models.TextField(verbose_name="新值")),
                (
                    "change_reason",
                    models.CharField(blank=True, max_length=200, verbose_name="变更原因"),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="变更时间"),
                ),
                (
                    "changed_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="操作者",
                    ),
                ),
                (
                    "config",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="history",
                        to="config.systemconfig",
                        verbose_name="配置项",
                    ),
                ),
            ],
            options={
                "verbose_name": "配置变更历史",
                "verbose_name_plural": "配置变更历史",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="ConfigGroup",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100, verbose_name="分组名称")),
                ("description", models.TextField(blank=True, verbose_name="分组描述")),
                (
                    "icon",
                    models.CharField(blank=True, max_length=50, verbose_name="图标"),
                ),
                ("sort_order", models.IntegerField(default=0, verbose_name="排序")),
                ("is_active", models.BooleanField(default=True, verbose_name="启用")),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "configs",
                    models.ManyToManyField(
                        blank=True, to="config.systemconfig", verbose_name="配置项"
                    ),
                ),
            ],
            options={
                "verbose_name": "配置分组",
                "verbose_name_plural": "配置分组",
                "ordering": ["sort_order", "name"],
            },
        ),
    ]
