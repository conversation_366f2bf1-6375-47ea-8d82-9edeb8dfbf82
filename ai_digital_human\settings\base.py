"""
Django settings for AI数字人SaaS平台
基于对https://hm.umi6.com/平台的逆向工程分析

Base settings shared across all environments.
"""

import os
from pathlib import Path
try:
    from decouple import config
except ImportError:
    # 如果decouple未安装，使用环境变量
    import os
    def config(key, default=None, cast=None):
        value = os.environ.get(key, default)
        if cast and value is not None:
            return cast(value)
        return value

from django.templatetags.static import static
from django.urls import reverse_lazy

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent.parent

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = config('SECRET_KEY', default='django-insecure-change-me-in-production')

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = config('DEBUG', default=True, cast=bool)

# Application definition
DJANGO_APPS = [
    'unfold',  # Django Unfold admin theme
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
]

THIRD_PARTY_APPS = [
    'rest_framework',
    'corsheaders',
    'django_filters',
]

LOCAL_APPS = [
    'apps.dashboard',  # 控制台应用
    'apps.users',
    'apps.avatars',
    'apps.voices',
    'apps.videos',
    'apps.copywriting',
    'apps.packages',
    'apps.common',
    'apps.notifications',  # 通知系统应用
    'apps.uploads',  # 文件上传应用
    'apps.moderation',  # 内容审核应用
    'apps.config',  # 系统配置应用
    'apps.tasks',  # 异步任务应用
    'apps.ai_tools',  # AI工具应用
    'digital_works',  # 数字作品应用
]

INSTALLED_APPS = DJANGO_APPS + THIRD_PARTY_APPS + LOCAL_APPS

MIDDLEWARE = [
    # 'apps.common.middleware.error_handling.ErrorHandlingMiddleware',  # 错误处理（最前面） - 临时禁用
    # 'apps.common.middleware.error_handling.SecurityMiddleware',       # 安全检查 - 临时禁用
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.security.SecurityMiddleware',
    'whitenoise.middleware.WhiteNoiseMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.locale.LocaleMiddleware',
    'django.middleware.common.CommonMiddleware',
    'apps.common.middleware.csrf_exempt.CSRFExemptMiddleware',  # CSRF豁免中间件
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    # 'apps.common.middleware.error_handling.RequestLoggingMiddleware', # 请求日志 - 临时禁用
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    # 'apps.common.middleware.error_handling.PerformanceMiddleware',    # 性能监控（最后面） - 临时禁用
]

ROOT_URLCONF = 'ai_digital_human.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [BASE_DIR / 'templates'],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'ai_digital_human.wsgi.application'

# Database
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db.sqlite3',
    }
}

# Password validation
AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

# Internationalization
LANGUAGE_CODE = 'zh-hans'
TIME_ZONE = 'Asia/Shanghai'
USE_I18N = True
USE_TZ = True

LANGUAGES = [
    ('zh-hans', '简体中文'),
    ('en', 'English'),
]

LOCALE_PATHS = [
    BASE_DIR / 'locale',
]

# Static files (CSS, JavaScript, Images)
STATIC_URL = '/static/'
STATIC_ROOT = BASE_DIR / 'staticfiles'
STATICFILES_DIRS = [
    BASE_DIR / 'static',
]

# Media files
MEDIA_URL = '/media/'
MEDIA_ROOT = BASE_DIR / 'media'

# Default primary key field type
DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# Custom user model
AUTH_USER_MODEL = 'users.User'

# Django REST Framework
REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'rest_framework.authentication.SessionAuthentication',
        'rest_framework_simplejwt.authentication.JWTAuthentication',
    ],
    'DEFAULT_PERMISSION_CLASSES': [],
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    'PAGE_SIZE': 20,
    'DEFAULT_FILTER_BACKENDS': [
        'django_filters.rest_framework.DjangoFilterBackend',
        'rest_framework.filters.SearchFilter',
        'rest_framework.filters.OrderingFilter',
    ],
    'DEFAULT_THROTTLE_CLASSES': [
        'apps.common.throttling.AnonRateThrottle',
        'apps.common.throttling.UserRateThrottle',
        'apps.common.throttling.BurstRateThrottle',
    ],
    'DEFAULT_THROTTLE_RATES': {
        'anon': '100/hour',
        'user': '1000/hour',
        'login': '5/min',
        'register': '3/min',
        'upload': '10/min',
        'ai_generate': '20/hour',
        'burst': '10/min',
    },
    'EXCEPTION_HANDLER': 'apps.common.exceptions.custom_exception_handler',
}

# JWT配置
from datetime import timedelta

SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(hours=24),  # 24小时有效期
    'REFRESH_TOKEN_LIFETIME': timedelta(days=7),   # 7天刷新期
    'ROTATE_REFRESH_TOKENS': True,
    'BLACKLIST_AFTER_ROTATION': True,
    'UPDATE_LAST_LOGIN': True,

    'ALGORITHM': 'HS256',
    'SIGNING_KEY': SECRET_KEY,
    'VERIFYING_KEY': None,
    'AUDIENCE': None,
    'ISSUER': None,

    'AUTH_HEADER_TYPES': ('Bearer',),
    'AUTH_HEADER_NAME': 'HTTP_AUTHORIZATION',
    'USER_ID_FIELD': 'id',
    'USER_ID_CLAIM': 'user_id',

    'AUTH_TOKEN_CLASSES': ('rest_framework_simplejwt.tokens.AccessToken',),
    'TOKEN_TYPE_CLAIM': 'token_type',
    'JTI_CLAIM': 'jti',

    'SLIDING_TOKEN_REFRESH_EXP_CLAIM': 'refresh_exp',
    'SLIDING_TOKEN_LIFETIME': timedelta(hours=24),
    'SLIDING_TOKEN_REFRESH_LIFETIME': timedelta(days=7),
}

# 缓存配置
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': 'redis://127.0.0.1:6379/1',
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
            'CONNECTION_POOL_KWARGS': {
                'max_connections': 50,
                'retry_on_timeout': True,
            },
            'COMPRESSOR': 'django_redis.compressors.zlib.ZlibCompressor',
            'IGNORE_EXCEPTIONS': True,
        },
        'KEY_PREFIX': 'ai_digital_human',
        'VERSION': 1,
        'TIMEOUT': 300,  # 默认5分钟
    },
    'sessions': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': 'redis://127.0.0.1:6379/2',
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
        },
        'KEY_PREFIX': 'sessions',
        'TIMEOUT': 86400,  # 24小时
    }
}

# 会话缓存 - 临时使用数据库会话，避免Redis依赖
SESSION_ENGINE = 'django.contrib.sessions.backends.db'
# SESSION_ENGINE = 'django.contrib.sessions.backends.cache'
# SESSION_CACHE_ALIAS = 'sessions'

# CORS settings
CORS_ALLOWED_ORIGINS = [
    "http://localhost:3000",
    "http://127.0.0.1:3000",
    "http://localhost:3001",
    "http://127.0.0.1:3001",
    "http://localhost:3002",
    "http://127.0.0.1:3002",
]

CORS_ALLOW_CREDENTIALS = True
CORS_ALLOW_ALL_ORIGINS = True  # 在开发环境中设置为True
CORS_ALLOWED_HEADERS = [
    'accept',
    'accept-encoding',
    'authorization',
    'content-type',
    'dnt',
    'origin',
    'user-agent',
    'x-csrftoken',
    'x-requested-with',
]

CORS_ALLOW_METHODS = [
    'DELETE',
    'GET',
    'OPTIONS',
    'PATCH',
    'POST',
    'PUT',
]

# Django Unfold configuration
UNFOLD = {
    "SITE_TITLE": "AI数字人管理后台",
    "SITE_HEADER": "AI数字人SaaS平台",
    "SITE_URL": "/",
    "SITE_ICON": {
        "light": lambda request: static("icon-light.svg"),
        "dark": lambda request: static("icon-dark.svg"),
    },
    "SITE_LOGO": {
        "light": lambda request: static("logo-light.svg"),
        "dark": lambda request: static("logo-dark.svg"),
    },
    "SITE_SYMBOL": "smart_toy",
    "SHOW_HISTORY": True,
    "SHOW_VIEW_ON_SITE": True,
    "ENVIRONMENT": "ai_digital_human.settings.base.environment_callback",
    "DASHBOARD_CALLBACK": "ai_digital_human.settings.base.dashboard_callback",
    "LOGIN": {
        "image": lambda request: static("login-bg.svg"),
        "redirect_after": lambda request: reverse_lazy("admin:index"),
    },
    "STYLES": [
        lambda request: static("css/admin-custom.css"),
    ],
    "SCRIPTS": [
        lambda request: static("js/admin-custom.js"),
    ],
    "COLORS": {
        "primary": {
            "50": "250 245 255",
            "100": "243 232 255", 
            "200": "233 213 255",
            "300": "216 180 254",
            "400": "196 181 253",
            "500": "168 85 247",
            "600": "147 51 234",
            "700": "126 34 206",
            "800": "107 33 168",
            "900": "88 28 135",
        },
    },
    "EXTENSIONS": {
        "modeltranslation": {
            "flags": {
                "en": "🇺🇸",
                "zh-hans": "🇨🇳",
            },
        },
    },
    "SIDEBAR": {
        "show_search": True,
        "show_all_applications": True,
        "navigation": [
            {
                "title": "控制台",
                "separator": True,
                "items": [
                    {
                        "title": "控制台概览",
                        "icon": "dashboard",
                        "link": lambda request: reverse_lazy("admin:index"),
                    },
                    {
                        "title": "系统统计",
                        "icon": "analytics",
                        "link": lambda request: reverse_lazy("admin:dashboard_systemstatistics_changelist"),
                    },
                    {
                        "title": "每日统计",
                        "icon": "calendar_today",
                        "link": lambda request: reverse_lazy("admin:dashboard_dailystatistics_changelist"),
                    },
                ],
            },
            {
                "title": "用户管理",
                "separator": True,
                "items": [
                    {
                        "title": "用户管理",
                        "icon": "people",
                        "link": lambda request: reverse_lazy("admin:users_user_changelist"),
                    },
                    {
                        "title": "算力日志",
                        "icon": "memory",
                        "link": lambda request: reverse_lazy("admin:users_computingpowerlog_changelist"),
                    },
                    {
                        "title": "用户套餐",
                        "icon": "card_membership",
                        "link": lambda request: reverse_lazy("admin:users_userpackage_changelist"),
                    },
                ],
            },
            {
                "title": "数字人管理",
                "separator": True,
                "items": [
                    {
                        "title": "数字人形象",
                        "icon": "smart_toy",
                        "link": lambda request: reverse_lazy("admin:avatars_digitalavatar_changelist"),
                    },
                    {
                        "title": "形象分类",
                        "icon": "category",
                        "link": lambda request: reverse_lazy("admin:avatars_avatarcategory_changelist"),
                    },
                    {
                        "title": "形象标签",
                        "icon": "label",
                        "link": lambda request: reverse_lazy("admin:avatars_avatartag_changelist"),
                    },
                    {
                        "title": "使用日志",
                        "icon": "history",
                        "link": lambda request: reverse_lazy("admin:avatars_avatarusagelog_changelist"),
                    },
                ],
            },
            {
                "title": "语音管理",
                "separator": True,
                "items": [
                    {
                        "title": "语音模型",
                        "icon": "record_voice_over",
                        "link": lambda request: reverse_lazy("admin:voices_voicemodel_changelist"),
                    },
                    {
                        "title": "语音分类",
                        "icon": "voice_chat",
                        "link": lambda request: reverse_lazy("admin:voices_voicecategory_changelist"),
                    },
                    {
                        "title": "语音标签",
                        "icon": "label_important",
                        "link": lambda request: reverse_lazy("admin:voices_voicetag_changelist"),
                    },
                    {
                        "title": "语音克隆请求",
                        "icon": "content_copy",
                        "link": lambda request: reverse_lazy("admin:voices_voiceclonerequest_changelist"),
                    },
                    {
                        "title": "语音合成任务",
                        "icon": "queue_music",
                        "link": lambda request: reverse_lazy("admin:voices_voicesynthesistask_changelist"),
                    },
                ],
            },
            {
                "title": "视频管理",
                "separator": True,
                "items": [
                    {
                        "title": "视频作品",
                        "icon": "video_library",
                        "link": lambda request: reverse_lazy("admin:videos_videoproduction_changelist"),
                    },
                    {
                        "title": "视频模板",
                        "icon": "video_settings",
                        "link": lambda request: reverse_lazy("admin:videos_videotemplate_changelist"),
                    },
                    {
                        "title": "视频分享",
                        "icon": "share",
                        "link": lambda request: reverse_lazy("admin:videos_videoshare_changelist"),
                    },
                    {
                        "title": "渲染任务",
                        "icon": "settings_applications",
                        "link": lambda request: reverse_lazy("admin:videos_renderingtask_changelist"),
                    },
                ],
            },
            {
                "title": "AI工具",
                "separator": True,
                "items": [
                    {
                        "title": "智能文案",
                        "icon": "edit_note",
                        "link": lambda request: reverse_lazy("admin:copywriting_copywriting_changelist"),
                    },
                    {
                        "title": "文案模板",
                        "icon": "description",
                        "link": lambda request: reverse_lazy("admin:copywriting_copywritingtemplate_changelist"),
                    },
                    {
                        "title": "文案历史",
                        "icon": "history",
                        "link": lambda request: reverse_lazy("admin:copywriting_copywritinghistory_changelist"),
                    },
                    {
                        "title": "文案提取",
                        "icon": "content_copy",
                        "link": lambda request: reverse_lazy("admin:copywriting_textextraction_changelist"),
                    },
                    {
                        "title": "AI工具使用日志",
                        "icon": "auto_awesome",
                        "link": lambda request: reverse_lazy("admin:ai_tools_aitoolusagelog_changelist"),
                    },
                    {
                        "title": "AI服务配置",
                        "icon": "settings",
                        "link": lambda request: reverse_lazy("admin:ai_tools_aiserviceconfig_changelist"),
                    },
                    {
                        "title": "AI对话记录",
                        "icon": "chat",
                        "link": lambda request: reverse_lazy("admin:ai_tools_aiconversation_changelist"),
                    },
                    {
                        "title": "AI图像生成",
                        "icon": "image",
                        "link": lambda request: reverse_lazy("admin:ai_tools_aiimagegeneration_changelist"),
                    },
                ],
            },

            {
                "title": "数字作品",
                "separator": True,
                "items": [
                    {
                        "title": "数字人作品",
                        "icon": "collections",
                        "link": lambda request: reverse_lazy("admin:digital_works_digitalwork_changelist"),
                    },
                    {
                        "title": "作品模板",
                        "icon": "template_add",
                        "link": lambda request: reverse_lazy("admin:digital_works_worktemplate_changelist"),
                    },
                ],
            },
            {
                "title": "套餐管理",
                "separator": True,
                "items": [
                    {
                        "title": "套餐管理",
                        "icon": "inventory",
                        "link": lambda request: reverse_lazy("admin:packages_package_changelist"),
                    },
                    {
                        "title": "套餐分类",
                        "icon": "category",
                        "link": lambda request: reverse_lazy("admin:packages_packagecategory_changelist"),
                    },
                    {
                        "title": "套餐功能",
                        "icon": "featured_play_list",
                        "link": lambda request: reverse_lazy("admin:packages_packagefeature_changelist"),
                    },
                ],
            },
            {
                "title": "系统管理",
                "separator": True,
                "items": [
                    {
                        "title": "用户组",
                        "icon": "group",
                        "link": lambda request: reverse_lazy("admin:auth_group_changelist"),
                    },
                ],
            },

        ],
    },
}

def environment_callback(request):
    """Environment indicator for admin"""
    return ["Development", "warning"] if DEBUG else ["Production", "danger"]

def dashboard_callback(request, context):
    """Dashboard customization"""
    # 添加自定义仪表盘数据
    from django.contrib.auth import get_user_model

    User = get_user_model()

    # 统计数据
    stats = {
        'total_users': User.objects.count(),
        'active_users': User.objects.filter(is_active=True).count(),
    }

    # 尝试获取其他模型的统计数据
    try:
        from apps.avatars.models import DigitalAvatar
        from apps.voices.models import VoiceModel
        from apps.videos.models import VideoProduction
        from apps.copywriting.models import Copywriting

        stats.update({
            'total_avatars': DigitalAvatar.objects.count(),
            'total_voices': VoiceModel.objects.count(),
            'total_videos': VideoProduction.objects.count(),
            'total_copywriting': Copywriting.objects.count(),
        })
    except ImportError:
        # 如果模型还没有迁移，忽略错误
        pass

    # 添加统计数据到上下文
    context.update({
        'platform_stats': stats,
        'platform_name': 'AI数字人SaaS平台',
    })

    return context

# Celery配置
CELERY_BROKER_URL = config('REDIS_URL', default='redis://127.0.0.1:6379/1')
CELERY_RESULT_BACKEND = config('REDIS_URL', default='redis://127.0.0.1:6379/1')
CELERY_ACCEPT_CONTENT = ['json']
CELERY_TASK_SERIALIZER = 'json'
CELERY_RESULT_SERIALIZER = 'json'
CELERY_TIMEZONE = TIME_ZONE
CELERY_ENABLE_UTC = True

# Celery任务路由
CELERY_TASK_ROUTES = {
    'apps.voices.tasks.*': {'queue': 'voice_processing'},
    'apps.videos.tasks.*': {'queue': 'video_processing'},
    'apps.copywriting.tasks.*': {'queue': 'ai_processing'},
    'apps.ai_tools.tasks.*': {'queue': 'ai_processing'},
}

# Celery定时任务
CELERY_BEAT_SCHEDULE = {
    'cleanup-expired-synthesis-tasks': {
        'task': 'apps.voices.tasks.cleanup_expired_synthesis_tasks',
        'schedule': 86400.0,  # 每天执行一次
    },
    'cleanup-expired-videos': {
        'task': 'apps.videos.tasks.cleanup_expired_videos',
        'schedule': 86400.0,  # 每天执行一次
    },
    'cleanup-expired-copywriting': {
        'task': 'apps.copywriting.tasks.cleanup_expired_copywriting',
        'schedule': 86400.0,  # 每天执行一次
    },
    'update-voice-model-usage-stats': {
        'task': 'apps.voices.tasks.update_voice_model_usage_stats',
        'schedule': 3600.0,  # 每小时执行一次
    },
    'update-video-statistics': {
        'task': 'apps.videos.tasks.update_video_statistics',
        'schedule': 3600.0,  # 每小时执行一次
    },
    'cleanup-ai-tool-cache': {
        'task': 'apps.ai_tools.tasks.cleanup_ai_tool_cache',
        'schedule': 7200.0,  # 每2小时执行一次
    },
}

# 日志配置
import os
os.makedirs(BASE_DIR / 'logs', exist_ok=True)

LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {message}',
            'style': '{',
        },
        'json': {
            'format': '{"level": "%(levelname)s", "time": "%(asctime)s", "module": "%(module)s", "message": "%(message)s"}',
        },
    },
    'filters': {
        'require_debug_true': {
            '()': 'django.utils.log.RequireDebugTrue',
        },
        'require_debug_false': {
            '()': 'django.utils.log.RequireDebugFalse',
        },
    },
    'handlers': {
        'console': {
            'level': 'INFO',
            'filters': ['require_debug_true'],
            'class': 'logging.StreamHandler',
            'formatter': 'simple'
        },
        'file': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': BASE_DIR / 'logs' / 'django.log',
            'maxBytes': 1024*1024*15,  # 15MB
            'backupCount': 10,
            'formatter': 'verbose',
        },
        'error_file': {
            'level': 'ERROR',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': BASE_DIR / 'logs' / 'error.log',
            'maxBytes': 1024*1024*15,  # 15MB
            'backupCount': 10,
            'formatter': 'verbose',
        },
        'api_file': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': BASE_DIR / 'logs' / 'api.log',
            'maxBytes': 1024*1024*15,  # 15MB
            'backupCount': 10,
            'formatter': 'json',
        },
        'security_file': {
            'level': 'WARNING',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': BASE_DIR / 'logs' / 'security.log',
            'maxBytes': 1024*1024*15,  # 15MB
            'backupCount': 10,
            'formatter': 'json',
        },
        'database': {
            'level': 'WARNING',
            'class': 'apps.common.logging.DatabaseLogHandler',
        },
    },
    'root': {
        'level': 'INFO',
        'handlers': ['console', 'file'],
    },
    'loggers': {
        'django': {
            'handlers': ['console', 'file'],
            'level': 'INFO',
            'propagate': False,
        },
        'django.request': {
            'handlers': ['error_file'],
            'level': 'ERROR',
            'propagate': False,
        },
        'apps': {
            'handlers': ['console', 'file', 'api_file'],
            'level': 'INFO',
            'propagate': False,
        },
        'ai_tools': {
            'handlers': ['console', 'file', 'api_file'],
            'level': 'INFO',
            'propagate': False,
        },
        'digital_works': {
            'handlers': ['console', 'file', 'api_file'],
            'level': 'INFO',
            'propagate': False,
        },
        'security': {
            'handlers': ['security_file', 'database'],
            'level': 'WARNING',
            'propagate': False,
        },
        'user_action': {
            'handlers': ['file', 'database'],
            'level': 'INFO',
            'propagate': False,
        },
        'business': {
            'handlers': ['file'],
            'level': 'INFO',
            'propagate': True,
        },
        'system': {
            'handlers': ['file'],
            'level': 'INFO',
            'propagate': True,
        },
    }
}

# 安全配置
SECURE_BROWSER_XSS_FILTER = True
SECURE_CONTENT_TYPE_NOSNIFF = True
X_FRAME_OPTIONS = 'DENY'
SECURE_HSTS_SECONDS = 31536000  # 1年
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_HSTS_PRELOAD = True

# 会话安全
SESSION_COOKIE_SECURE = False  # 开发环境设置为False，生产环境设置为True
SESSION_COOKIE_HTTPONLY = True
SESSION_COOKIE_SAMESITE = 'Lax'
CSRF_COOKIE_SECURE = False  # 开发环境设置为False，生产环境设置为True
CSRF_COOKIE_HTTPONLY = True
CSRF_COOKIE_SAMESITE = 'Lax'

# CSRF配置
CSRF_TRUSTED_ORIGINS = [
    'http://localhost:3000',
    'http://127.0.0.1:3000',
    'http://localhost:5173',
    'http://127.0.0.1:5173',
]

# API接口CSRF豁免
CSRF_EXEMPT_URLS = [
    r'^/api/v1/auth/.*$',
    r'^/api/v1/.*$',
]

# 文件上传安全
FILE_UPLOAD_MAX_MEMORY_SIZE = 100 * 1024 * 1024  # 100MB
DATA_UPLOAD_MAX_MEMORY_SIZE = 100 * 1024 * 1024  # 100MB
DATA_UPLOAD_MAX_NUMBER_FIELDS = 10000  # 增加字段数量限制
FILE_UPLOAD_PERMISSIONS = 0o644

# 允许的文件类型
ALLOWED_UPLOAD_EXTENSIONS = [
    '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp',  # 图片
    '.mp3', '.wav', '.flac', '.aac', '.ogg',  # 音频
    '.mp4', '.avi', '.mov', '.wmv', '.flv',  # 视频
    '.pdf', '.doc', '.docx', '.txt',  # 文档
]

# 最大文件大小（字节）
MAX_UPLOAD_SIZE = {
    'image': 5 * 1024 * 1024,    # 5MB
    'audio': 50 * 1024 * 1024,   # 50MB
    'video': 100 * 1024 * 1024,  # 100MB
    'document': 10 * 1024 * 1024, # 10MB
}

# 安全头配置
SECURE_REFERRER_POLICY = 'strict-origin-when-cross-origin'

# 内容安全策略
CSP_DEFAULT_SRC = ("'self'",)
CSP_SCRIPT_SRC = ("'self'", "'unsafe-inline'", "'unsafe-eval'")
CSP_STYLE_SRC = ("'self'", "'unsafe-inline'")
CSP_IMG_SRC = ("'self'", "data:", "https:")
CSP_FONT_SRC = ("'self'", "https:")
CSP_CONNECT_SRC = ("'self'", "https:")
CSP_FRAME_ANCESTORS = ("'none'",)
