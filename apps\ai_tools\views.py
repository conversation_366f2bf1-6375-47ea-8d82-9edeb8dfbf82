"""
AI工具相关视图
"""
from rest_framework import status, permissions
from rest_framework.views import APIView
from rest_framework.response import Response
from django.contrib.auth import get_user_model
from .services.openai_service import openai_service, chat_with_ai, generate_ai_image
from .models import AIToolUsageLog

User = get_user_model()


class AIChatView(APIView):
    """AI对话"""
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request):
        """AI对话接口"""
        user = request.user
        message = request.data.get('message', '')
        
        if not message:
            return Response(
                {'error': '请提供对话内容'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # 计算算力消耗
        computing_power_needed = 2  # 每次对话消耗2算力
        
        # 检查算力是否足够
        if user.computing_power < computing_power_needed:
            return Response(
                {'error': f'算力不足，需要 {computing_power_needed} 算力'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            # 消耗算力
            user.consume_computing_power(
                computing_power_needed,
                'AI对话'
            )

            # 调用OpenAI API进行对话
            context = request.data.get('context', [])
            try:
                ai_result = chat_with_ai(message, context)
            except Exception as ai_error:
                # 如果AI服务失败，使用降级处理
                ai_result = {
                    'success': True,  # 改为True，表示降级处理成功
                    'content': f'抱歉，AI服务暂时不可用，但我已收到您的问题："{message}"。请稍后再试或联系管理员。',
                    'is_fallback': True,
                    'error_info': str(ai_error)
                }

            # 记录使用日志
            try:
                AIToolUsageLog.objects.create(
                    user=user,
                    tool_name='ai_chat',
                    input_data={'message': message, 'context': context},
                    output_data={'response': ai_result.get('content', '')},
                    computing_power_consumed=computing_power_needed,
                    is_fallback=ai_result.get('is_fallback', False)
                )
            except Exception as log_error:
                # 日志记录失败不影响主功能
                print(f"AI对话日志记录失败: {log_error}")

            return Response({
                'message': 'AI对话成功',
                'user_message': message,
                'ai_response': ai_result['content'],
                'model': ai_result.get('model', 'unknown'),
                'usage': ai_result.get('usage', {}),
                'computing_power_consumed': computing_power_needed,
                'remaining_power': user.computing_power,
                'is_fallback': ai_result.get('is_fallback', False),
                'service_status': 'fallback' if ai_result.get('is_fallback') else 'normal'
            })
            
        except ValueError as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )


class AIPaintingView(APIView):
    """AI绘画"""
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request):
        """AI绘画接口"""
        user = request.user
        prompt = request.data.get('prompt', '')
        style = request.data.get('style', 'realistic')
        size = request.data.get('size', '512x512')
        
        if not prompt:
            return Response(
                {'error': '请提供绘画描述'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # 计算算力消耗（根据尺寸）
        size_costs = {
            '512x512': 10,
            '1024x1024': 20,
            '1024x1536': 30,
        }
        computing_power_needed = size_costs.get(size, 10)
        
        # 检查算力是否足够
        if user.computing_power < computing_power_needed:
            return Response(
                {'error': f'算力不足，需要 {computing_power_needed} 算力'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            # 消耗算力
            user.consume_computing_power(
                computing_power_needed,
                f'AI绘画: {prompt[:20]}'
            )

            # 调用OpenAI DALL-E API生成图像
            try:
                ai_result = generate_ai_image(prompt, size)
            except Exception as ai_error:
                # 如果AI服务失败，使用降级处理
                ai_result = {
                    'success': True,  # 改为True，表示降级处理成功
                    'image_url': '/static/images/placeholder.jpg',
                    'is_fallback': True,
                    'error_info': str(ai_error),
                    'message': f'AI绘画服务暂时不可用，显示占位图片。提示词：{prompt}'
                }

            # 记录使用日志
            try:
                AIToolUsageLog.objects.create(
                    user=user,
                    tool_name='ai_painting',
                    input_data={'prompt': prompt, 'style': style, 'size': size},
                    output_data={'image_url': ai_result.get('image_url', '')},
                    computing_power_consumed=computing_power_needed,
                    is_fallback=ai_result.get('is_fallback', False)
                )
            except Exception as log_error:
                # 日志记录失败不影响主功能
                print(f"AI绘画日志记录失败: {log_error}")

            # 获取生成的图像信息
            images = ai_result.get('images', [])
            image_data = images[0] if images else {}

            # 如果是降级处理，使用降级的图片URL
            image_url = ai_result.get('image_url', '') if ai_result.get('is_fallback') else image_data.get('url', '')

            return Response({
                'message': 'AI绘画生成成功' if not ai_result.get('is_fallback') else ai_result.get('message', 'AI绘画生成成功'),
                'prompt': prompt,
                'revised_prompt': image_data.get('revised_prompt', prompt),
                'style': style,
                'size': size,
                'image_url': image_url,
                'computing_power_consumed': computing_power_needed,
                'remaining_power': user.computing_power,
                'is_fallback': ai_result.get('is_fallback', False),
                'service_status': 'fallback' if ai_result.get('is_fallback') else 'normal'
            })
            
        except ValueError as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )


class AIMusicView(APIView):
    """AI音乐"""
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request):
        """AI音乐生成接口"""
        user = request.user
        description = request.data.get('description', '')
        duration = request.data.get('duration', 30)  # 秒
        genre = request.data.get('genre', 'pop')
        
        if not description:
            return Response(
                {'error': '请提供音乐描述'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # 计算算力消耗（按时长）
        computing_power_needed = max(duration // 10, 5)  # 每10秒消耗1算力，最少5算力
        
        # 检查算力是否足够
        if user.computing_power < computing_power_needed:
            return Response(
                {'error': f'算力不足，需要 {computing_power_needed} 算力'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            # 消耗算力
            user.consume_computing_power(
                computing_power_needed,
                f'AI音乐: {description[:20]}'
            )
            
            # 这里应该调用实际的AI音乐服务
            # 暂时返回模拟结果
            audio_url = "https://example.com/generated_music.mp3"
            
            return Response({
                'message': 'AI音乐生成任务已创建',
                'description': description,
                'duration': duration,
                'genre': genre,
                'audio_url': audio_url,
                'computing_power_consumed': computing_power_needed,
                'remaining_power': user.computing_power
            })
            
        except ValueError as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )


class AIVideoView(APIView):
    """AI视频"""
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request):
        """AI视频生成接口"""
        user = request.user
        prompt = request.data.get('prompt', '')
        duration = request.data.get('duration', 5)  # 秒
        resolution = request.data.get('resolution', '720p')
        
        if not prompt:
            return Response(
                {'error': '请提供视频描述'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # 计算算力消耗
        resolution_costs = {
            '480p': 5,
            '720p': 10,
            '1080p': 20,
        }
        base_cost = resolution_costs.get(resolution, 10)
        computing_power_needed = base_cost * max(duration // 5, 1)  # 每5秒为一个单位
        
        # 检查算力是否足够
        if user.computing_power < computing_power_needed:
            return Response(
                {'error': f'算力不足，需要 {computing_power_needed} 算力'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            # 消耗算力
            user.consume_computing_power(
                computing_power_needed,
                f'AI视频: {prompt[:20]}'
            )
            
            # 这里应该调用实际的AI视频服务
            # 暂时返回模拟结果
            video_url = "https://example.com/generated_video.mp4"
            
            return Response({
                'message': 'AI视频生成任务已创建',
                'prompt': prompt,
                'duration': duration,
                'resolution': resolution,
                'video_url': video_url,
                'computing_power_consumed': computing_power_needed,
                'remaining_power': user.computing_power
            })
            
        except ValueError as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )


class FaceFusionView(APIView):
    """人脸融合"""
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request):
        """人脸融合接口"""
        user = request.user
        source_image = request.FILES.get('source_image')
        target_image = request.FILES.get('target_image')
        
        if not source_image or not target_image:
            return Response(
                {'error': '请提供源图片和目标图片'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # 计算算力消耗
        computing_power_needed = 8  # 人脸融合固定消耗8算力
        
        # 检查算力是否足够
        if user.computing_power < computing_power_needed:
            return Response(
                {'error': f'算力不足，需要 {computing_power_needed} 算力'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            # 消耗算力
            user.consume_computing_power(
                computing_power_needed,
                '人脸融合'
            )
            
            # 这里应该调用实际的人脸融合服务
            # 暂时返回模拟结果
            result_image_url = "https://example.com/face_fusion_result.jpg"
            
            return Response({
                'message': '人脸融合任务已完成',
                'result_image_url': result_image_url,
                'computing_power_consumed': computing_power_needed,
                'remaining_power': user.computing_power
            })
            
        except ValueError as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )


class PhotoDigitalHumanView(APIView):
    """照片数字人"""
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request):
        """照片数字人生成接口"""
        user = request.user
        photo = request.FILES.get('photo')
        voice_text = request.data.get('voice_text', '')
        
        if not photo:
            return Response(
                {'error': '请提供照片'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        if not voice_text:
            return Response(
                {'error': '请提供语音文本'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # 计算算力消耗
        computing_power_needed = 15  # 照片数字人固定消耗15算力
        
        # 检查算力是否足够
        if user.computing_power < computing_power_needed:
            return Response(
                {'error': f'算力不足，需要 {computing_power_needed} 算力'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            # 消耗算力
            user.consume_computing_power(
                computing_power_needed,
                '照片数字人'
            )
            
            # 这里应该调用实际的照片数字人服务
            # 暂时返回模拟结果
            video_url = "https://example.com/photo_digital_human.mp4"
            
            return Response({
                'message': '照片数字人生成任务已创建',
                'voice_text': voice_text,
                'video_url': video_url,
                'computing_power_consumed': computing_power_needed,
                'remaining_power': user.computing_power
            })
            
        except ValueError as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )


class TextExtractionView(APIView):
    """文案提取"""
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request):
        """文案提取接口"""
        user = request.user
        source_file = request.FILES.get('source_file')
        source_url = request.data.get('source_url', '')
        
        if not source_file and not source_url:
            return Response(
                {'error': '请提供文件或URL'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # 计算算力消耗
        computing_power_needed = 3  # 文案提取固定消耗3算力
        
        # 检查算力是否足够
        if user.computing_power < computing_power_needed:
            return Response(
                {'error': f'算力不足，需要 {computing_power_needed} 算力'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            # 消耗算力
            user.consume_computing_power(
                computing_power_needed,
                '文案提取'
            )
            
            # 这里应该调用实际的文案提取服务
            # 暂时返回模拟结果
            extracted_text = "这是从文件中提取的示例文本内容。"
            
            return Response({
                'message': '文案提取完成',
                'extracted_text': extracted_text,
                'confidence': 0.95,
                'computing_power_consumed': computing_power_needed,
                'remaining_power': user.computing_power
            })
            
        except ValueError as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
