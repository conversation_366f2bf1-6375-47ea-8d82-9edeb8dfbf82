"""
创建示例套餐数据
"""
from django.core.management.base import BaseCommand
from apps.packages.models import PackageCategory, Package


class Command(BaseCommand):
    help = '创建示例套餐数据'

    def handle(self, *args, **options):
        self.stdout.write('开始创建示例套餐数据...')

        # 创建套餐分类
        categories = [
            {'name': '算力套餐', 'description': '购买算力点数用于数字人视频制作'},
            {'name': '会员套餐', 'description': '购买会员享受更多功能和优惠'},
            {'name': '组合套餐', 'description': '算力和会员的组合优惠套餐'},
        ]

        for cat_data in categories:
            category, created = PackageCategory.objects.get_or_create(
                name=cat_data['name'],
                defaults=cat_data
            )
            if created:
                self.stdout.write(f'创建分类: {category.name}')

        # 获取分类
        computing_category = PackageCategory.objects.get(name='算力套餐')
        membership_category = PackageCategory.objects.get(name='会员套餐')
        combo_category = PackageCategory.objects.get(name='组合套餐')

        # 创建算力套餐
        computing_packages = [
            {
                'category': computing_category,
                'name': '算力套餐100',
                'description': '100算力点数，适合轻度使用',
                'package_type': 'computing_power',
                'price': 10.00,
                'computing_power_amount': 100,
                'bonus_computing_power': 0,
                'features': ['100算力点数', '永久有效', '可用于所有功能'],
                'sort_order': 1,
            },
            {
                'category': computing_category,
                'name': '算力套餐500',
                'description': '500算力点数，赠送50算力',
                'package_type': 'computing_power',
                'price': 45.00,
                'original_price': 50.00,
                'computing_power_amount': 500,
                'bonus_computing_power': 50,
                'features': ['500算力点数', '赠送50算力', '永久有效', '可用于所有功能'],
                'sort_order': 2,
            },
            {
                'category': computing_category,
                'name': '算力套餐1000',
                'description': '1000算力点数，赠送200算力',
                'package_type': 'computing_power',
                'price': 80.00,
                'original_price': 100.00,
                'computing_power_amount': 1000,
                'bonus_computing_power': 200,
                'features': ['1000算力点数', '赠送200算力', '永久有效', '可用于所有功能'],
                'is_recommended': True,
                'sort_order': 3,
            },
            {
                'category': computing_category,
                'name': '算力套餐2000',
                'description': '2000算力点数，赠送500算力',
                'package_type': 'computing_power',
                'price': 150.00,
                'original_price': 200.00,
                'computing_power_amount': 2000,
                'bonus_computing_power': 500,
                'features': ['2000算力点数', '赠送500算力', '永久有效', '可用于所有功能'],
                'sort_order': 4,
            },
            {
                'category': computing_category,
                'name': '算力套餐5000',
                'description': '5000算力点数，赠送1500算力',
                'package_type': 'computing_power',
                'price': 350.00,
                'original_price': 500.00,
                'computing_power_amount': 5000,
                'bonus_computing_power': 1500,
                'features': ['5000算力点数', '赠送1500算力', '永久有效', '可用于所有功能'],
                'sort_order': 5,
            },
            {
                'category': computing_category,
                'name': '算力套餐10000',
                'description': '10000算力点数，赠送3500算力',
                'package_type': 'computing_power',
                'price': 650.00,
                'original_price': 1000.00,
                'computing_power_amount': 10000,
                'bonus_computing_power': 3500,
                'features': ['10000算力点数', '赠送3500算力', '永久有效', '可用于所有功能'],
                'sort_order': 6,
            },
        ]

        for pkg_data in computing_packages:
            package, created = Package.objects.get_or_create(
                name=pkg_data['name'],
                defaults=pkg_data
            )
            if created:
                self.stdout.write(f'创建套餐: {package.name}')

        # 创建会员套餐
        membership_packages = [
            {
                'category': membership_category,
                'name': '标准版80分钟',
                'description': '标准版会员，包含80分钟数字人视频合成',
                'package_type': 'membership',
                'price': 980.00,
                'original_price': 2980.00,
                'computing_power_amount': 0,
                'synthesis_duration': 4800,  # 80分钟 = 4800秒
                'voice_clone_count': 1,
                'avatar_count': 999,
                'duration_type': 'days',
                'duration_value': 365,
                'features': [
                    '数字人视频合成：4800秒',
                    '数字人形象：999个',
                    '语音克隆形象：1个',
                    '文字转语音合成：4800秒'
                ],
                'sort_order': 1,
            },
            {
                'category': membership_category,
                'name': '专业版300分钟',
                'description': '专业版会员，包含300分钟数字人视频合成',
                'package_type': 'membership',
                'price': 2980.00,
                'original_price': 8980.00,
                'computing_power_amount': 0,
                'synthesis_duration': 18000,  # 300分钟 = 18000秒
                'voice_clone_count': 3,
                'avatar_count': 999,
                'duration_type': 'days',
                'duration_value': 365,
                'features': [
                    '数字人视频合成：18000秒',
                    '数字人形象：999个',
                    '语音克隆形象：3个',
                    '文字转语音合成：18000秒'
                ],
                'is_recommended': True,
                'sort_order': 2,
            },
        ]

        for pkg_data in membership_packages:
            package, created = Package.objects.get_or_create(
                name=pkg_data['name'],
                defaults=pkg_data
            )
            if created:
                self.stdout.write(f'创建套餐: {package.name}')

        self.stdout.write(self.style.SUCCESS('示例套餐数据创建完成！'))
