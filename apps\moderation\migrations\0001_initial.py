# Generated by Django 5.2.1 on 2025-07-16 12:53

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("contenttypes", "0002_remove_content_type_name"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="ModerationRequest",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("object_id", models.PositiveIntegerField()),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "待审核"),
                            ("approved", "已通过"),
                            ("rejected", "已拒绝"),
                            ("flagged", "已标记"),
                            ("auto_approved", "自动通过"),
                            ("auto_rejected", "自动拒绝"),
                        ],
                        default="pending",
                        max_length=20,
                        verbose_name="状态",
                    ),
                ),
                (
                    "priority",
                    models.CharField(
                        choices=[
                            ("low", "低"),
                            ("normal", "普通"),
                            ("high", "高"),
                            ("urgent", "紧急"),
                        ],
                        default="normal",
                        max_length=10,
                        verbose_name="优先级",
                    ),
                ),
                ("review_notes", models.TextField(blank=True, verbose_name="审核备注")),
                ("rejection_reason", models.TextField(blank=True, verbose_name="拒绝原因")),
                (
                    "auto_review_score",
                    models.FloatField(blank=True, null=True, verbose_name="自动审核分数"),
                ),
                (
                    "auto_review_details",
                    models.JSONField(blank=True, default=dict, verbose_name="自动审核详情"),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "reviewed_at",
                    models.DateTimeField(blank=True, null=True, verbose_name="审核时间"),
                ),
                (
                    "content_type",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="contenttypes.contenttype",
                    ),
                ),
                (
                    "reviewer",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="reviewed_requests",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="审核员",
                    ),
                ),
                (
                    "submitted_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="提交者",
                    ),
                ),
            ],
            options={
                "verbose_name": "审核请求",
                "verbose_name_plural": "审核请求",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="ModerationLog",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "action",
                    models.CharField(
                        choices=[
                            ("submit", "提交审核"),
                            ("approve", "通过审核"),
                            ("reject", "拒绝审核"),
                            ("flag", "标记内容"),
                            ("auto_approve", "自动通过"),
                            ("auto_reject", "自动拒绝"),
                            ("rule_triggered", "规则触发"),
                        ],
                        max_length=20,
                        verbose_name="操作",
                    ),
                ),
                (
                    "details",
                    models.JSONField(blank=True, default=dict, verbose_name="操作详情"),
                ),
                ("notes", models.TextField(blank=True, verbose_name="备注")),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "user",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="操作用户",
                    ),
                ),
                (
                    "moderation_request",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="logs",
                        to="moderation.moderationrequest",
                        verbose_name="审核请求",
                    ),
                ),
            ],
            options={
                "verbose_name": "审核日志",
                "verbose_name_plural": "审核日志",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="ModerationRule",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100, verbose_name="规则名称")),
                (
                    "rule_type",
                    models.CharField(
                        choices=[
                            ("keyword", "关键词过滤"),
                            ("length", "长度限制"),
                            ("format", "格式检查"),
                            ("ai_detection", "AI检测"),
                            ("manual", "人工审核"),
                        ],
                        max_length=20,
                        verbose_name="规则类型",
                    ),
                ),
                ("description", models.TextField(blank=True, verbose_name="规则描述")),
                (
                    "config",
                    models.JSONField(
                        default=dict, help_text="JSON格式的规则配置", verbose_name="规则配置"
                    ),
                ),
                ("is_active", models.BooleanField(default=True, verbose_name="启用")),
                (
                    "severity",
                    models.CharField(
                        choices=[
                            ("low", "低"),
                            ("medium", "中"),
                            ("high", "高"),
                            ("critical", "严重"),
                        ],
                        default="medium",
                        max_length=10,
                        verbose_name="严重程度",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "content_types",
                    models.ManyToManyField(
                        help_text="此规则适用的内容类型",
                        to="contenttypes.contenttype",
                        verbose_name="适用内容类型",
                    ),
                ),
            ],
            options={
                "verbose_name": "审核规则",
                "verbose_name_plural": "审核规则",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="ContentFlag",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("object_id", models.PositiveIntegerField()),
                (
                    "flag_type",
                    models.CharField(
                        choices=[
                            ("spam", "垃圾内容"),
                            ("inappropriate", "不当内容"),
                            ("copyright", "版权侵犯"),
                            ("harassment", "骚扰"),
                            ("violence", "暴力内容"),
                            ("hate_speech", "仇恨言论"),
                            ("misinformation", "虚假信息"),
                            ("other", "其他"),
                        ],
                        max_length=20,
                        verbose_name="举报类型",
                    ),
                ),
                ("reason", models.TextField(verbose_name="举报原因")),
                ("is_resolved", models.BooleanField(default=False, verbose_name="已处理")),
                ("resolution_notes", models.TextField(blank=True, verbose_name="处理备注")),
                (
                    "resolved_at",
                    models.DateTimeField(blank=True, null=True, verbose_name="处理时间"),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "content_type",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="contenttypes.contenttype",
                    ),
                ),
                (
                    "reporter",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="举报者",
                    ),
                ),
                (
                    "resolved_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="resolved_flags",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="处理人",
                    ),
                ),
            ],
            options={
                "verbose_name": "内容举报",
                "verbose_name_plural": "内容举报",
                "ordering": ["-created_at"],
                "unique_together": {("content_type", "object_id", "reporter")},
            },
        ),
        migrations.AddIndex(
            model_name="moderationrequest",
            index=models.Index(
                fields=["status", "priority"], name="moderation__status_dba9ef_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="moderationrequest",
            index=models.Index(
                fields=["content_type", "object_id"],
                name="moderation__content_83caee_idx",
            ),
        ),
    ]
