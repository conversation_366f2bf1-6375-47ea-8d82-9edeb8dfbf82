# Generated by Django 5.2.1 on 2025-07-16 11:30

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("videos", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="RenderingTask",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "待处理"),
                            ("processing", "处理中"),
                            ("completed", "已完成"),
                            ("failed", "失败"),
                            ("cancelled", "已取消"),
                        ],
                        default="pending",
                        help_text="渲染任务的当前状态",
                        max_length=20,
                        verbose_name="状态",
                    ),
                ),
                (
                    "priority",
                    models.CharField(
                        choices=[
                            ("low", "低"),
                            ("normal", "普通"),
                            ("high", "高"),
                            ("urgent", "紧急"),
                        ],
                        default="normal",
                        help_text="渲染任务的优先级",
                        max_length=10,
                        verbose_name="优先级",
                    ),
                ),
                (
                    "progress",
                    models.PositiveIntegerField(
                        default=0, help_text="渲染进度百分比（0-100）", verbose_name="进度"
                    ),
                ),
                (
                    "estimated_duration",
                    models.PositiveIntegerField(
                        blank=True,
                        help_text="预计渲染时长（秒）",
                        null=True,
                        verbose_name="预计时长",
                    ),
                ),
                (
                    "actual_duration",
                    models.PositiveIntegerField(
                        blank=True,
                        help_text="实际渲染时长（秒）",
                        null=True,
                        verbose_name="实际时长",
                    ),
                ),
                (
                    "error_message",
                    models.TextField(
                        blank=True, help_text="渲染失败时的错误信息", verbose_name="错误信息"
                    ),
                ),
                (
                    "computing_power_consumed",
                    models.PositiveIntegerField(
                        default=0, help_text="此次渲染消耗的算力点数", verbose_name="消耗算力"
                    ),
                ),
                (
                    "started_at",
                    models.DateTimeField(
                        blank=True, help_text="渲染开始时间", null=True, verbose_name="开始时间"
                    ),
                ),
                (
                    "completed_at",
                    models.DateTimeField(
                        blank=True, help_text="渲染完成时间", null=True, verbose_name="完成时间"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "user",
                    models.ForeignKey(
                        help_text="提交渲染任务的用户",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="rendering_tasks",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="用户",
                    ),
                ),
                (
                    "video",
                    models.ForeignKey(
                        help_text="需要渲染的视频",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="rendering_tasks",
                        to="videos.videoproduction",
                        verbose_name="关联视频",
                    ),
                ),
            ],
            options={
                "verbose_name": "渲染任务",
                "verbose_name_plural": "渲染任务",
                "db_table": "video_rendering_tasks",
                "ordering": ["-created_at"],
                "indexes": [
                    models.Index(
                        fields=["status", "priority"],
                        name="video_rende_status_2adc32_idx",
                    ),
                    models.Index(
                        fields=["user", "status"], name="video_rende_user_id_797814_idx"
                    ),
                    models.Index(
                        fields=["video"], name="video_rende_video_i_cf2c71_idx"
                    ),
                ],
            },
        ),
    ]
