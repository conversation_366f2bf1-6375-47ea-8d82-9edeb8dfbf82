# Generated by Django 5.2.4 on 2025-07-15 02:41

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="AvatarCategory",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        help_text="数字人形象分类名称",
                        max_length=50,
                        unique=True,
                        verbose_name="分类名称",
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        blank=True, help_text="分类的详细描述", verbose_name="分类描述"
                    ),
                ),
                (
                    "icon",
                    models.CharField(
                        blank=True,
                        help_text="分类图标的CSS类名或图标名称",
                        max_length=50,
                        verbose_name="图标",
                    ),
                ),
                (
                    "sort_order",
                    models.PositiveIntegerField(
                        default=0, help_text="显示排序，数字越小越靠前", verbose_name="排序"
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True, help_text="是否启用此分类", verbose_name="是否启用"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
            ],
            options={
                "verbose_name": "形象分类",
                "verbose_name_plural": "形象分类",
                "db_table": "avatar_categories",
                "ordering": ["sort_order", "name"],
            },
        ),
        migrations.CreateModel(
            name="AvatarTag",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        help_text="数字人形象标签名称",
                        max_length=30,
                        unique=True,
                        verbose_name="标签名称",
                    ),
                ),
                (
                    "color",
                    models.CharField(
                        default="#007bff",
                        help_text="标签显示颜色，十六进制格式",
                        max_length=7,
                        verbose_name="标签颜色",
                    ),
                ),
                (
                    "usage_count",
                    models.PositiveIntegerField(
                        default=0, help_text="此标签被使用的次数", verbose_name="使用次数"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
            ],
            options={
                "verbose_name": "形象标签",
                "verbose_name_plural": "形象标签",
                "db_table": "avatar_tags",
                "ordering": ["-usage_count", "name"],
            },
        ),
        migrations.CreateModel(
            name="DigitalAvatar",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        help_text="数字人形象的名称", max_length=100, verbose_name="形象名称"
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        blank=True, help_text="数字人形象的详细描述", verbose_name="形象描述"
                    ),
                ),
                (
                    "model_version",
                    models.CharField(
                        choices=[("V8", "V8模型"), ("V6", "V6模型"), ("V5", "V5模型")],
                        default="V8",
                        help_text="支持的数字人模型版本",
                        max_length=10,
                        verbose_name="模型版本",
                    ),
                ),
                (
                    "scene_type",
                    models.CharField(
                        choices=[
                            ("outdoor_sitting", "户外坐姿"),
                            ("sitting_half", "坐姿半身"),
                            ("outdoor_full", "户外全身"),
                            ("outdoor_standing", "户外站姿"),
                            ("standing_material", "站姿素材"),
                            ("indoor_oral", "室内口播"),
                            ("indoor_half", "室内半身"),
                            ("indoor_sitting", "室内坐姿"),
                            ("indoor_full", "室内全身"),
                            ("indoor_drama", "室内戏剧"),
                            ("green_screen_sitting", "绿幕斜坐"),
                            ("green_screen_half_standing", "绿幕半身站"),
                            ("green_screen_half_sitting", "绿幕半身坐"),
                            ("walking_talking", "边说边走"),
                            ("garden_scene", "花园场景"),
                            ("ancient_building", "古建筑"),
                            ("library_scene", "图书馆"),
                            ("restaurant_scene", "餐厅场景"),
                            ("travel_scene", "旅游景区"),
                            ("office_scene", "写字楼"),
                            ("other", "其他场景"),
                        ],
                        help_text="数字人形象的场景类型",
                        max_length=50,
                        verbose_name="场景类型",
                    ),
                ),
                (
                    "thumbnail",
                    models.ImageField(
                        help_text="数字人形象的预览图片",
                        upload_to="avatars/thumbnails/",
                        verbose_name="缩略图",
                    ),
                ),
                (
                    "video_preview",
                    models.FileField(
                        blank=True,
                        help_text="数字人形象的视频预览文件",
                        null=True,
                        upload_to="avatars/videos/",
                        verbose_name="视频预览",
                    ),
                ),
                (
                    "video_url",
                    models.URLField(
                        blank=True,
                        help_text="数字人形象的在线视频链接",
                        null=True,
                        verbose_name="视频链接",
                    ),
                ),
                (
                    "is_public",
                    models.BooleanField(
                        default=True, help_text="是否为公共数字人形象，所有用户可用", verbose_name="是否公开"
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True, help_text="是否启用此数字人形象", verbose_name="是否启用"
                    ),
                ),
                (
                    "computing_power_cost",
                    models.PositiveIntegerField(
                        default=10, help_text="使用此数字人形象生成视频所需的算力点数", verbose_name="算力消耗"
                    ),
                ),
                (
                    "sort_order",
                    models.PositiveIntegerField(
                        default=0, help_text="显示排序，数字越小越靠前", verbose_name="排序"
                    ),
                ),
                (
                    "usage_count",
                    models.PositiveIntegerField(
                        default=0, help_text="此数字人形象被使用的总次数", verbose_name="使用次数"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "categories",
                    models.ManyToManyField(
                        blank=True,
                        help_text="数字人形象所属的分类",
                        related_name="avatars",
                        to="avatars.avatarcategory",
                        verbose_name="分类",
                    ),
                ),
                (
                    "owner",
                    models.ForeignKey(
                        blank=True,
                        help_text="私有数字人形象的所有者，公共形象此字段为空",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="owned_avatars",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="所有者",
                    ),
                ),
                (
                    "tags",
                    models.ManyToManyField(
                        blank=True,
                        help_text="数字人形象的标签",
                        related_name="avatars",
                        to="avatars.avatartag",
                        verbose_name="标签",
                    ),
                ),
            ],
            options={
                "verbose_name": "数字人形象",
                "verbose_name_plural": "数字人形象",
                "db_table": "digital_avatars",
                "ordering": ["sort_order", "-created_at"],
            },
        ),
        migrations.CreateModel(
            name="AvatarUsageLog",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "computing_power_consumed",
                    models.PositiveIntegerField(
                        help_text="本次使用消耗的算力点数", verbose_name="消耗算力"
                    ),
                ),
                (
                    "purpose",
                    models.CharField(
                        blank=True,
                        help_text="使用数字人形象的目的或项目名称",
                        max_length=100,
                        verbose_name="使用目的",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="使用时间"),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="avatar_usage_logs",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="用户",
                    ),
                ),
                (
                    "avatar",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="usage_logs",
                        to="avatars.digitalavatar",
                        verbose_name="数字人形象",
                    ),
                ),
            ],
            options={
                "verbose_name": "形象使用日志",
                "verbose_name_plural": "形象使用日志",
                "db_table": "avatar_usage_logs",
                "ordering": ["-created_at"],
            },
        ),
        migrations.AddIndex(
            model_name="digitalavatar",
            index=models.Index(
                fields=["is_public", "is_active"], name="digital_ava_is_publ_14310e_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="digitalavatar",
            index=models.Index(
                fields=["model_version"], name="digital_ava_model_v_728581_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="digitalavatar",
            index=models.Index(
                fields=["scene_type"], name="digital_ava_scene_t_30dff4_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="digitalavatar",
            index=models.Index(fields=["owner"], name="digital_ava_owner_i_244b16_idx"),
        ),
        migrations.AddIndex(
            model_name="avatarusagelog",
            index=models.Index(
                fields=["avatar", "created_at"], name="avatar_usag_avatar__f8eb83_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="avatarusagelog",
            index=models.Index(
                fields=["user", "created_at"], name="avatar_usag_user_id_edc238_idx"
            ),
        ),
    ]
