# Generated by Django 5.2.4 on 2025-07-15 02:41

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="VoiceModel",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        help_text="语音模型的名称", max_length=100, verbose_name="语音名称"
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        blank=True, help_text="语音模型的详细描述", verbose_name="语音描述"
                    ),
                ),
                (
                    "language",
                    models.CharField(
                        choices=[
                            ("zh-cn", "中文（简体）"),
                            ("zh-tw", "中文（繁体）"),
                            ("en-us", "英语（美式）"),
                            ("en-gb", "英语（英式）"),
                            ("ja-jp", "日语"),
                            ("ko-kr", "韩语"),
                            ("es-es", "西班牙语"),
                            ("pt-br", "葡萄牙语"),
                            ("id-id", "印尼语"),
                            ("yue", "粤语"),
                            ("wu", "上海话"),
                            ("sichuan", "四川话"),
                            ("hunan", "湖南话"),
                            ("henan", "河南话"),
                            ("tianjin", "天津话"),
                            ("hubei", "湖北话"),
                            ("dongbei", "东北话"),
                            ("guangxi", "广西话"),
                            ("chongqing", "重庆话"),
                        ],
                        help_text="语音模型支持的语言",
                        max_length=20,
                        verbose_name="语言",
                    ),
                ),
                (
                    "gender",
                    models.CharField(
                        choices=[("male", "男声"), ("female", "女声"), ("child", "童声")],
                        help_text="语音的性别特征",
                        max_length=10,
                        verbose_name="性别",
                    ),
                ),
                (
                    "voice_type",
                    models.CharField(
                        choices=[
                            ("standard", "标准"),
                            ("news", "新闻播报"),
                            ("magnetic", "磁性"),
                            ("sweet", "甜美"),
                            ("lively", "活泼"),
                            ("gentle", "温柔"),
                            ("intellectual", "知性"),
                            ("cute", "可爱"),
                            ("dramatic", "戏剧"),
                            ("audiobook", "有声阅读"),
                            ("education", "教育"),
                            ("customer_service", "客服"),
                            ("dialect", "方言"),
                        ],
                        default="standard",
                        help_text="语音的风格类型",
                        max_length=30,
                        verbose_name="语音类型",
                    ),
                ),
                (
                    "audio_sample",
                    models.FileField(
                        blank=True,
                        help_text="语音模型的音频样本文件",
                        null=True,
                        upload_to="voices/samples/",
                        verbose_name="音频样本",
                    ),
                ),
                (
                    "audio_sample_url",
                    models.URLField(
                        blank=True,
                        help_text="语音模型的在线音频样本链接",
                        null=True,
                        verbose_name="音频样本链接",
                    ),
                ),
                (
                    "is_cloned",
                    models.BooleanField(
                        default=False, help_text="是否为用户克隆的语音", verbose_name="是否为克隆语音"
                    ),
                ),
                (
                    "is_public",
                    models.BooleanField(
                        default=True, help_text="是否为公共语音模型，所有用户可用", verbose_name="是否公开"
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True, help_text="是否启用此语音模型", verbose_name="是否启用"
                    ),
                ),
                (
                    "computing_power_cost",
                    models.PositiveIntegerField(
                        default=5,
                        help_text="使用此语音模型合成语音所需的算力点数（每分钟）",
                        verbose_name="算力消耗",
                    ),
                ),
                (
                    "sort_order",
                    models.PositiveIntegerField(
                        default=0, help_text="显示排序，数字越小越靠前", verbose_name="排序"
                    ),
                ),
                (
                    "usage_count",
                    models.PositiveIntegerField(
                        default=0, help_text="此语音模型被使用的总次数", verbose_name="使用次数"
                    ),
                ),
                (
                    "default_volume",
                    models.FloatField(
                        default=50.0, help_text="默认音量大小（0-100）", verbose_name="默认音量"
                    ),
                ),
                (
                    "default_pitch",
                    models.FloatField(
                        default=1.0, help_text="默认音调高低（0-2）", verbose_name="默认音调"
                    ),
                ),
                (
                    "default_speed",
                    models.FloatField(
                        default=1.0, help_text="默认语速快慢（0-2）", verbose_name="默认语速"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "owner",
                    models.ForeignKey(
                        blank=True,
                        help_text="私有语音模型的所有者，公共语音此字段为空",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="owned_voices",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="所有者",
                    ),
                ),
            ],
            options={
                "verbose_name": "语音模型",
                "verbose_name_plural": "语音模型",
                "db_table": "voice_models",
                "ordering": ["sort_order", "-created_at"],
            },
        ),
        migrations.CreateModel(
            name="VoiceCloneRequest",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        help_text="克隆语音的名称", max_length=100, verbose_name="语音名称"
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        blank=True, help_text="克隆语音的描述", verbose_name="语音描述"
                    ),
                ),
                (
                    "source_audio",
                    models.FileField(
                        help_text="用于克隆的源音频文件",
                        upload_to="voices/clone_sources/",
                        verbose_name="源音频文件",
                    ),
                ),
                (
                    "audio_duration",
                    models.PositiveIntegerField(
                        blank=True,
                        help_text="源音频文件的时长（秒）",
                        null=True,
                        verbose_name="音频时长",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "待处理"),
                            ("processing", "处理中"),
                            ("completed", "已完成"),
                            ("failed", "失败"),
                            ("cancelled", "已取消"),
                        ],
                        default="pending",
                        help_text="语音克隆的处理状态",
                        max_length=20,
                        verbose_name="处理状态",
                    ),
                ),
                (
                    "progress",
                    models.PositiveIntegerField(
                        default=0, help_text="处理进度百分比（0-100）", verbose_name="处理进度"
                    ),
                ),
                (
                    "error_message",
                    models.TextField(
                        blank=True, help_text="处理失败时的错误信息", verbose_name="错误信息"
                    ),
                ),
                (
                    "computing_power_consumed",
                    models.PositiveIntegerField(
                        default=0, help_text="本次克隆消耗的算力点数", verbose_name="消耗算力"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "completed_at",
                    models.DateTimeField(
                        blank=True, help_text="克隆完成的时间", null=True, verbose_name="完成时间"
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="voice_clone_requests",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="用户",
                    ),
                ),
                (
                    "result_voice",
                    models.ForeignKey(
                        blank=True,
                        help_text="克隆成功后生成的语音模型",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="clone_requests",
                        to="voices.voicemodel",
                        verbose_name="生成的语音模型",
                    ),
                ),
            ],
            options={
                "verbose_name": "语音克隆请求",
                "verbose_name_plural": "语音克隆请求",
                "db_table": "voice_clone_requests",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="VoiceSynthesisTask",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "text_content",
                    models.TextField(help_text="需要合成语音的文本内容", verbose_name="文本内容"),
                ),
                (
                    "volume",
                    models.FloatField(
                        default=50.0, help_text="音量大小（0-100）", verbose_name="音量"
                    ),
                ),
                (
                    "pitch",
                    models.FloatField(
                        default=1.0, help_text="音调高低（0-2）", verbose_name="音调"
                    ),
                ),
                (
                    "speed",
                    models.FloatField(
                        default=1.0, help_text="语速快慢（0-2）", verbose_name="语速"
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "待处理"),
                            ("processing", "处理中"),
                            ("completed", "已完成"),
                            ("failed", "失败"),
                            ("cancelled", "已取消"),
                        ],
                        default="pending",
                        help_text="语音合成的处理状态",
                        max_length=20,
                        verbose_name="处理状态",
                    ),
                ),
                (
                    "progress",
                    models.PositiveIntegerField(
                        default=0, help_text="处理进度百分比（0-100）", verbose_name="处理进度"
                    ),
                ),
                (
                    "error_message",
                    models.TextField(
                        blank=True, help_text="处理失败时的错误信息", verbose_name="错误信息"
                    ),
                ),
                (
                    "result_audio",
                    models.FileField(
                        blank=True,
                        help_text="合成生成的音频文件",
                        null=True,
                        upload_to="voices/synthesis_results/",
                        verbose_name="合成音频",
                    ),
                ),
                (
                    "result_audio_url",
                    models.URLField(
                        blank=True,
                        help_text="合成音频的在线链接",
                        null=True,
                        verbose_name="合成音频链接",
                    ),
                ),
                (
                    "audio_duration",
                    models.PositiveIntegerField(
                        blank=True,
                        help_text="合成音频的时长（秒）",
                        null=True,
                        verbose_name="音频时长",
                    ),
                ),
                (
                    "computing_power_consumed",
                    models.PositiveIntegerField(
                        default=0, help_text="本次合成消耗的算力点数", verbose_name="消耗算力"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "completed_at",
                    models.DateTimeField(
                        blank=True, help_text="合成完成的时间", null=True, verbose_name="完成时间"
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="voice_synthesis_tasks",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="用户",
                    ),
                ),
                (
                    "voice_model",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="synthesis_tasks",
                        to="voices.voicemodel",
                        verbose_name="语音模型",
                    ),
                ),
            ],
            options={
                "verbose_name": "语音合成任务",
                "verbose_name_plural": "语音合成任务",
                "db_table": "voice_synthesis_tasks",
                "ordering": ["-created_at"],
            },
        ),
        migrations.AddIndex(
            model_name="voicemodel",
            index=models.Index(
                fields=["language", "gender"], name="voice_model_languag_4342df_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="voicemodel",
            index=models.Index(
                fields=["is_public", "is_active"], name="voice_model_is_publ_1e12aa_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="voicemodel",
            index=models.Index(
                fields=["voice_type"], name="voice_model_voice_t_ffa593_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="voicemodel",
            index=models.Index(fields=["owner"], name="voice_model_owner_i_d9634c_idx"),
        ),
        migrations.AddIndex(
            model_name="voiceclonerequest",
            index=models.Index(
                fields=["user", "status"], name="voice_clone_user_id_54ca5d_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="voiceclonerequest",
            index=models.Index(
                fields=["status", "created_at"], name="voice_clone_status_db086e_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="voicesynthesistask",
            index=models.Index(
                fields=["user", "status"], name="voice_synth_user_id_5ebea4_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="voicesynthesistask",
            index=models.Index(
                fields=["voice_model", "status"], name="voice_synth_voice_m_dd7abe_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="voicesynthesistask",
            index=models.Index(
                fields=["status", "created_at"], name="voice_synth_status_8987ac_idx"
            ),
        ),
    ]
