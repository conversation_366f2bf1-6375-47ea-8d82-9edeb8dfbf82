"""
控制台数据模型
"""
from django.db import models
from django.utils.translation import gettext_lazy as _
import json


class SystemStatistics(models.Model):
    """系统统计数据"""
    
    # 用户统计
    total_users = models.IntegerField(_('总用户数'), default=0)
    active_users = models.IntegerField(_('活跃用户数'), default=0)
    new_users_today = models.IntegerField(_('今日新增用户'), default=0)
    
    # 数字人统计
    total_avatars = models.IntegerField(_('总数字人数'), default=0)
    public_avatars = models.IntegerField(_('公共数字人数'), default=0)
    private_avatars = models.IntegerField(_('私有数字人数'), default=0)
    
    # 视频统计
    total_videos = models.IntegerField(_('总视频数'), default=0)
    videos_today = models.IntegerField(_('今日生成视频'), default=0)
    
    # 算力统计
    total_computing_power = models.BigIntegerField(_('总算力消耗'), default=0)
    computing_power_today = models.IntegerField(_('今日算力消耗'), default=0)
    
    # 收入统计
    total_revenue = models.DecimalField(_('总收入'), max_digits=12, decimal_places=2, default=0)
    revenue_today = models.DecimalField(_('今日收入'), max_digits=10, decimal_places=2, default=0)
    
    # 套餐统计
    active_packages = models.IntegerField(_('活跃套餐数'), default=0)
    
    # 更新时间
    updated_at = models.DateTimeField(_('更新时间'), auto_now=True)
    
    class Meta:
        verbose_name = _('系统统计')
        verbose_name_plural = _('系统统计')
        
    def __str__(self):
        return f'系统统计 - {self.updated_at.strftime("%Y-%m-%d %H:%M")}'


class DailyStatistics(models.Model):
    """每日统计数据"""
    
    date = models.DateField(_('日期'), unique=True)
    
    # 用户数据
    new_users = models.IntegerField(_('新增用户'), default=0)
    active_users = models.IntegerField(_('活跃用户'), default=0)
    
    # 业务数据
    videos_created = models.IntegerField(_('生成视频数'), default=0)
    avatars_created = models.IntegerField(_('创建数字人数'), default=0)
    computing_power_consumed = models.IntegerField(_('算力消耗'), default=0)
    
    # 收入数据
    revenue = models.DecimalField(_('收入'), max_digits=10, decimal_places=2, default=0)
    packages_sold = models.IntegerField(_('套餐销售数'), default=0)
    
    created_at = models.DateTimeField(_('创建时间'), auto_now_add=True)
    
    class Meta:
        verbose_name = _('每日统计')
        verbose_name_plural = _('每日统计')
        ordering = ['-date']
        
    def __str__(self):
        return f'{self.date} 统计数据'


class SystemMonitor(models.Model):
    """系统监控数据"""

    MONITOR_TYPE_CHOICES = [
        ('cpu', _('CPU使用率')),
        ('memory', _('内存使用率')),
        ('disk', _('磁盘使用率')),
        ('network', _('网络流量')),
        ('database', _('数据库连接')),
        ('redis', _('Redis状态')),
        ('celery', _('Celery队列')),
    ]

    monitor_type = models.CharField(_('监控类型'), max_length=20, choices=MONITOR_TYPE_CHOICES)
    value = models.FloatField(_('监控值'))
    unit = models.CharField(_('单位'), max_length=10, default='%')
    status = models.CharField(_('状态'), max_length=10, choices=[
        ('normal', _('正常')),
        ('warning', _('警告')),
        ('critical', _('严重')),
    ], default='normal')
    message = models.TextField(_('详细信息'), blank=True)
    created_at = models.DateTimeField(_('记录时间'), auto_now_add=True)

    class Meta:
        verbose_name = _('系统监控')
        verbose_name_plural = _('系统监控')
        ordering = ['-created_at']

    def __str__(self):
        return f'{self.get_monitor_type_display()}: {self.value}{self.unit}'


class SystemLog(models.Model):
    """系统日志"""

    LOG_LEVEL_CHOICES = [
        ('debug', _('调试')),
        ('info', _('信息')),
        ('warning', _('警告')),
        ('error', _('错误')),
        ('critical', _('严重错误')),
    ]

    level = models.CharField(_('日志级别'), max_length=10, choices=LOG_LEVEL_CHOICES)
    module = models.CharField(_('模块'), max_length=50)
    message = models.TextField(_('日志消息'))
    extra_data = models.TextField(_('额外数据'), blank=True, help_text='JSON格式的额外数据')
    user_id = models.IntegerField(_('用户ID'), null=True, blank=True)
    ip_address = models.GenericIPAddressField(_('IP地址'), null=True, blank=True)
    user_agent = models.TextField(_('用户代理'), blank=True)
    created_at = models.DateTimeField(_('创建时间'), auto_now_add=True)

    class Meta:
        verbose_name = _('系统日志')
        verbose_name_plural = _('系统日志')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['level', 'created_at']),
            models.Index(fields=['module', 'created_at']),
        ]

    def __str__(self):
        return f'[{self.level.upper()}] {self.module}: {self.message[:50]}'

    def get_extra_data_dict(self):
        """获取额外数据的字典格式"""
        if self.extra_data:
            try:
                return json.loads(self.extra_data)
            except json.JSONDecodeError:
                return {}
        return {}

    def set_extra_data_dict(self, data):
        """设置额外数据"""
        self.extra_data = json.dumps(data, ensure_ascii=False)


class ServiceStatus(models.Model):
    """服务状态监控"""

    SERVICE_CHOICES = [
        ('django', _('Django应用')),
        ('database', _('数据库')),
        ('redis', _('Redis缓存')),
        ('celery_worker', _('Celery Worker')),
        ('celery_beat', _('Celery Beat')),
        ('nginx', _('Nginx')),
        ('ai_service', _('AI服务')),
    ]

    STATUS_CHOICES = [
        ('online', _('在线')),
        ('offline', _('离线')),
        ('error', _('错误')),
        ('maintenance', _('维护中')),
    ]

    service_name = models.CharField(_('服务名称'), max_length=20, choices=SERVICE_CHOICES, unique=True)
    status = models.CharField(_('状态'), max_length=20, choices=STATUS_CHOICES, default='offline')
    response_time = models.FloatField(_('响应时间(ms)'), null=True, blank=True)
    error_message = models.TextField(_('错误信息'), blank=True)
    last_check = models.DateTimeField(_('最后检查时间'), auto_now=True)
    uptime_percentage = models.FloatField(_('可用性百分比'), default=0.0)

    class Meta:
        verbose_name = _('服务状态')
        verbose_name_plural = _('服务状态')

    def __str__(self):
        return f'{self.get_service_name_display()}: {self.get_status_display()}'
