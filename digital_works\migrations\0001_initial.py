# Generated by Django 5.2.4 on 2025-07-16 08:25

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="WorkTemplate",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=200, verbose_name="模板名称")),
                ("description", models.TextField(blank=True, verbose_name="模板描述")),
                ("thumbnail", models.URLField(blank=True, verbose_name="模板缩略图")),
                ("preview_video", models.URLField(blank=True, verbose_name="预览视频")),
                (
                    "default_avatar",
                    models.CharField(blank=True, max_length=100, verbose_name="默认数字人"),
                ),
                (
                    "default_voice",
                    models.CharField(blank=True, max_length=100, verbose_name="默认声音"),
                ),
                ("default_script", models.TextField(blank=True, verbose_name="默认文案")),
                (
                    "category",
                    models.CharField(blank=True, max_length=50, verbose_name="分类"),
                ),
                ("tags", models.JSONField(default=list, verbose_name="标签")),
                ("is_active", models.BooleanField(default=True, verbose_name="是否启用")),
                (
                    "is_premium",
                    models.BooleanField(default=False, verbose_name="是否付费模板"),
                ),
                ("sort_order", models.IntegerField(default=0, verbose_name="排序")),
                ("usage_count", models.IntegerField(default=0, verbose_name="使用次数")),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
            ],
            options={
                "verbose_name": "作品模板",
                "verbose_name_plural": "作品模板",
                "db_table": "work_templates",
                "ordering": ["sort_order", "-created_at"],
            },
        ),
        migrations.CreateModel(
            name="DigitalWork",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=200, verbose_name="作品名称")),
                ("description", models.TextField(blank=True, verbose_name="作品描述")),
                ("thumbnail", models.URLField(blank=True, verbose_name="缩略图")),
                ("video_url", models.URLField(blank=True, verbose_name="视频地址")),
                (
                    "duration",
                    models.CharField(blank=True, max_length=20, verbose_name="视频时长"),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("processing", "生成中"),
                            ("completed", "已完成"),
                            ("failed", "失败"),
                            ("pending", "等待中"),
                        ],
                        default="pending",
                        max_length=20,
                        verbose_name="状态",
                    ),
                ),
                (
                    "avatar_name",
                    models.CharField(
                        blank=True, max_length=100, verbose_name="使用的数字人形象"
                    ),
                ),
                (
                    "voice_name",
                    models.CharField(blank=True, max_length=100, verbose_name="使用的声音"),
                ),
                ("script_content", models.TextField(blank=True, verbose_name="文案内容")),
                (
                    "computing_power_cost",
                    models.IntegerField(default=0, verbose_name="消耗算力"),
                ),
                (
                    "generation_time",
                    models.FloatField(default=0.0, verbose_name="生成耗时(秒)"),
                ),
                (
                    "file_size",
                    models.BigIntegerField(default=0, verbose_name="文件大小(字节)"),
                ),
                (
                    "resolution",
                    models.CharField(blank=True, max_length=20, verbose_name="分辨率"),
                ),
                (
                    "frame_rate",
                    models.CharField(blank=True, max_length=10, verbose_name="帧率"),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "completed_at",
                    models.DateTimeField(blank=True, null=True, verbose_name="完成时间"),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="创建用户",
                    ),
                ),
            ],
            options={
                "verbose_name": "数字人作品",
                "verbose_name_plural": "数字人作品",
                "db_table": "digital_works",
                "ordering": ["-created_at"],
            },
        ),
    ]
