"""
语音合成服务 - 集成第三方API
"""
import logging
import requests
import json
from typing import Dict, Any, Optional
from django.conf import settings

logger = logging.getLogger(__name__)


class VoiceSynthesisService:
    """语音合成服务"""
    
    def __init__(self):
        self.azure_config = None
        self.elevenlabs_config = None
        self._load_configs()
    
    def _load_configs(self):
        """加载配置"""
        try:
            # 延迟导入避免循环依赖
            from apps.config.services import api_config
            self.azure_config = api_config.get_azure_speech_config()
            self.elevenlabs_config = api_config.get_elevenlabs_config()
            logger.info("语音合成服务配置加载成功")
        except Exception as e:
            logger.error(f"语音合成服务配置加载失败: {e}")
            # 使用默认配置
            self.azure_config = {}
            self.elevenlabs_config = {}
    
    def synthesize_with_azure(self, text: str, voice_name: str = "zh-CN-XiaoxiaoNeural", 
                             volume: float = 50.0, pitch: float = 1.0, speed: float = 1.0) -> Dict[str, Any]:
        """使用Azure语音服务合成"""
        try:
            if not self.azure_config or not self.azure_config.get('api_key'):
                return {
                    'success': False,
                    'error': 'Azure语音服务未配置',
                    'fallback': True
                }
            
            # Azure语音合成API调用
            region = self.azure_config.get('region', 'eastasia')
            api_key = self.azure_config['api_key']
            
            # 构建SSML
            ssml = f"""
            <speak version='1.0' xml:lang='zh-CN'>
                <voice xml:lang='zh-CN' name='{voice_name}'>
                    <prosody rate='{speed}' pitch='{pitch}%' volume='{volume}%'>
                        {text}
                    </prosody>
                </voice>
            </speak>
            """
            
            headers = {
                'Ocp-Apim-Subscription-Key': api_key,
                'Content-Type': 'application/ssml+xml',
                'X-Microsoft-OutputFormat': 'audio-16khz-128kbitrate-mono-mp3',
                'User-Agent': 'AI-Digital-Human-Platform'
            }
            
            url = f"https://{region}.tts.speech.microsoft.com/cognitiveservices/v1"
            
            response = requests.post(url, headers=headers, data=ssml.encode('utf-8'), timeout=30)
            
            if response.status_code == 200:
                # 这里应该保存音频文件到存储服务
                # 暂时返回成功状态
                return {
                    'success': True,
                    'audio_data': response.content,
                    'audio_url': f'/media/voice_synthesis/azure_{hash(text)}.mp3',
                    'provider': 'azure',
                    'voice_name': voice_name
                }
            else:
                logger.error(f"Azure语音合成失败: {response.status_code} - {response.text}")
                return {
                    'success': False,
                    'error': f'Azure API错误: {response.status_code}',
                    'fallback': True
                }
                
        except Exception as e:
            logger.error(f"Azure语音合成异常: {e}")
            return {
                'success': False,
                'error': str(e),
                'fallback': True
            }
    
    def synthesize_with_elevenlabs(self, text: str, voice_id: str = "21m00Tcm4TlvDq8ikWAM", 
                                  volume: float = 50.0, pitch: float = 1.0, speed: float = 1.0) -> Dict[str, Any]:
        """使用ElevenLabs语音服务合成"""
        try:
            if not self.elevenlabs_config or not self.elevenlabs_config.get('api_key'):
                return {
                    'success': False,
                    'error': 'ElevenLabs语音服务未配置',
                    'fallback': True
                }
            
            api_key = self.elevenlabs_config['api_key']
            
            headers = {
                'Accept': 'audio/mpeg',
                'Content-Type': 'application/json',
                'xi-api-key': api_key
            }
            
            data = {
                'text': text,
                'model_id': 'eleven_multilingual_v2',
                'voice_settings': {
                    'stability': 0.5,
                    'similarity_boost': 0.5,
                    'style': 0.0,
                    'use_speaker_boost': True
                }
            }
            
            url = f"https://api.elevenlabs.io/v1/text-to-speech/{voice_id}"
            
            response = requests.post(url, json=data, headers=headers, timeout=30)
            
            if response.status_code == 200:
                return {
                    'success': True,
                    'audio_data': response.content,
                    'audio_url': f'/media/voice_synthesis/elevenlabs_{hash(text)}.mp3',
                    'provider': 'elevenlabs',
                    'voice_id': voice_id
                }
            else:
                logger.error(f"ElevenLabs语音合成失败: {response.status_code} - {response.text}")
                return {
                    'success': False,
                    'error': f'ElevenLabs API错误: {response.status_code}',
                    'fallback': True
                }
                
        except Exception as e:
            logger.error(f"ElevenLabs语音合成异常: {e}")
            return {
                'success': False,
                'error': str(e),
                'fallback': True
            }
    
    def synthesize_voice(self, text: str, voice_model, **kwargs) -> Dict[str, Any]:
        """语音合成主方法"""
        try:
            # 根据语音模型选择合适的服务
            provider = voice_model.provider if hasattr(voice_model, 'provider') else 'azure'
            
            if provider == 'azure' or not provider:
                result = self.synthesize_with_azure(
                    text=text,
                    voice_name=getattr(voice_model, 'azure_voice_name', 'zh-CN-XiaoxiaoNeural'),
                    volume=kwargs.get('volume', 50.0),
                    pitch=kwargs.get('pitch', 1.0),
                    speed=kwargs.get('speed', 1.0)
                )
            elif provider == 'elevenlabs':
                result = self.synthesize_with_elevenlabs(
                    text=text,
                    voice_id=getattr(voice_model, 'elevenlabs_voice_id', '21m00Tcm4TlvDq8ikWAM'),
                    volume=kwargs.get('volume', 50.0),
                    pitch=kwargs.get('pitch', 1.0),
                    speed=kwargs.get('speed', 1.0)
                )
            else:
                result = {
                    'success': False,
                    'error': f'不支持的语音服务提供商: {provider}',
                    'fallback': True
                }
            
            # 如果第三方服务失败，使用本地模拟
            if not result['success'] and result.get('fallback'):
                logger.warning(f"第三方语音服务失败，使用本地模拟: {result.get('error')}")
                result = self._generate_fallback_audio(text, voice_model)
            
            return result
            
        except Exception as e:
            logger.error(f"语音合成失败: {e}")
            return self._generate_fallback_audio(text, voice_model)
    
    def _generate_fallback_audio(self, text: str, voice_model) -> Dict[str, Any]:
        """生成本地模拟音频"""
        return {
            'success': True,
            'audio_url': f'/static/audio/sample_voice.mp3',
            'provider': 'local_fallback',
            'voice_name': voice_model.name,
            'is_fallback': True,
            'message': '使用本地模拟音频（第三方服务不可用）'
        }


# 创建全局实例
voice_synthesis_service = VoiceSynthesisService()


def synthesize_voice(text: str, voice_model, **kwargs) -> Dict[str, Any]:
    """语音合成便捷函数"""
    return voice_synthesis_service.synthesize_voice(text, voice_model, **kwargs)
