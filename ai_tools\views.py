from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import SearchFilter, OrderingFilter
from .models import Tool<PERSON>ategory, AITool, ToolUsageLog
from .serializers import (
    ToolCategorySerializer, AIToolSerializer,
    ToolUsageLogSerializer, ToolUsageCreateSerializer
)


class ToolCategoryViewSet(viewsets.ReadOnlyModelViewSet):
    """工具分类视图集"""
    queryset = ToolCategory.objects.filter(is_active=True)
    serializer_class = ToolCategorySerializer
    permission_classes = [permissions.AllowAny]  # 允许所有用户访问
    ordering = ['sort_order', 'id']


class AIToolViewSet(viewsets.ReadOnlyModelViewSet):
    """AI工具视图集"""
    queryset = AITool.objects.select_related('category').all()
    serializer_class = AIToolSerializer
    permission_classes = [permissions.AllowAny]  # 允许所有用户访问
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['category', 'status', 'is_new', 'is_pro']
    search_fields = ['name', 'description', 'tags']
    ordering_fields = ['sort_order', 'usage_count', 'rating', 'created_at']
    ordering = ['sort_order', 'id']

    @action(detail=False, methods=['get'])
    def by_category(self, request):
        """按分类获取工具"""
        category_key = request.query_params.get('category')
        if not category_key or category_key == 'all':
            tools = self.get_queryset()
        else:
            tools = self.get_queryset().filter(category__key=category_key)

        page = self.paginate_queryset(tools)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(tools, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def increment_usage(self, request, pk=None):
        """增加工具使用次数"""
        tool = self.get_object()
        tool.usage_count += 1
        tool.save(update_fields=['usage_count'])
        return Response({'usage_count': tool.usage_count})


class ToolUsageLogViewSet(viewsets.ModelViewSet):
    """工具使用记录视图集"""
    serializer_class = ToolUsageLogSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, OrderingFilter]
    filterset_fields = ['tool', 'status']
    ordering = ['-created_at']

    def get_queryset(self):
        return ToolUsageLog.objects.filter(user=self.request.user).select_related('tool', 'user')

    def get_serializer_class(self):
        if self.action == 'create':
            return ToolUsageCreateSerializer
        return ToolUsageLogSerializer

    @action(detail=False, methods=['get'])
    def statistics(self, request):
        """获取使用统计"""
        queryset = self.get_queryset()
        total_usage = queryset.count()
        total_cost = sum(log.cost_points for log in queryset)

        # 按工具统计
        tool_stats = {}
        for log in queryset:
            tool_name = log.tool.name
            if tool_name not in tool_stats:
                tool_stats[tool_name] = {'count': 0, 'cost': 0}
            tool_stats[tool_name]['count'] += 1
            tool_stats[tool_name]['cost'] += log.cost_points

        return Response({
            'total_usage': total_usage,
            'total_cost': total_cost,
            'tool_statistics': tool_stats
        })
