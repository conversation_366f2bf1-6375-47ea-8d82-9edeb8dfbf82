"""
数字人形象数据初始化命令
基于原项目 https://hm.umi6.com/ 的真实数据
"""

from django.core.management.base import BaseCommand
from django.db import transaction
from apps.avatars.models import DigitalAvatar, AvatarCategory, AvatarTag


class Command(BaseCommand):
    help = '初始化数字人形象数据'

    def add_arguments(self, parser):
        parser.add_argument(
            '--clear',
            action='store_true',
            help='清除现有数据后重新初始化',
        )

    def handle(self, *args, **options):
        if options['clear']:
            self.stdout.write('清除现有数据...')
            DigitalAvatar.objects.all().delete()
            AvatarCategory.objects.all().delete()

        with transaction.atomic():
            self.create_categories()
            self.create_avatars()

        self.stdout.write(
            self.style.SUCCESS('数字人形象数据初始化完成！')
        )

    def create_categories(self):
        """创建分类数据"""
        categories_data = [
            {'name': '户外', 'description': '户外场景数字人形象'},
            {'name': '室内', 'description': '室内场景数字人形象'},
            {'name': '希希', 'description': '希希系列数字人形象'},
            {'name': '通用', 'description': '通用场景数字人形象'},
            {'name': '艺术', 'description': '艺术风格数字人形象'},
            {'name': '商务', 'description': '商务场景数字人形象'},
            {'name': '教育', 'description': '教育场景数字人形象'},
            {'name': '娱乐', 'description': '娱乐场景数字人形象'},
            {'name': '新闻', 'description': '新闻播报数字人形象'},
            {'name': '生活', 'description': '生活场景数字人形象'},
            {'name': '坐姿', 'description': '坐姿类型数字人形象'},
            {'name': '站姿', 'description': '站姿类型数字人形象'},
            {'name': '全身', 'description': '全身类型数字人形象'},
            {'name': '半身', 'description': '半身类型数字人形象'},
            {'name': '花园', 'description': '花园场景数字人形象'},
            {'name': '油画', 'description': '油画风格数字人形象'},
            {'name': '特色', 'description': '特色场景数字人形象'},
            {'name': '动态', 'description': '动态类型数字人形象'},
            {'name': '旅游', 'description': '旅游场景数字人形象'},
            {'name': '文化', 'description': '文化场景数字人形象'},
            {'name': '口播', 'description': '口播类型数字人形象'},
            {'name': '运动', 'description': '运动场景数字人形象'},
            {'name': '介绍', 'description': '介绍类型数字人形象'},
        ]

        for category_data in categories_data:
            category, created = AvatarCategory.objects.get_or_create(
                name=category_data['name'],
                defaults=category_data
            )
            if created:
                self.stdout.write(f'创建分类: {category.name}')

    def create_avatars(self):
        """创建数字人形象数据 - 基于原项目的完整数据"""
        avatars_data = [
            # 户外场景系列
            {
                'name': '户外坐姿',
                'description': '自然户外环境下的坐姿形象，适合休闲和生活类内容',
                'model_version': 'V8模型/V6模型/V5模型',
                'scene_type': 'outdoor',
                'computing_power_cost': 10,
                'is_public': True,
                'is_active': True,
                'sort_order': 1,
                'categories': ['户外', '坐姿'],
                'tags': ['自然', '休闲', '户外']
            },
            {
                'name': '坐姿半身',
                'description': '半身坐姿形象，适合访谈和对话类内容',
                'model_version': 'V8模型/V6模型/V5模型',
                'scene_type': 'indoor',
                'computing_power_cost': 8,
                'is_public': True,
                'is_active': True,
                'sort_order': 2,
                'categories': ['室内', '坐姿'],
                'tags': ['半身', '访谈', '对话']
            },
            {
                'name': '户外全身',
                'description': '户外全身站立形象，适合展示和介绍类内容',
                'model_version': 'V8模型/V6模型/V5模型',
                'scene_type': 'outdoor',
                'computing_power_cost': 12,
                'is_public': True,
                'is_active': True,
                'sort_order': 3,
                'categories': ['户外', '全身'],
                'tags': ['全身', '展示', '介绍']
            },
            {
                'name': '户外站姿',
                'description': '户外正式站姿形象，适合商务和正式场合',
                'model_version': 'V8模型/V6模型/V5模型',
                'scene_type': 'outdoor',
                'computing_power_cost': 10,
                'is_public': True,
                'is_active': True,
                'sort_order': 4,
                'categories': ['户外', '站姿'],
                'tags': ['正式', '商务', '站姿']
            },
            {
                'name': '站姿素材',
                'description': '通用站姿素材，适合多种场景使用',
                'model_version': 'V8模型/V6模型/V5模型',
                'scene_type': 'general',
                'computing_power_cost': 8,
                'is_public': True,
                'is_active': True,
                'sort_order': 5,
                'categories': ['通用', '站姿'],
                'tags': ['通用', '素材', '站姿']
            },
            # 希希系列
            {
                'name': '希希花园',
                'description': '希希在花园场景中的形象，适合生活和休闲内容',
                'model_version': 'V8模型/V6模型/V5模型',
                'scene_type': 'outdoor',
                'computing_power_cost': 10,
                'is_public': True,
                'is_active': True,
                'sort_order': 6,
                'categories': ['希希', '花园'],
                'tags': ['希希', '花园', '生活']
            },
            {
                'name': '坐着素材',
                'description': '坐着姿势的通用素材',
                'model_version': 'V8模型/V6模型/V5模型',
                'scene_type': 'general',
                'computing_power_cost': 8,
                'is_public': True,
                'is_active': True,
                'sort_order': 7,
                'categories': ['通用', '坐姿'],
                'tags': ['坐姿', '素材', '通用']
            },
            {
                'name': '介绍素材',
                'description': '适合介绍和展示的通用素材',
                'model_version': 'V8模型/V6模型/V5模型',
                'scene_type': 'general',
                'computing_power_cost': 8,
                'is_public': True,
                'is_active': True,
                'sort_order': 8,
                'categories': ['通用', '介绍'],
                'tags': ['介绍', '展示', '素材']
            },
            {
                'name': '油画背景',
                'description': '油画风格背景的艺术形象',
                'model_version': 'V8模型/V6模型/V5模型',
                'scene_type': 'artistic',
                'computing_power_cost': 12,
                'is_public': True,
                'is_active': True,
                'sort_order': 9,
                'categories': ['艺术', '油画'],
                'tags': ['油画', '艺术', '背景']
            },
            {
                'name': '油画素材',
                'description': '油画风格的艺术素材',
                'model_version': 'V8模型/V6模型/V5模型',
                'scene_type': 'artistic',
                'computing_power_cost': 12,
                'is_public': True,
                'is_active': True,
                'sort_order': 10,
                'categories': ['艺术', '油画'],
                'tags': ['油画', '艺术', '素材']
            },
            # 更多场景系列
            {
                'name': '唐人街素材',
                'description': '唐人街场景的特色素材',
                'model_version': 'V8模型/V6模型/V5模型',
                'scene_type': 'outdoor',
                'computing_power_cost': 10,
                'is_public': True,
                'is_active': True,
                'sort_order': 11,
                'categories': ['户外', '特色'],
                'tags': ['唐人街', '特色', '文化']
            },
            {
                'name': '景区走动',
                'description': '景区中走动的动态形象',
                'model_version': 'V8模型/V6模型/V5模型',
                'scene_type': 'outdoor',
                'computing_power_cost': 12,
                'is_public': True,
                'is_active': True,
                'sort_order': 12,
                'categories': ['户外', '动态'],
                'tags': ['景区', '走动', '旅游']
            },
            {
                'name': '景区半身',
                'description': '景区中的半身形象',
                'model_version': 'V8模型/V6模型/V5模型',
                'scene_type': 'outdoor',
                'computing_power_cost': 8,
                'is_public': True,
                'is_active': True,
                'sort_order': 13,
                'categories': ['户外', '半身'],
                'tags': ['景区', '半身', '旅游']
            },
            {
                'name': '旅游景区',
                'description': '旅游景区的专业形象',
                'model_version': 'V8模型/V6模型/V5模型',
                'scene_type': 'outdoor',
                'computing_power_cost': 10,
                'is_public': True,
                'is_active': True,
                'sort_order': 14,
                'categories': ['户外', '旅游'],
                'tags': ['旅游', '景区', '介绍']
            },
            {
                'name': '文博宫',
                'description': '文博宫场景的文化形象',
                'model_version': 'V6模型/V5模型',
                'scene_type': 'indoor',
                'computing_power_cost': 10,
                'is_public': True,
                'is_active': True,
                'sort_order': 15,
                'categories': ['室内', '文化'],
                'tags': ['文博', '文化', '历史']
            },
            # 室内系列
            {
                'name': '室内口播',
                'description': '室内口播专用形象',
                'model_version': 'V6模型/V5模型',
                'scene_type': 'indoor',
                'computing_power_cost': 8,
                'is_public': True,
                'is_active': True,
                'sort_order': 16,
                'categories': ['室内', '口播'],
                'tags': ['口播', '室内', '播报']
            },
            {
                'name': '室内半身',
                'description': '室内半身形象',
                'model_version': 'V6模型/V5模型',
                'scene_type': 'indoor',
                'computing_power_cost': 6,
                'is_public': True,
                'is_active': True,
                'sort_order': 17,
                'categories': ['室内', '半身'],
                'tags': ['室内', '半身', '对话']
            },
            {
                'name': '健身爬梯',
                'description': '健身爬梯的运动形象',
                'model_version': 'V6模型/V5模型',
                'scene_type': 'indoor',
                'computing_power_cost': 10,
                'is_public': True,
                'is_active': True,
                'sort_order': 18,
                'categories': ['室内', '运动'],
                'tags': ['健身', '运动', '爬梯']
            },
            {
                'name': '室内坐姿',
                'description': '室内坐姿形象',
                'model_version': 'V6模型/V5模型',
                'scene_type': 'indoor',
                'computing_power_cost': 6,
                'is_public': True,
                'is_active': True,
                'sort_order': 19,
                'categories': ['室内', '坐姿'],
                'tags': ['室内', '坐姿', '休闲']
            },
            {
                'name': '边说边走',
                'description': '边说边走的动态形象',
                'model_version': 'V8模型/V6模型/V5模型',
                'scene_type': 'general',
                'computing_power_cost': 12,
                'is_public': True,
                'is_active': True,
                'sort_order': 20,
                'categories': ['通用', '动态'],
                'tags': ['走动', '动态', '讲解']
            }
        ]

        for avatar_data in avatars_data:
            # 提取分类和标签
            categories = avatar_data.pop('categories', [])
            tags = avatar_data.pop('tags', [])
            
            # 创建数字人形象
            avatar, created = DigitalAvatar.objects.get_or_create(
                name=avatar_data['name'],
                defaults=avatar_data
            )
            
            if created:
                self.stdout.write(f'创建数字人形象: {avatar.name}')
                
                # 添加分类关联
                for category_name in categories:
                    try:
                        category = AvatarCategory.objects.get(name=category_name)
                        avatar.categories.add(category)
                    except AvatarCategory.DoesNotExist:
                        self.stdout.write(
                            self.style.WARNING(f'分类不存在: {category_name}')
                        )
                
                # 设置标签
                if tags:
                    tag_objects = []
                    for tag_name in tags:
                        tag, created = AvatarTag.objects.get_or_create(
                            name=tag_name
                        )
                        tag_objects.append(tag)
                    avatar.tags.set(tag_objects)
