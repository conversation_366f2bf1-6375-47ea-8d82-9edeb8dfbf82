"""
数字人视频生成服务
支持多个第三方数字人API提供商
"""
import requests
import logging
import time
import json
from typing import Dict, Any, Optional
from apps.config.services import api_config
from .baidu_xiling_logger import (
    baidu_logger, log_api_call, log_performance,
    handle_api_errors, APIConnectionError, AuthenticationError
)

logger = logging.getLogger(__name__)


class HeyGenService:
    """HeyGen数字人视频生成服务"""
    
    def __init__(self):
        self.api_key = None
        self.base_url = "https://api.heygen.com/v2"
        self._initialize_service()
    
    def _initialize_service(self):
        """初始化HeyGen服务"""
        try:
            self.api_key = api_config.get_config('heygen_api_key')
            if not self.api_key:
                logger.warning("HeyGen API密钥未配置")
                return
            logger.info("HeyGen服务初始化成功")
        except Exception as e:
            logger.error(f"HeyGen服务初始化失败: {e}")
    
    def is_available(self) -> bool:
        """检查服务是否可用"""
        return bool(self.api_key)
    
    def create_video(
        self,
        avatar_id: str,
        script: str,
        voice_id: str = None,
        background: str = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        创建数字人视频
        
        Args:
            avatar_id: 数字人形象ID
            script: 视频脚本
            voice_id: 语音ID
            background: 背景设置
            **kwargs: 其他参数
        
        Returns:
            创建结果
        """
        if not self.is_available():
            return {
                'success': False,
                'error': 'HeyGen服务不可用，请检查API配置'
            }
        
        try:
            headers = {
                'X-API-Key': self.api_key,
                'Content-Type': 'application/json'
            }
            
            payload = {
                'video_inputs': [{
                    'character': {
                        'type': 'avatar',
                        'avatar_id': avatar_id,
                        'avatar_style': kwargs.get('avatar_style', 'normal')
                    },
                    'voice': {
                        'type': 'text',
                        'input_text': script,
                        'voice_id': voice_id or 'default'
                    },
                    'background': {
                        'type': kwargs.get('background_type', 'color'),
                        'value': background or '#ffffff'
                    }
                }],
                'dimension': {
                    'width': kwargs.get('width', 1920),
                    'height': kwargs.get('height', 1080)
                },
                'aspect_ratio': kwargs.get('aspect_ratio', '16:9'),
                'test': kwargs.get('test_mode', False)
            }
            
            response = requests.post(
                f"{self.base_url}/video/generate",
                headers=headers,
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                return {
                    'success': True,
                    'video_id': result.get('data', {}).get('video_id'),
                    'status': 'processing',
                    'message': '视频生成任务已创建'
                }
            else:
                error_msg = response.json().get('error', {}).get('message', '未知错误')
                return {
                    'success': False,
                    'error': f'HeyGen API错误: {error_msg}',
                    'status_code': response.status_code
                }
                
        except requests.exceptions.RequestException as e:
            logger.error(f"HeyGen API请求失败: {e}")
            return {
                'success': False,
                'error': f'网络请求失败: {str(e)}'
            }
        except Exception as e:
            logger.error(f"HeyGen视频生成失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_video_status(self, video_id: str) -> Dict[str, Any]:
        """
        获取视频生成状态
        
        Args:
            video_id: 视频ID
        
        Returns:
            状态信息
        """
        if not self.is_available():
            return {
                'success': False,
                'error': 'HeyGen服务不可用'
            }
        
        try:
            headers = {
                'X-API-Key': self.api_key
            }
            
            response = requests.get(
                f"{self.base_url}/video/{video_id}",
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                data = result.get('data', {})
                
                return {
                    'success': True,
                    'status': data.get('status'),
                    'progress': data.get('progress', 0),
                    'video_url': data.get('video_url'),
                    'thumbnail_url': data.get('thumbnail_url'),
                    'duration': data.get('duration'),
                    'created_at': data.get('created_at'),
                    'completed_at': data.get('completed_at')
                }
            else:
                return {
                    'success': False,
                    'error': f'获取状态失败: {response.status_code}'
                }
                
        except Exception as e:
            logger.error(f"获取HeyGen视频状态失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_avatars(self) -> Dict[str, Any]:
        """获取可用的数字人形象列表"""
        if not self.is_available():
            return {
                'success': False,
                'error': 'HeyGen服务不可用'
            }

        try:
            headers = {
                'X-API-Key': self.api_key
            }

            response = requests.get(
                f"{self.base_url}/avatars",
                headers=headers,
                timeout=10
            )

            if response.status_code == 200:
                result = response.json()
                return {
                    'success': True,
                    'avatars': result.get('data', [])
                }
            else:
                return {
                    'success': False,
                    'error': f'获取形象列表失败: {response.status_code}'
                }

        except Exception as e:
            logger.error(f"获取HeyGen形象列表失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def upload_asset(self, file_path: str) -> Dict[str, Any]:
        """上传资源文件到HeyGen"""
        if not self.is_available():
            return {
                'success': False,
                'error': 'HeyGen服务不可用'
            }

        try:
            headers = {
                'X-API-Key': self.api_key
            }

            with open(file_path, 'rb') as file:
                files = {'file': file}
                response = requests.post(
                    f"{self.base_url}/assets",
                    headers=headers,
                    files=files,
                    timeout=60
                )

            if response.status_code == 200:
                result = response.json()
                return {
                    'success': True,
                    'asset_id': result.get('data', {}).get('asset_id'),
                    'asset_url': result.get('data', {}).get('asset_url')
                }
            else:
                return {
                    'success': False,
                    'error': f'上传文件失败: {response.status_code}'
                }

        except Exception as e:
            logger.error(f"上传HeyGen资源失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def create_photo_avatar_group(self, name: str, image_key: str) -> Dict[str, Any]:
        """创建照片头像组"""
        if not self.is_available():
            return {
                'success': False,
                'error': 'HeyGen服务不可用'
            }

        try:
            headers = {
                'X-API-Key': self.api_key,
                'Content-Type': 'application/json'
            }

            data = {
                'name': name,
                'image_key': image_key
            }

            response = requests.post(
                f"{self.base_url}/photo_avatar/avatar_group/create",
                headers=headers,
                json=data,
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                return {
                    'success': True,
                    'group_id': result.get('data', {}).get('group_id'),
                    'avatar_id': result.get('data', {}).get('id'),
                    'status': result.get('data', {}).get('status')
                }
            else:
                return {
                    'success': False,
                    'error': f'创建照片头像组失败: {response.status_code}'
                }

        except Exception as e:
            logger.error(f"创建HeyGen照片头像组失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def train_photo_avatar_group(self, group_id: str) -> Dict[str, Any]:
        """训练照片头像组"""
        if not self.is_available():
            return {
                'success': False,
                'error': 'HeyGen服务不可用'
            }

        try:
            headers = {
                'X-API-Key': self.api_key,
                'Content-Type': 'application/json'
            }

            data = {
                'group_id': group_id
            }

            response = requests.post(
                f"{self.base_url}/photo_avatar/train",
                headers=headers,
                json=data,
                timeout=30
            )

            if response.status_code == 200:
                return {
                    'success': True,
                    'message': '训练任务已提交'
                }
            else:
                return {
                    'success': False,
                    'error': f'提交训练任务失败: {response.status_code}'
                }

        except Exception as e:
            logger.error(f"训练HeyGen照片头像组失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def get_training_status(self, group_id: str) -> Dict[str, Any]:
        """获取训练状态"""
        if not self.is_available():
            return {
                'success': False,
                'error': 'HeyGen服务不可用'
            }

        try:
            headers = {
                'X-API-Key': self.api_key
            }

            response = requests.get(
                f"{self.base_url}/photo_avatar/train/status/{group_id}",
                headers=headers,
                timeout=10
            )

            if response.status_code == 200:
                result = response.json()
                data = result.get('data', {})
                return {
                    'success': True,
                    'status': data.get('status'),
                    'error_msg': data.get('error_msg'),
                    'created_at': data.get('created_at'),
                    'updated_at': data.get('updated_at')
                }
            else:
                return {
                    'success': False,
                    'error': f'获取训练状态失败: {response.status_code}'
                }

        except Exception as e:
            logger.error(f"获取HeyGen训练状态失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }


class DIDService:
    """D-ID数字人视频生成服务"""
    
    def __init__(self):
        self.api_key = None
        self.base_url = "https://api.d-id.com"
        self._initialize_service()
    
    def _initialize_service(self):
        """初始化D-ID服务"""
        try:
            self.api_key = api_config.get_config('did_api_key')
            if not self.api_key:
                logger.warning("D-ID API密钥未配置")
                return
            logger.info("D-ID服务初始化成功")
        except Exception as e:
            logger.error(f"D-ID服务初始化失败: {e}")
    
    def is_available(self) -> bool:
        """检查服务是否可用"""
        return bool(self.api_key)
    
    def create_video(
        self,
        source_url: str,
        script: str,
        voice_id: str = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        创建数字人视频
        
        Args:
            source_url: 源图片URL
            script: 视频脚本
            voice_id: 语音ID
            **kwargs: 其他参数
        
        Returns:
            创建结果
        """
        if not self.is_available():
            return {
                'success': False,
                'error': 'D-ID服务不可用，请检查API配置'
            }
        
        try:
            headers = {
                'Authorization': f'Basic {self.api_key}',
                'Content-Type': 'application/json'
            }
            
            payload = {
                'source_url': source_url,
                'script': {
                    'type': 'text',
                    'input': script,
                    'provider': {
                        'type': 'microsoft',
                        'voice_id': voice_id or 'zh-CN-XiaoxiaoNeural'
                    }
                },
                'config': {
                    'fluent': kwargs.get('fluent', True),
                    'pad_audio': kwargs.get('pad_audio', 0.0)
                }
            }
            
            response = requests.post(
                f"{self.base_url}/talks",
                headers=headers,
                json=payload,
                timeout=30
            )
            
            if response.status_code == 201:
                result = response.json()
                return {
                    'success': True,
                    'video_id': result.get('id'),
                    'status': result.get('status', 'created'),
                    'message': '视频生成任务已创建'
                }
            else:
                error_msg = response.json().get('error', {}).get('message', '未知错误')
                return {
                    'success': False,
                    'error': f'D-ID API错误: {error_msg}',
                    'status_code': response.status_code
                }
                
        except Exception as e:
            logger.error(f"D-ID视频生成失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }


class BaiduXilingService:
    """百度曦灵数字人服务"""

    def __init__(self):
        self.api_key = None
        self.secret_key = None
        self.base_url = "https://xiling.cloud.baidu.com/api/v1"
        self._initialize_service()

    def _initialize_service(self):
        """初始化百度曦灵服务"""
        try:
            self.api_key = api_config.get_config('baidu_xiling_api_key')
            self.secret_key = api_config.get_config('baidu_xiling_secret_key')
            if not self.api_key or not self.secret_key:
                logger.warning("百度曦灵API密钥未配置")
                return
            logger.info("百度曦灵服务初始化成功")
        except Exception as e:
            logger.error(f"百度曦灵服务初始化失败: {e}")

    def is_available(self) -> bool:
        """检查服务是否可用"""
        return bool(self.api_key and self.secret_key)

    @log_api_call
    @log_performance("baidu_xiling_auth")
    def _get_access_token(self) -> str:
        """获取访问令牌"""
        try:
            import hashlib

            # 生成时间戳
            timestamp = str(int(time.time()))

            # 生成签名
            string_to_sign = f"POST\n/oauth/2.0/token\napi_key={self.api_key}&timestamp={timestamp}"
            signature = hashlib.sha256((string_to_sign + self.secret_key).encode()).hexdigest()

            # 请求访问令牌
            url = "https://xiling.cloud.baidu.com/oauth/2.0/token"
            headers = {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
            data = {
                'grant_type': 'client_credentials',
                'api_key': self.api_key,
                'timestamp': timestamp,
                'signature': signature
            }

            baidu_logger.log_api_call(
                method='POST',
                url=url,
                params={'api_key': self.api_key, 'timestamp': timestamp},
                success=True
            )

            response = requests.post(url, headers=headers, data=data, timeout=10)

            if response.status_code == 200:
                result = response.json()
                access_token = result.get('access_token')
                if access_token:
                    baidu_logger.log_api_call(
                        method='POST',
                        url=url,
                        response_data={'success': True, 'has_token': True},
                        success=True
                    )
                    return access_token
                else:
                    raise AuthenticationError("访问令牌为空")
            else:
                error_msg = f"HTTP {response.status_code}: {response.text}"
                raise APIConnectionError(f"获取访问令牌失败: {error_msg}")

        except Exception as e:
            if not isinstance(e, (APIConnectionError, AuthenticationError)):
                baidu_logger.log_error(
                    error_type="TokenGenerationError",
                    error_message=str(e),
                    context={'api_key': self.api_key[:10] + '...' if self.api_key else None}
                )
            raise

    def upload_file(self, file_path: str, provider_type: str) -> Dict[str, Any]:
        """上传文件到百度曦灵"""
        if not self.is_available():
            return {
                'success': False,
                'error': '百度曦灵服务不可用'
            }

        try:
            access_token = self._get_access_token()
            if not access_token:
                return {
                    'success': False,
                    'error': '获取访问令牌失败'
                }

            headers = {
                'Authorization': f'Bearer {access_token}'
            }

            with open(file_path, 'rb') as file:
                files = {
                    'file': file,
                    'providerType': (None, provider_type),
                    'sourceFileName': (None, file_path.split('/')[-1])
                }

                response = requests.post(
                    f"{self.base_url}/file/upload",
                    headers=headers,
                    files=files,
                    timeout=120
                )

            if response.status_code == 200:
                result = response.json()
                return {
                    'success': True,
                    'file_id': result.get('fileId'),
                    'file_name': result.get('fileName')
                }
            else:
                return {
                    'success': False,
                    'error': f'上传文件失败: {response.status_code}'
                }

        except Exception as e:
            logger.error(f"上传百度曦灵文件失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def create_2d_avatar(self, name: str, gender: str, template_video_id: str) -> Dict[str, Any]:
        """创建2D小样本数字人"""
        if not self.is_available():
            return {
                'success': False,
                'error': '百度曦灵服务不可用'
            }

        try:
            access_token = self._get_access_token()
            if not access_token:
                return {
                    'success': False,
                    'error': '获取访问令牌失败'
                }

            headers = {
                'Authorization': f'Bearer {access_token}',
                'Content-Type': 'application/json'
            }

            data = {
                'name': name,
                'gender': gender,
                'keepBackground': False,  # 去除背景
                'templateVideoId': template_video_id
            }

            response = requests.post(
                f"{self.base_url}/figure/lite2d/general",
                headers=headers,
                json=data,
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                return {
                    'success': True,
                    'figure_id': result.get('figureId'),
                    'message': '数字人创建任务已提交'
                }
            else:
                return {
                    'success': False,
                    'error': f'创建数字人失败: {response.status_code}'
                }

        except Exception as e:
            logger.error(f"创建百度曦灵数字人失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def get_avatar_status(self, figure_id: str) -> Dict[str, Any]:
        """获取数字人创建状态"""
        if not self.is_available():
            return {
                'success': False,
                'error': '百度曦灵服务不可用'
            }

        try:
            access_token = self._get_access_token()
            if not access_token:
                return {
                    'success': False,
                    'error': '获取访问令牌失败'
                }

            headers = {
                'Authorization': f'Bearer {access_token}'
            }

            params = {
                'figureId': figure_id,
                'trainSuccess': 'true'
            }

            response = requests.get(
                f"{self.base_url}/figure/lite2d/general/status",
                headers=headers,
                params=params,
                timeout=10
            )

            if response.status_code == 200:
                result = response.json()
                figures = result.get('figures', [])

                if figures:
                    figure = figures[0]
                    return {
                        'success': True,
                        'status': figure.get('status'),
                        'figure_id': figure.get('figureId'),
                        'name': figure.get('name'),
                        'failed_message': figure.get('failedMessage')
                    }
                else:
                    return {
                        'success': False,
                        'error': '未找到对应的数字人'
                    }
            else:
                return {
                    'success': False,
                    'error': f'获取状态失败: {response.status_code}'
                }

        except Exception as e:
            logger.error(f"获取百度曦灵数字人状态失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def generate_video(self, figure_id: str, text: str, voice_id: str = None) -> Dict[str, Any]:
        """生成数字人视频"""
        if not self.is_available():
            return {
                'success': False,
                'error': '百度曦灵服务不可用'
            }

        try:
            access_token = self._get_access_token()
            if not access_token:
                return {
                    'success': False,
                    'error': '获取访问令牌失败'
                }

            headers = {
                'Authorization': f'Bearer {access_token}',
                'Content-Type': 'application/json'
            }

            # 使用默认音色如果没有指定
            if not voice_id:
                voice_id = "zh-CN-XiaoxiaoNeural"  # 百度曦灵默认音色

            data = {
                'figureId': figure_id,
                'driveType': 'text',  # 文本驱动
                'text': text,
                'person': voice_id,
                'width': 1280,
                'height': 720,
                'cameraId': 0,  # 横屏半身
                'enabled': True,  # 开启字幕
                'autoAnimoji': True  # 自动添加动作
            }

            response = requests.post(
                f"{self.base_url}/video/generate",
                headers=headers,
                json=data,
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                return {
                    'success': True,
                    'task_id': result.get('taskId'),
                    'message': '视频生成任务已提交'
                }
            else:
                return {
                    'success': False,
                    'error': f'生成视频失败: {response.status_code}'
                }

        except Exception as e:
            logger.error(f"生成百度曦灵视频失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def get_video_status(self, task_id: str) -> Dict[str, Any]:
        """获取视频生成状态"""
        if not self.is_available():
            return {
                'success': False,
                'error': '百度曦灵服务不可用'
            }

        try:
            access_token = self._get_access_token()
            if not access_token:
                return {
                    'success': False,
                    'error': '获取访问令牌失败'
                }

            headers = {
                'Authorization': f'Bearer {access_token}'
            }

            params = {
                'taskId': task_id
            }

            response = requests.get(
                f"{self.base_url}/video/status",
                headers=headers,
                params=params,
                timeout=10
            )

            if response.status_code == 200:
                result = response.json()
                return {
                    'success': True,
                    'status': result.get('status'),
                    'video_url': result.get('videoUrl'),
                    'failed_message': result.get('failedMessage'),
                    'task_id': task_id
                }
            else:
                return {
                    'success': False,
                    'error': f'获取视频状态失败: {response.status_code}'
                }

        except Exception as e:
            logger.error(f"获取百度曦灵视频状态失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def get_voices(self, is_system: bool = True) -> Dict[str, Any]:
        """获取可用音色列表"""
        if not self.is_available():
            return {
                'success': False,
                'error': '百度曦灵服务不可用'
            }

        try:
            access_token = self._get_access_token()
            if not access_token:
                return {
                    'success': False,
                    'error': '获取访问令牌失败'
                }

            headers = {
                'Authorization': f'Bearer {access_token}'
            }

            params = {
                'isSystem': 'true' if is_system else 'false'
            }

            response = requests.get(
                f"{self.base_url}/voices",
                headers=headers,
                params=params,
                timeout=10
            )

            if response.status_code == 200:
                result = response.json()
                return {
                    'success': True,
                    'voices': result.get('voices', [])
                }
            else:
                return {
                    'success': False,
                    'error': f'获取音色列表失败: {response.status_code}'
                }

        except Exception as e:
            logger.error(f"获取百度曦灵音色列表失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }


class DigitalHumanService:
    """数字人视频生成服务统一接口"""

    def __init__(self):
        self.heygen = HeyGenService()
        self.did = DIDService()
        self.baidu_xiling = BaiduXilingService()
        self.default_provider = 'baidu_xiling'  # 改为百度曦灵为默认
    
    def create_video(
        self,
        provider: str = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        创建数字人视频
        
        Args:
            provider: 服务提供商 (heygen, did)
            **kwargs: 视频参数
        
        Returns:
            创建结果
        """
        provider = provider or self.default_provider
        
        if provider == 'heygen' and self.heygen.is_available():
            return self.heygen.create_video(**kwargs)
        elif provider == 'did' and self.did.is_available():
            return self.did.create_video(**kwargs)
        else:
            # 尝试其他可用的服务
            if self.heygen.is_available():
                return self.heygen.create_video(**kwargs)
            elif self.did.is_available():
                return self.did.create_video(**kwargs)
            else:
                return {
                    'success': False,
                    'error': '没有可用的数字人视频生成服务'
                }
    
    def get_video_status(self, video_id: str, provider: str = None) -> Dict[str, Any]:
        """获取视频生成状态"""
        provider = provider or self.default_provider
        
        if provider == 'heygen':
            return self.heygen.get_video_status(video_id)
        elif provider == 'did':
            # D-ID的状态查询实现
            return {'success': False, 'error': 'D-ID状态查询待实现'}
        else:
            return {
                'success': False,
                'error': f'不支持的服务提供商: {provider}'
            }


# 创建全局实例
digital_human_service = DigitalHumanService()


def create_digital_human_video(**kwargs) -> Dict[str, Any]:
    """创建数字人视频的便捷函数"""
    return digital_human_service.create_video(**kwargs)


def get_video_generation_status(video_id: str, provider: str = None) -> Dict[str, Any]:
    """获取视频生成状态的便捷函数"""
    return digital_human_service.get_video_status(video_id, provider)
