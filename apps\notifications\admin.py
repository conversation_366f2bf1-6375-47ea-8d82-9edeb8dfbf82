"""
通知管理后台配置
"""
from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from unfold.admin import ModelAdmin
from unfold.contrib.filters.admin import RangeDateFilter, ChoicesDropdownFilter
from .models import Notification, NotificationTemplate, UserNotificationSettings


@admin.register(Notification)
class NotificationAdmin(ModelAdmin):
    """通知管理"""
    
    list_display = (
        'title', 'user', 'type', 'priority', 'is_read', 'is_sent', 'created_at'
    )
    
    list_filter = (
        ('type', ChoicesDropdownFilter),
        ('priority', ChoicesDropdownFilter),
        'is_read', 'is_sent',
        ('created_at', RangeDateFilter),
    )
    
    search_fields = ('title', 'message', 'user__username', 'user__email')
    
    readonly_fields = ('created_at', 'read_at')
    
    fieldsets = (
        (_('基本信息'), {
            'fields': ('user', 'type', 'title', 'message', 'priority')
        }),
        (_('状态'), {
            'fields': ('is_read', 'is_sent')
        }),
        (_('额外数据'), {
            'fields': ('extra_data',),
            'classes': ('collapse',)
        }),
        (_('时间信息'), {
            'fields': ('created_at', 'read_at'),
            'classes': ('collapse',)
        }),
    )
    
    ordering = ('-created_at',)
    
    actions = ['mark_as_read', 'mark_as_sent']
    
    def mark_as_read(self, request, queryset):
        """批量标记为已读"""
        updated = queryset.update(is_read=True)
        self.message_user(request, f'成功标记 {updated} 个通知为已读')
    mark_as_read.short_description = _('标记为已读')
    
    def mark_as_sent(self, request, queryset):
        """批量标记为已发送"""
        updated = queryset.update(is_sent=True)
        self.message_user(request, f'成功标记 {updated} 个通知为已发送')
    mark_as_sent.short_description = _('标记为已发送')


@admin.register(NotificationTemplate)
class NotificationTemplateAdmin(ModelAdmin):
    """通知模板管理"""
    
    list_display = ('type', 'title_template', 'is_active', 'created_at')
    
    list_filter = ('is_active', ('created_at', RangeDateFilter))
    
    search_fields = ('type', 'title_template', 'message_template')
    
    readonly_fields = ('created_at', 'updated_at')
    
    fieldsets = (
        (_('基本信息'), {
            'fields': ('type', 'title_template', 'message_template')
        }),
        (_('设置'), {
            'fields': ('is_active',)
        }),
        (_('时间信息'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    ordering = ('type',)


@admin.register(UserNotificationSettings)
class UserNotificationSettingsAdmin(ModelAdmin):
    """用户通知设置管理"""
    
    list_display = (
        'user', 'email_notifications', 'browser_notifications',
        'video_progress_notifications', 'system_notifications'
    )
    
    list_filter = (
        'email_notifications', 'browser_notifications',
        'video_progress_notifications', 'system_notifications',
        'package_notifications'
    )
    
    search_fields = ('user__username', 'user__email')
    
    readonly_fields = ('created_at', 'updated_at')
    
    fieldsets = (
        (_('用户'), {
            'fields': ('user',)
        }),
        (_('通知方式'), {
            'fields': ('email_notifications', 'browser_notifications')
        }),
        (_('通知类型'), {
            'fields': (
                'video_progress_notifications', 'system_notifications',
                'package_notifications'
            )
        }),
        (_('时间信息'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    ordering = ('user__username',)
