"""
语音系统相关API路由
"""
from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

router = DefaultRouter()
router.register(r'voice-models', views.VoiceModelViewSet)
router.register(r'voice-clone-requests', views.VoiceCloneRequestViewSet)
router.register(r'voice-synthesis-tasks', views.VoiceSynthesisTaskViewSet)

# 为了兼容前端API调用，添加别名路由
router.register(r'voice-cloning', views.VoiceCloneRequestViewSet, basename='voice-cloning')

urlpatterns = [
    path('', include(router.urls)),
]
