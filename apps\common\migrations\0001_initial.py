# Generated by Django 5.2.1 on 2025-07-16 13:57

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="SecurityLog",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "event_type",
                    models.CharField(
                        choices=[
                            ("login_success", "登录成功"),
                            ("login_failure", "登录失败"),
                            ("permission_denied", "权限拒绝"),
                            ("suspicious_activity", "可疑活动"),
                            ("rate_limit_exceeded", "频率限制超出"),
                            ("invalid_token", "无效令牌"),
                            ("sql_injection_attempt", "SQL注入尝试"),
                            ("xss_attempt", "XSS攻击尝试"),
                            ("csrf_failure", "CSRF验证失败"),
                            ("file_upload_blocked", "文件上传被阻止"),
                            ("other", "其他"),
                        ],
                        max_length=50,
                        verbose_name="事件类型",
                    ),
                ),
                (
                    "severity",
                    models.CharField(
                        choices=[
                            ("low", "低"),
                            ("medium", "中"),
                            ("high", "高"),
                            ("critical", "严重"),
                        ],
                        default="medium",
                        max_length=20,
                        verbose_name="严重程度",
                    ),
                ),
                ("description", models.TextField(verbose_name="事件描述")),
                (
                    "username",
                    models.CharField(blank=True, max_length=150, verbose_name="用户名"),
                ),
                (
                    "ip_address",
                    models.GenericIPAddressField(
                        blank=True, null=True, verbose_name="IP地址"
                    ),
                ),
                ("user_agent", models.TextField(blank=True, verbose_name="用户代理")),
                ("url", models.URLField(blank=True, verbose_name="请求URL")),
                (
                    "method",
                    models.CharField(blank=True, max_length=10, verbose_name="请求方法"),
                ),
                (
                    "extra_data",
                    models.JSONField(blank=True, default=dict, verbose_name="额外数据"),
                ),
                ("is_resolved", models.BooleanField(default=False, verbose_name="已处理")),
                (
                    "resolved_at",
                    models.DateTimeField(blank=True, null=True, verbose_name="处理时间"),
                ),
                ("resolution_notes", models.TextField(blank=True, verbose_name="处理说明")),
                (
                    "timestamp",
                    models.DateTimeField(auto_now_add=True, verbose_name="时间戳"),
                ),
                (
                    "resolved_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="resolved_security_logs",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="处理人",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="用户",
                    ),
                ),
            ],
            options={
                "verbose_name": "安全日志",
                "verbose_name_plural": "安全日志",
                "ordering": ["-timestamp"],
                "indexes": [
                    models.Index(
                        fields=["event_type", "timestamp"],
                        name="common_secu_event_t_537970_idx",
                    ),
                    models.Index(
                        fields=["severity", "timestamp"],
                        name="common_secu_severit_93d2f6_idx",
                    ),
                    models.Index(
                        fields=["user", "timestamp"],
                        name="common_secu_user_id_c07f68_idx",
                    ),
                    models.Index(
                        fields=["ip_address", "timestamp"],
                        name="common_secu_ip_addr_a5afcb_idx",
                    ),
                    models.Index(
                        fields=["is_resolved"], name="common_secu_is_reso_49c63a_idx"
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="SystemLog",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "level",
                    models.CharField(
                        choices=[
                            ("DEBUG", "调试"),
                            ("INFO", "信息"),
                            ("WARNING", "警告"),
                            ("ERROR", "错误"),
                            ("CRITICAL", "严重错误"),
                        ],
                        max_length=20,
                        verbose_name="日志级别",
                    ),
                ),
                ("logger", models.CharField(max_length=100, verbose_name="日志记录器")),
                ("message", models.TextField(verbose_name="日志消息")),
                (
                    "module",
                    models.CharField(blank=True, max_length=100, verbose_name="模块"),
                ),
                (
                    "function",
                    models.CharField(blank=True, max_length=100, verbose_name="函数"),
                ),
                (
                    "line_number",
                    models.IntegerField(blank=True, null=True, verbose_name="行号"),
                ),
                (
                    "request_id",
                    models.CharField(blank=True, max_length=100, verbose_name="请求ID"),
                ),
                (
                    "ip_address",
                    models.GenericIPAddressField(
                        blank=True, null=True, verbose_name="IP地址"
                    ),
                ),
                ("user_agent", models.TextField(blank=True, verbose_name="用户代理")),
                ("url", models.URLField(blank=True, verbose_name="请求URL")),
                (
                    "method",
                    models.CharField(blank=True, max_length=10, verbose_name="请求方法"),
                ),
                (
                    "status_code",
                    models.IntegerField(blank=True, null=True, verbose_name="状态码"),
                ),
                (
                    "exception_type",
                    models.CharField(blank=True, max_length=100, verbose_name="异常类型"),
                ),
                (
                    "exception_message",
                    models.TextField(blank=True, verbose_name="异常消息"),
                ),
                ("traceback", models.TextField(blank=True, verbose_name="异常堆栈")),
                (
                    "extra_data",
                    models.JSONField(blank=True, default=dict, verbose_name="额外数据"),
                ),
                (
                    "timestamp",
                    models.DateTimeField(auto_now_add=True, verbose_name="时间戳"),
                ),
                (
                    "user",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="用户",
                    ),
                ),
            ],
            options={
                "verbose_name": "系统日志",
                "verbose_name_plural": "系统日志",
                "ordering": ["-timestamp"],
                "indexes": [
                    models.Index(
                        fields=["level", "timestamp"],
                        name="common_syst_level_9e6215_idx",
                    ),
                    models.Index(
                        fields=["logger", "timestamp"],
                        name="common_syst_logger_cf8602_idx",
                    ),
                    models.Index(
                        fields=["user", "timestamp"],
                        name="common_syst_user_id_7561cf_idx",
                    ),
                    models.Index(
                        fields=["request_id"], name="common_syst_request_a95f52_idx"
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="UserActionLog",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "action",
                    models.CharField(
                        choices=[
                            ("login", "登录"),
                            ("logout", "登出"),
                            ("register", "注册"),
                            ("profile_update", "更新资料"),
                            ("password_change", "修改密码"),
                            ("avatar_create", "创建数字人"),
                            ("avatar_update", "更新数字人"),
                            ("avatar_delete", "删除数字人"),
                            ("voice_create", "创建语音"),
                            ("voice_update", "更新语音"),
                            ("voice_delete", "删除语音"),
                            ("video_create", "创建视频"),
                            ("video_update", "更新视频"),
                            ("video_delete", "删除视频"),
                            ("package_purchase", "购买套餐"),
                            ("recharge", "充值"),
                            ("other", "其他"),
                        ],
                        max_length=50,
                        verbose_name="操作类型",
                    ),
                ),
                ("description", models.TextField(verbose_name="操作描述")),
                (
                    "ip_address",
                    models.GenericIPAddressField(
                        blank=True, null=True, verbose_name="IP地址"
                    ),
                ),
                ("user_agent", models.TextField(blank=True, verbose_name="用户代理")),
                (
                    "object_type",
                    models.CharField(blank=True, max_length=50, verbose_name="对象类型"),
                ),
                (
                    "object_id",
                    models.CharField(blank=True, max_length=100, verbose_name="对象ID"),
                ),
                (
                    "extra_data",
                    models.JSONField(blank=True, default=dict, verbose_name="额外数据"),
                ),
                (
                    "timestamp",
                    models.DateTimeField(auto_now_add=True, verbose_name="时间戳"),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="用户",
                    ),
                ),
            ],
            options={
                "verbose_name": "用户操作日志",
                "verbose_name_plural": "用户操作日志",
                "ordering": ["-timestamp"],
                "indexes": [
                    models.Index(
                        fields=["user", "timestamp"],
                        name="common_user_user_id_c0d488_idx",
                    ),
                    models.Index(
                        fields=["action", "timestamp"],
                        name="common_user_action_6d2654_idx",
                    ),
                    models.Index(
                        fields=["object_type", "object_id"],
                        name="common_user_object__18b2b5_idx",
                    ),
                ],
            },
        ),
    ]
