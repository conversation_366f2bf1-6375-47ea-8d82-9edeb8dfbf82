"""
AI工具相关API路由
"""
from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

router = DefaultRouter()

urlpatterns = [
    path('', include(router.urls)),
    # 兼容两种URL格式
    path('chat/', views.AIChatView.as_view(), name='ai-chat'),
    path('ai-chat/', views.AIChatView.as_view(), name='ai-chat-alt'),
    path('painting/', views.AIPaintingView.as_view(), name='ai-painting'),
    path('ai-painting/', views.AIPaintingView.as_view(), name='ai-painting-alt'),
    path('ai-music/', views.AIMusicView.as_view(), name='ai-music'),
    path('ai-video/', views.AIVideoView.as_view(), name='ai-video'),
    path('face-fusion/', views.FaceFusionView.as_view(), name='face-fusion'),
    path('photo-digital-human/', views.PhotoDigitalHumanView.as_view(), name='photo-digital-human'),
    path('text-extraction/', views.TextExtractionView.as_view(), name='text-extraction'),
]
