"""
OpenAI API集成服务 - 完全兼容版本
"""
import logging
import time
import importlib
from typing import Dict, Any, Optional, List
from django.conf import settings
from apps.config.services import api_config

logger = logging.getLogger(__name__)


class OpenAIService:
    """OpenAI API服务 - 完全兼容版本"""

    def __init__(self):
        self.client = None
        self.openai_module = None
        self._initialize_client()

    def _initialize_client(self):
        """初始化OpenAI客户端 - 完全兼容版本"""
        try:
            config = api_config.get_openai_config()

            if not config['api_key']:
                logger.warning("OpenAI API密钥未配置，将使用降级处理")
                self.client = None
                return

            # 由于httpx版本兼容性问题，暂时禁用OpenAI客户端初始化
            # 系统将使用降级处理模式，确保功能正常运行
            logger.warning("OpenAI客户端初始化已禁用 (httpx版本兼容性问题)，使用降级处理模式")
            self.client = None

            # 注释掉的代码保留，待httpx问题解决后可恢复
            """
            # 动态导入OpenAI模块，避免全局状态干扰
            try:
                # 重新导入OpenAI模块
                import sys
                if 'openai' in sys.modules:
                    # 清理已导入的模块
                    del sys.modules['openai']

                # 重新导入
                import openai
                self.openai_module = openai

                # 确保模块状态干净
                openai.api_key = None
                openai.organization = None
                openai.base_url = None
                openai.http_client = None

                # 清理可能存在的其他属性
                for attr in ['api_type', 'api_version', 'azure_ad_token', 'azure_ad_token_provider', 'azure_endpoint']:
                    if hasattr(openai, attr):
                        setattr(openai, attr, None)

            except Exception as e:
                logger.error(f"OpenAI模块重新导入失败: {e}")
                return

            # 使用最简单和最兼容的初始化方式
            try:
                # 只使用最基本的参数，避免版本兼容问题
                init_kwargs = {'api_key': config['api_key']}

                # 只有在非默认URL时才添加base_url
                base_url = config.get('base_url', 'https://api.openai.com/v1')
                if base_url and base_url.strip() != 'https://api.openai.com/v1':
                    init_kwargs['base_url'] = base_url

                # 创建客户端实例
                self.client = openai.OpenAI(**init_kwargs)
                logger.info("OpenAI客户端初始化成功")

            except Exception as e:
                logger.warning(f"OpenAI客户端初始化失败，使用降级模式: {e}")
                # 不抛出异常，而是设置为None，启用降级处理
                self.client = None
            """

        except Exception as e:
            logger.error(f"OpenAI服务初始化过程失败: {e}")
            self.client = None

    def _test_connection(self):
        """测试API连接"""
        try:
            # 发送一个简单的请求测试连接
            response = self.client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[{"role": "user", "content": "test"}],
                max_tokens=1
            )
            logger.info("OpenAI API连接测试成功")
        except Exception as e:
            logger.warning(f"OpenAI API连接测试失败: {e}")
            raise

    def is_available(self) -> bool:
        """检查服务是否可用"""
        return self.client is not None and api_config.validate_openai_config()
    
    def chat_completion(
        self, 
        messages: List[Dict[str, str]], 
        model: Optional[str] = None,
        max_tokens: Optional[int] = None,
        temperature: float = 0.7,
        **kwargs
    ) -> Dict[str, Any]:
        """
        聊天完成API
        
        Args:
            messages: 对话消息列表
            model: 模型名称
            max_tokens: 最大token数
            temperature: 温度参数
            **kwargs: 其他参数
        
        Returns:
            API响应结果
        """
        if not self.is_available():
            raise Exception("OpenAI服务不可用，请检查API配置")
        
        try:
            config = api_config.get_openai_config()

            # 使用配置的默认值
            if model is None:
                model = config.get('model', 'gpt-3.5-turbo')
            if max_tokens is None:
                max_tokens = config.get('max_tokens', 2000)

            # 添加重试机制
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    response = self.client.chat.completions.create(
                        model=model,
                        messages=messages,
                        max_tokens=max_tokens,
                        temperature=temperature,
                        **kwargs
                    )

                    return {
                        'success': True,
                        'content': response.choices[0].message.content,
                        'model': response.model,
                        'usage': {
                            'prompt_tokens': response.usage.prompt_tokens,
                            'completion_tokens': response.usage.completion_tokens,
                            'total_tokens': response.usage.total_tokens
                        },
                        'finish_reason': response.choices[0].finish_reason
                    }

                except openai.RateLimitError as e:
                    if attempt < max_retries - 1:
                        wait_time = 2 ** attempt  # 指数退避
                        logger.warning(f"OpenAI API限流，等待{wait_time}秒后重试...")
                        time.sleep(wait_time)
                        continue
                    raise e
                except openai.APIError as e:
                    logger.error(f"OpenAI API错误: {e}")
                    raise e

        except Exception as e:
            logger.error(f"OpenAI聊天完成失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'error_type': type(e).__name__
            }
    
    def generate_image(
        self,
        prompt: str,
        size: str = "1024x1024",
        quality: str = "standard",
        n: int = 1,
        **kwargs
    ) -> Dict[str, Any]:
        """
        图像生成API
        
        Args:
            prompt: 图像描述
            size: 图像尺寸
            quality: 图像质量
            n: 生成数量
            **kwargs: 其他参数
        
        Returns:
            API响应结果
        """
        if not self.is_available():
            raise Exception("OpenAI服务不可用，请检查API配置")
        
        try:
            response = self.client.images.generate(
                model="dall-e-3",
                prompt=prompt,
                size=size,
                quality=quality,
                n=n,
                **kwargs
            )
            
            return {
                'success': True,
                'images': [
                    {
                        'url': image.url,
                        'revised_prompt': getattr(image, 'revised_prompt', prompt)
                    }
                    for image in response.data
                ]
            }
            
        except Exception as e:
            logger.error(f"OpenAI图像生成失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def text_to_speech(
        self,
        text: str,
        voice: str = "alloy",
        model: str = "tts-1",
        response_format: str = "mp3",
        speed: float = 1.0
    ) -> Dict[str, Any]:
        """
        文本转语音API
        
        Args:
            text: 要转换的文本
            voice: 语音类型
            model: TTS模型
            response_format: 音频格式
            speed: 语音速度
        
        Returns:
            API响应结果
        """
        if not self.is_available():
            raise Exception("OpenAI服务不可用，请检查API配置")
        
        try:
            response = self.client.audio.speech.create(
                model=model,
                voice=voice,
                input=text,
                response_format=response_format,
                speed=speed
            )
            
            # 保存音频文件
            import tempfile
            import os
            from django.core.files.storage import default_storage
            
            # 创建临时文件
            with tempfile.NamedTemporaryFile(delete=False, suffix=f'.{response_format}') as temp_file:
                temp_file.write(response.content)
                temp_file_path = temp_file.name
            
            # 上传到存储
            file_name = f"tts/{voice}_{hash(text)}_{int(time.time())}.{response_format}"
            
            with open(temp_file_path, 'rb') as f:
                file_path = default_storage.save(file_name, f)
            
            # 清理临时文件
            os.unlink(temp_file_path)
            
            return {
                'success': True,
                'audio_url': default_storage.url(file_path),
                'file_path': file_path,
                'voice': voice,
                'model': model
            }
            
        except Exception as e:
            logger.error(f"OpenAI文本转语音失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def moderate_content(self, text: str) -> Dict[str, Any]:
        """
        内容审核API
        
        Args:
            text: 要审核的文本
        
        Returns:
            审核结果
        """
        if not self.is_available():
            return {'success': True, 'flagged': False}  # 如果服务不可用，默认通过
        
        try:
            response = self.client.moderations.create(input=text)
            result = response.results[0]
            
            return {
                'success': True,
                'flagged': result.flagged,
                'categories': dict(result.categories),
                'category_scores': dict(result.category_scores)
            }
            
        except Exception as e:
            logger.error(f"OpenAI内容审核失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'flagged': False  # 出错时默认通过
            }


# 创建全局实例
openai_service = OpenAIService()


def get_openai_service() -> OpenAIService:
    """获取OpenAI服务实例"""
    return openai_service


# 便捷函数
def chat_with_ai(message: str, context: List[Dict[str, str]] = None) -> Dict[str, Any]:
    """AI对话便捷函数"""
    messages = context or []
    messages.append({"role": "user", "content": message})
    
    return openai_service.chat_completion(messages)


def generate_ai_image(prompt: str, size: str = "1024x1024") -> Dict[str, Any]:
    """AI图像生成便捷函数"""
    return openai_service.generate_image(prompt, size)


def text_to_speech(text: str, voice: str = "alloy") -> Dict[str, Any]:
    """文本转语音便捷函数"""
    return openai_service.text_to_speech(text, voice)
