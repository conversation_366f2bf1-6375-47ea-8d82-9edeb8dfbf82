"""
支付服务
支持支付宝、微信支付等多种支付方式
"""
import json
import logging
import hashlib
import time
from decimal import Decimal
from typing import Dict, Any, Optional
from django.conf import settings
from apps.config.services import api_config

logger = logging.getLogger(__name__)

try:
    from alipay import AliPay
    ALIPAY_AVAILABLE = True
except ImportError:
    ALIPAY_AVAILABLE = False
    logger.warning("支付宝SDK未安装")

try:
    from wechatpayv3 import WeChatPayV3, WeChatPayType
    WECHAT_AVAILABLE = True
except ImportError:
    WECHAT_AVAILABLE = False
    logger.warning("微信支付SDK未安装")


class AlipayService:
    """支付宝支付服务"""
    
    def __init__(self):
        self.alipay = None
        self._initialize_service()
    
    def _initialize_service(self):
        """初始化支付宝服务"""
        if not ALIPAY_AVAILABLE:
            return
        
        try:
            config = api_config.get_alipay_config()
            
            if not all([config['app_id'], config['private_key'], config['public_key']]):
                logger.warning("支付宝配置不完整")
                return
            
            self.alipay = AliPay(
                appid=config['app_id'],
                app_notify_url=config.get('notify_url', ''),
                app_private_key_string=config['private_key'],
                alipay_public_key_string=config['public_key'],
                sign_type="RSA2",
                debug=config.get('debug', False)
            )
            
            logger.info("支付宝服务初始化成功")
            
        except Exception as e:
            logger.error(f"支付宝服务初始化失败: {e}")
            self.alipay = None
    
    def is_available(self) -> bool:
        """检查服务是否可用"""
        return ALIPAY_AVAILABLE and self.alipay is not None
    
    def create_order(
        self,
        order_id: str,
        amount: Decimal,
        subject: str,
        body: str = None,
        return_url: str = None,
        notify_url: str = None
    ) -> Dict[str, Any]:
        """
        创建支付宝订单
        
        Args:
            order_id: 订单号
            amount: 支付金额
            subject: 订单标题
            body: 订单描述
            return_url: 同步回调地址
            notify_url: 异步回调地址
        
        Returns:
            支付结果
        """
        if not self.is_available():
            return {
                'success': False,
                'error': '支付宝服务不可用，请检查配置'
            }
        
        try:
            order_string = self.alipay.api_alipay_trade_page_pay(
                out_trade_no=order_id,
                total_amount=str(amount),
                subject=subject,
                body=body or subject,
                return_url=return_url,
                notify_url=notify_url
            )
            
            # 生成支付URL
            config = api_config.get_alipay_config()
            gateway = "https://openapi.alipay.com/gateway.do"
            if config.get('debug', False):
                gateway = "https://openapi.alipaydev.com/gateway.do"
            
            pay_url = f"{gateway}?{order_string}"
            
            return {
                'success': True,
                'pay_url': pay_url,
                'order_string': order_string,
                'order_id': order_id
            }
            
        except Exception as e:
            logger.error(f"创建支付宝订单失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def verify_notify(self, data: Dict[str, str]) -> bool:
        """验证支付宝异步通知"""
        if not self.is_available():
            return False
        
        try:
            signature = data.pop("sign")
            return self.alipay.verify(data, signature)
        except Exception as e:
            logger.error(f"验证支付宝通知失败: {e}")
            return False
    
    def query_order(self, order_id: str) -> Dict[str, Any]:
        """查询订单状态"""
        if not self.is_available():
            return {
                'success': False,
                'error': '支付宝服务不可用'
            }
        
        try:
            result = self.alipay.api_alipay_trade_query(out_trade_no=order_id)
            
            if result.get("code") == "10000":
                return {
                    'success': True,
                    'trade_status': result.get('trade_status'),
                    'trade_no': result.get('trade_no'),
                    'total_amount': result.get('total_amount'),
                    'buyer_pay_amount': result.get('buyer_pay_amount')
                }
            else:
                return {
                    'success': False,
                    'error': result.get('msg', '查询失败')
                }
                
        except Exception as e:
            logger.error(f"查询支付宝订单失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }


class WechatPayService:
    """微信支付服务"""
    
    def __init__(self):
        self.wxpay = None
        self._initialize_service()
    
    def _initialize_service(self):
        """初始化微信支付服务"""
        if not WECHAT_AVAILABLE:
            return
        
        try:
            config = api_config.get_wechat_pay_config()
            
            if not all([config['mch_id'], config['api_key'], config['cert_path']]):
                logger.warning("微信支付配置不完整")
                return
            
            self.wxpay = WeChatPayV3(
                wechatpay_type=WeChatPayType.NATIVE,
                mchid=config['mch_id'],
                private_key=config['private_key'],
                cert_serial_no=config['cert_serial_no'],
                apiv3_key=config['api_key'],
                appid=config['app_id']
            )
            
            logger.info("微信支付服务初始化成功")
            
        except Exception as e:
            logger.error(f"微信支付服务初始化失败: {e}")
            self.wxpay = None
    
    def is_available(self) -> bool:
        """检查服务是否可用"""
        return WECHAT_AVAILABLE and self.wxpay is not None
    
    def create_order(
        self,
        order_id: str,
        amount: Decimal,
        description: str,
        notify_url: str = None
    ) -> Dict[str, Any]:
        """
        创建微信支付订单
        
        Args:
            order_id: 订单号
            amount: 支付金额（分）
            description: 订单描述
            notify_url: 异步回调地址
        
        Returns:
            支付结果
        """
        if not self.is_available():
            return {
                'success': False,
                'error': '微信支付服务不可用，请检查配置'
            }
        
        try:
            # 微信支付金额单位为分
            amount_fen = int(amount * 100)
            
            result = self.wxpay.pay(
                description=description,
                out_trade_no=order_id,
                amount={'total': amount_fen},
                notify_url=notify_url
            )
            
            if result.get('code_url'):
                return {
                    'success': True,
                    'code_url': result['code_url'],
                    'prepay_id': result.get('prepay_id'),
                    'order_id': order_id
                }
            else:
                return {
                    'success': False,
                    'error': '创建微信支付订单失败'
                }
                
        except Exception as e:
            logger.error(f"创建微信支付订单失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def verify_notify(self, headers: Dict[str, str], body: str) -> bool:
        """验证微信支付异步通知"""
        if not self.is_available():
            return False
        
        try:
            return self.wxpay.callback(headers, body)
        except Exception as e:
            logger.error(f"验证微信支付通知失败: {e}")
            return False


class PaymentService:
    """统一支付服务接口"""
    
    def __init__(self):
        self.alipay = AlipayService()
        self.wechat = WechatPayService()
    
    def create_payment(
        self,
        payment_method: str,
        order_id: str,
        amount: Decimal,
        subject: str,
        **kwargs
    ) -> Dict[str, Any]:
        """
        创建支付订单
        
        Args:
            payment_method: 支付方式 (alipay, wechat)
            order_id: 订单号
            amount: 支付金额
            subject: 订单标题
            **kwargs: 其他参数
        
        Returns:
            支付结果
        """
        if payment_method == 'alipay' and self.alipay.is_available():
            return self.alipay.create_order(
                order_id=order_id,
                amount=amount,
                subject=subject,
                **kwargs
            )
        elif payment_method == 'wechat' and self.wechat.is_available():
            return self.wechat.create_order(
                order_id=order_id,
                amount=amount,
                description=subject,
                **kwargs
            )
        else:
            return {
                'success': False,
                'error': f'不支持的支付方式或服务不可用: {payment_method}'
            }
    
    def verify_payment_notify(
        self,
        payment_method: str,
        data: Dict[str, Any]
    ) -> bool:
        """验证支付回调通知"""
        if payment_method == 'alipay':
            return self.alipay.verify_notify(data)
        elif payment_method == 'wechat':
            headers = data.get('headers', {})
            body = data.get('body', '')
            return self.wechat.verify_notify(headers, body)
        else:
            return False
    
    def query_payment_status(
        self,
        payment_method: str,
        order_id: str
    ) -> Dict[str, Any]:
        """查询支付状态"""
        if payment_method == 'alipay':
            return self.alipay.query_order(order_id)
        elif payment_method == 'wechat':
            # 微信支付查询实现
            return {'success': False, 'error': '微信支付查询功能待实现'}
        else:
            return {
                'success': False,
                'error': f'不支持的支付方式: {payment_method}'
            }
    
    def get_available_methods(self) -> list:
        """获取可用的支付方式"""
        methods = []
        
        if self.alipay.is_available():
            methods.append({
                'method': 'alipay',
                'name': '支付宝',
                'icon': 'alipay'
            })
        
        if self.wechat.is_available():
            methods.append({
                'method': 'wechat',
                'name': '微信支付',
                'icon': 'wechat'
            })
        
        return methods


# 创建全局实例
payment_service = PaymentService()


def create_payment(payment_method: str, order_id: str, amount: Decimal, subject: str, **kwargs) -> Dict[str, Any]:
    """创建支付订单的便捷函数"""
    return payment_service.create_payment(payment_method, order_id, amount, subject, **kwargs)


def verify_payment_notify(payment_method: str, data: Dict[str, Any]) -> bool:
    """验证支付回调的便捷函数"""
    return payment_service.verify_payment_notify(payment_method, data)


def get_available_payment_methods() -> list:
    """获取可用支付方式的便捷函数"""
    return payment_service.get_available_methods()
