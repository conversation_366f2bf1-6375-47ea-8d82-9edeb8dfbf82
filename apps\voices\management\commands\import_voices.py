"""
批量导入语音模型管理命令
"""
import csv
import json
from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from apps.voices.models import VoiceModel, VoiceCategory, VoiceTag


class Command(BaseCommand):
    help = '批量导入语音模型数据'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--file',
            type=str,
            required=True,
            help='CSV或JSON文件路径'
        )
        parser.add_argument(
            '--format',
            type=str,
            choices=['csv', 'json'],
            default='csv',
            help='文件格式 (csv 或 json)'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='仅验证数据，不实际导入'
        )
    
    def handle(self, *args, **options):
        file_path = options['file']
        file_format = options['format']
        dry_run = options['dry_run']
        
        try:
            if file_format == 'csv':
                self.import_from_csv(file_path, dry_run)
            elif file_format == 'json':
                self.import_from_json(file_path, dry_run)
        except Exception as e:
            raise CommandError(f'导入失败: {str(e)}')
    
    def import_from_csv(self, file_path, dry_run=False):
        """从CSV文件导入"""
        self.stdout.write('开始从CSV文件导入语音模型...')
        
        imported_count = 0
        error_count = 0
        
        with open(file_path, 'r', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)
            
            # 验证必需的列
            required_fields = ['name', 'language', 'gender']
            missing_fields = [field for field in required_fields if field not in reader.fieldnames]
            if missing_fields:
                raise CommandError(f'CSV文件缺少必需的列: {", ".join(missing_fields)}')
            
            with transaction.atomic():
                for row_num, row in enumerate(reader, start=2):
                    try:
                        if dry_run:
                            self.validate_row(row)
                        else:
                            self.create_voice_from_row(row)
                        imported_count += 1
                    except Exception as e:
                        error_count += 1
                        self.stdout.write(
                            self.style.ERROR(f'第{row_num}行导入失败: {str(e)}')
                        )
                
                if dry_run:
                    self.stdout.write(
                        self.style.SUCCESS(f'验证完成: {imported_count} 行有效, {error_count} 行有错误')
                    )
                    transaction.set_rollback(True)
                else:
                    self.stdout.write(
                        self.style.SUCCESS(f'导入完成: {imported_count} 个语音模型, {error_count} 个失败')
                    )
    
    def validate_row(self, row):
        """验证CSV行数据"""
        required_fields = ['name', 'language', 'gender']
        for field in required_fields:
            if not row.get(field, '').strip():
                raise ValueError(f'必需字段 {field} 不能为空')
        
        # 验证性别字段
        valid_genders = ['male', 'female', 'neutral']
        if row.get('gender', '').lower() not in valid_genders:
            raise ValueError(f'gender 必须是: {", ".join(valid_genders)}')
        
        # 验证数值字段
        if row.get('computing_power_cost'):
            try:
                int(row['computing_power_cost'])
            except ValueError:
                raise ValueError('computing_power_cost 必须是整数')
    
    def create_voice_from_row(self, row):
        """从CSV行创建语音模型"""
        # 检查是否已存在
        if VoiceModel.objects.filter(name=row['name']).exists():
            raise ValueError(f'语音模型 "{row["name"]}" 已存在')
        
        # 创建语音模型
        voice = VoiceModel.objects.create(
            name=row['name'].strip(),
            description=row.get('description', '').strip(),
            language=row['language'].strip(),
            gender=row['gender'].lower().strip(),
            age_range=row.get('age_range', '').strip() or 'adult',
            voice_style=row.get('voice_style', '').strip() or 'natural',
            computing_power_cost=int(row.get('computing_power_cost', 5)),
            is_public=row.get('is_public', '').lower() in ['true', '1', 'yes'],
            is_active=row.get('is_active', '').lower() in ['true', '1', 'yes'],
            audio_sample_url=row.get('audio_sample_url', '').strip(),
            model_file_url=row.get('model_file_url', '').strip(),
        )
        
        # 处理分类
        if row.get('categories'):
            category_names = [name.strip() for name in row['categories'].split(',')]
            for category_name in category_names:
                if category_name:
                    category, _ = VoiceCategory.objects.get_or_create(
                        name=category_name,
                        defaults={'description': f'{category_name}分类'}
                    )
                    voice.categories.add(category)
        
        # 处理标签
        if row.get('tags'):
            tag_names = [name.strip() for name in row['tags'].split(',')]
            for tag_name in tag_names:
                if tag_name:
                    tag, _ = VoiceTag.objects.get_or_create(
                        name=tag_name,
                        defaults={'color': '#28a745'}
                    )
                    voice.tags.add(tag)
        
        return voice
    
    def import_from_json(self, file_path, dry_run=False):
        """从JSON文件导入"""
        self.stdout.write('开始从JSON文件导入语音模型...')
        
        with open(file_path, 'r', encoding='utf-8') as jsonfile:
            data = json.load(jsonfile)
        
        if not isinstance(data, list):
            raise CommandError('JSON文件应包含语音模型数组')
        
        imported_count = 0
        error_count = 0
        
        with transaction.atomic():
            for index, item in enumerate(data):
                try:
                    if dry_run:
                        self.validate_json_item(item)
                    else:
                        self.create_voice_from_json(item)
                    imported_count += 1
                except Exception as e:
                    error_count += 1
                    self.stdout.write(
                        self.style.ERROR(f'第{index+1}项导入失败: {str(e)}')
                    )
            
            if dry_run:
                self.stdout.write(
                    self.style.SUCCESS(f'验证完成: {imported_count} 项有效, {error_count} 项有错误')
                )
                transaction.set_rollback(True)
            else:
                self.stdout.write(
                    self.style.SUCCESS(f'导入完成: {imported_count} 个语音模型, {error_count} 个失败')
                )
    
    def validate_json_item(self, item):
        """验证JSON项数据"""
        required_fields = ['name', 'language', 'gender']
        for field in required_fields:
            if not item.get(field, '').strip():
                raise ValueError(f'必需字段 {field} 不能为空')
        
        # 验证性别字段
        valid_genders = ['male', 'female', 'neutral']
        if item.get('gender', '').lower() not in valid_genders:
            raise ValueError(f'gender 必须是: {", ".join(valid_genders)}')
    
    def create_voice_from_json(self, item):
        """从JSON项创建语音模型"""
        # 检查是否已存在
        if VoiceModel.objects.filter(name=item['name']).exists():
            raise ValueError(f'语音模型 "{item["name"]}" 已存在')
        
        # 创建语音模型
        voice = VoiceModel.objects.create(
            name=item['name'].strip(),
            description=item.get('description', ''),
            language=item['language'].strip(),
            gender=item['gender'].lower().strip(),
            age_range=item.get('age_range', 'adult'),
            voice_style=item.get('voice_style', 'natural'),
            computing_power_cost=item.get('computing_power_cost', 5),
            is_public=item.get('is_public', True),
            is_active=item.get('is_active', True),
            audio_sample_url=item.get('audio_sample_url', ''),
            model_file_url=item.get('model_file_url', ''),
        )
        
        # 处理分类
        if item.get('categories'):
            for category_name in item['categories']:
                if category_name:
                    category, _ = VoiceCategory.objects.get_or_create(
                        name=category_name,
                        defaults={'description': f'{category_name}分类'}
                    )
                    voice.categories.add(category)
        
        # 处理标签
        if item.get('tags'):
            for tag_name in item['tags']:
                if tag_name:
                    tag, _ = VoiceTag.objects.get_or_create(
                        name=tag_name,
                        defaults={'color': '#28a745'}
                    )
                    voice.tags.add(tag)
        
        return voice
