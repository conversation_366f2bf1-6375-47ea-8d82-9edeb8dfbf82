"""
配置管理序列化器
"""
from rest_framework import serializers
from .models import SystemConfig


class SystemConfigSerializer(serializers.ModelSerializer):
    """系统配置序列化器"""

    class Meta:
        model = SystemConfig
        fields = [
            'id', 'key', 'name', 'value', 'description',
            'config_type', 'value_type', 'default_value',
            'is_active', 'is_sensitive', 'is_readonly', 'sort_order',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def to_representation(self, instance):
        """自定义序列化输出"""
        data = super().to_representation(instance)
        
        # 如果不是超级用户，隐藏敏感信息
        request = self.context.get('request')
        if request and not request.user.is_superuser:
            if instance.is_sensitive:
                data['value'] = '***'
        
        return data
