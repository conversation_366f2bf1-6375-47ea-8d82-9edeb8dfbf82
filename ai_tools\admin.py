from django.contrib import admin
from .models import ToolCategory, AITool, ToolUsageLog


@admin.register(ToolCategory)
class ToolCategoryAdmin(admin.ModelAdmin):
    list_display = ['name', 'key', 'icon', 'color', 'sort_order', 'is_active', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'key']
    ordering = ['sort_order', 'id']
    list_editable = ['sort_order', 'is_active']


@admin.register(AITool)
class AIToolAdmin(admin.ModelAdmin):
    list_display = ['name', 'category', 'status', 'usage_count', 'rating', 'is_new', 'is_pro', 'sort_order', 'created_at']
    list_filter = ['category', 'status', 'is_new', 'is_pro', 'created_at']
    search_fields = ['name', 'description']
    ordering = ['sort_order', 'id']
    list_editable = ['status', 'sort_order', 'is_new', 'is_pro']
    readonly_fields = ['usage_count', 'created_at', 'updated_at']

    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'description', 'category', 'icon', 'gradient')
        }),
        ('配置信息', {
            'fields': ('tags', 'route_path', 'api_endpoint', 'sort_order')
        }),
        ('状态信息', {
            'fields': ('status', 'is_new', 'is_pro')
        }),
        ('统计信息', {
            'fields': ('usage_count', 'rating'),
            'classes': ('collapse',)
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(ToolUsageLog)
class ToolUsageLogAdmin(admin.ModelAdmin):
    list_display = ['user', 'tool', 'status', 'cost_points', 'processing_time', 'created_at']
    list_filter = ['status', 'tool', 'created_at']
    search_fields = ['user__username', 'tool__name', 'session_id']
    ordering = ['-created_at']
    readonly_fields = ['created_at']

    fieldsets = (
        ('基本信息', {
            'fields': ('user', 'tool', 'session_id', 'status')
        }),
        ('数据信息', {
            'fields': ('input_data', 'output_data'),
            'classes': ('collapse',)
        }),
        ('统计信息', {
            'fields': ('processing_time', 'cost_points', 'error_message')
        }),
        ('时间信息', {
            'fields': ('created_at',)
        }),
    )
