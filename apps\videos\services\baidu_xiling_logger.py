"""
百度曦灵数字人服务专用日志系统
提供详细的API调用日志、错误追踪和性能监控
"""

import logging
import json
import time
from datetime import datetime
from functools import wraps
from typing import Dict, Any, Optional
from django.conf import settings
from django.core.cache import cache


class BaiduXilingLogger:
    """百度曦灵专用日志记录器"""
    
    def __init__(self):
        self.logger = logging.getLogger('baidu_xiling')
        self.setup_logger()
    
    def setup_logger(self):
        """设置日志记录器"""
        if not self.logger.handlers:
            # 创建文件处理器
            handler = logging.FileHandler('logs/baidu_xiling.log', encoding='utf-8')
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)
    
    def log_api_call(self, method: str, url: str, params: Dict = None, 
                     response_data: Dict = None, duration: float = None,
                     success: bool = True, error: str = None):
        """记录API调用日志"""
        log_data = {
            'timestamp': datetime.now().isoformat(),
            'method': method,
            'url': url,
            'params': self._sanitize_params(params),
            'success': success,
            'duration_ms': round(duration * 1000, 2) if duration else None,
            'response_status': response_data.get('status') if response_data else None,
            'error': error
        }
        
        if success:
            self.logger.info(f"API调用成功: {json.dumps(log_data, ensure_ascii=False)}")
        else:
            self.logger.error(f"API调用失败: {json.dumps(log_data, ensure_ascii=False)}")
        
        # 更新统计信息
        self._update_stats(success, duration, error)
    
    def log_task_progress(self, task_id: str, task_type: str, progress: int, 
                         message: str, status: str = 'running'):
        """记录任务进度日志"""
        log_data = {
            'timestamp': datetime.now().isoformat(),
            'task_id': task_id,
            'task_type': task_type,
            'progress': progress,
            'message': message,
            'status': status
        }
        
        self.logger.info(f"任务进度: {json.dumps(log_data, ensure_ascii=False)}")
    
    def log_error(self, error_type: str, error_message: str, context: Dict = None):
        """记录错误日志"""
        log_data = {
            'timestamp': datetime.now().isoformat(),
            'error_type': error_type,
            'error_message': error_message,
            'context': context or {}
        }
        
        self.logger.error(f"系统错误: {json.dumps(log_data, ensure_ascii=False)}")
    
    def log_performance(self, operation: str, duration: float, details: Dict = None):
        """记录性能日志"""
        log_data = {
            'timestamp': datetime.now().isoformat(),
            'operation': operation,
            'duration_ms': round(duration * 1000, 2),
            'details': details or {}
        }
        
        if duration > 5.0:  # 超过5秒的操作记录为警告
            self.logger.warning(f"性能警告: {json.dumps(log_data, ensure_ascii=False)}")
        else:
            self.logger.info(f"性能监控: {json.dumps(log_data, ensure_ascii=False)}")
    
    def _sanitize_params(self, params: Dict) -> Dict:
        """清理敏感参数"""
        if not params:
            return {}
        
        sanitized = params.copy()
        sensitive_keys = ['api_key', 'secret_key', 'access_token', 'password']
        
        for key in sensitive_keys:
            if key in sanitized:
                sanitized[key] = '***HIDDEN***'
        
        return sanitized
    
    def _update_stats(self, success: bool, duration: float, error: str):
        """更新统计信息"""
        try:
            # 获取当前统计
            stats_key = 'baidu_xiling_stats'
            stats = cache.get(stats_key, {
                'total_calls': 0,
                'success_calls': 0,
                'failed_calls': 0,
                'avg_duration': 0,
                'error_types': {},
                'last_updated': datetime.now().isoformat()
            })
            
            # 更新统计
            stats['total_calls'] += 1
            if success:
                stats['success_calls'] += 1
            else:
                stats['failed_calls'] += 1
                if error:
                    error_type = error.split(':')[0] if ':' in error else error
                    stats['error_types'][error_type] = stats['error_types'].get(error_type, 0) + 1
            
            # 更新平均响应时间
            if duration:
                current_avg = stats['avg_duration']
                total_calls = stats['total_calls']
                stats['avg_duration'] = (current_avg * (total_calls - 1) + duration) / total_calls
            
            stats['last_updated'] = datetime.now().isoformat()
            
            # 缓存统计信息（24小时）
            cache.set(stats_key, stats, 86400)
            
        except Exception as e:
            self.logger.error(f"更新统计信息失败: {e}")
    
    def get_stats(self) -> Dict:
        """获取统计信息"""
        return cache.get('baidu_xiling_stats', {})


# 全局日志记录器实例
baidu_logger = BaiduXilingLogger()


def log_api_call(func):
    """API调用日志装饰器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        method_name = func.__name__
        
        try:
            result = func(*args, **kwargs)
            duration = time.time() - start_time
            
            # 记录成功调用
            baidu_logger.log_api_call(
                method=method_name,
                url=kwargs.get('url', 'unknown'),
                params=kwargs.get('params'),
                response_data=result,
                duration=duration,
                success=result.get('success', False) if isinstance(result, dict) else True
            )
            
            return result
            
        except Exception as e:
            duration = time.time() - start_time
            error_message = str(e)
            
            # 记录失败调用
            baidu_logger.log_api_call(
                method=method_name,
                url=kwargs.get('url', 'unknown'),
                params=kwargs.get('params'),
                duration=duration,
                success=False,
                error=error_message
            )
            
            raise
    
    return wrapper


def log_performance(operation_name: str):
    """性能监控装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            
            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time
                
                baidu_logger.log_performance(
                    operation=f"{operation_name}.{func.__name__}",
                    duration=duration,
                    details={'args_count': len(args), 'kwargs_count': len(kwargs)}
                )
                
                return result
                
            except Exception as e:
                duration = time.time() - start_time
                baidu_logger.log_performance(
                    operation=f"{operation_name}.{func.__name__}",
                    duration=duration,
                    details={'error': str(e), 'args_count': len(args), 'kwargs_count': len(kwargs)}
                )
                raise
        
        return wrapper
    return decorator


class BaiduXilingException(Exception):
    """百度曦灵服务专用异常类"""
    
    def __init__(self, message: str, error_code: str = None, 
                 api_response: Dict = None, context: Dict = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.api_response = api_response
        self.context = context or {}
        
        # 记录异常日志
        baidu_logger.log_error(
            error_type=self.__class__.__name__,
            error_message=message,
            context={
                'error_code': error_code,
                'api_response': api_response,
                **self.context
            }
        )


class APIConnectionError(BaiduXilingException):
    """API连接错误"""
    pass


class AuthenticationError(BaiduXilingException):
    """认证错误"""
    pass


class RateLimitError(BaiduXilingException):
    """频率限制错误"""
    pass


class ResourceNotFoundError(BaiduXilingException):
    """资源未找到错误"""
    pass


class ProcessingError(BaiduXilingException):
    """处理错误"""
    pass


def handle_api_errors(response_data: Dict, operation: str) -> None:
    """统一的API错误处理"""
    if not response_data:
        raise APIConnectionError(f"{operation}: 未收到API响应")
    
    if not response_data.get('success', True):
        error_message = response_data.get('error', '未知错误')
        error_code = response_data.get('error_code')
        
        # 根据错误类型抛出相应异常
        if 'authentication' in error_message.lower() or 'unauthorized' in error_message.lower():
            raise AuthenticationError(f"{operation}: {error_message}", error_code, response_data)
        elif 'rate limit' in error_message.lower() or 'quota' in error_message.lower():
            raise RateLimitError(f"{operation}: {error_message}", error_code, response_data)
        elif 'not found' in error_message.lower():
            raise ResourceNotFoundError(f"{operation}: {error_message}", error_code, response_data)
        elif 'processing' in error_message.lower() or 'generation' in error_message.lower():
            raise ProcessingError(f"{operation}: {error_message}", error_code, response_data)
        else:
            raise BaiduXilingException(f"{operation}: {error_message}", error_code, response_data)


# 导出主要组件
__all__ = [
    'BaiduXilingLogger',
    'baidu_logger',
    'log_api_call',
    'log_performance',
    'BaiduXilingException',
    'APIConnectionError',
    'AuthenticationError',
    'RateLimitError',
    'ResourceNotFoundError',
    'ProcessingError',
    'handle_api_errors'
]
