# Generated by Django 5.2.4 on 2025-07-15 02:42

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("avatars", "0001_initial"),
        ("voices", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="VideoProduction",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "title",
                    models.CharField(
                        help_text="视频作品的标题", max_length=200, verbose_name="视频标题"
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        blank=True, help_text="视频作品的描述", verbose_name="视频描述"
                    ),
                ),
                (
                    "drive_mode",
                    models.CharField(
                        choices=[("text", "文本驱动"), ("audio", "语音驱动")],
                        default="text",
                        help_text="视频生成的驱动模式",
                        max_length=10,
                        verbose_name="驱动模式",
                    ),
                ),
                (
                    "text_content",
                    models.TextField(
                        blank=True, help_text="文本驱动模式下的文本内容", verbose_name="文本内容"
                    ),
                ),
                (
                    "audio_file",
                    models.FileField(
                        blank=True,
                        help_text="语音驱动模式下的音频文件",
                        null=True,
                        upload_to="videos/audio_sources/",
                        verbose_name="音频文件",
                    ),
                ),
                (
                    "audio_url",
                    models.URLField(
                        blank=True,
                        help_text="语音驱动模式下的音频链接",
                        null=True,
                        verbose_name="音频链接",
                    ),
                ),
                (
                    "voice_volume",
                    models.FloatField(
                        default=50.0, help_text="语音音量（0-100）", verbose_name="音量"
                    ),
                ),
                (
                    "voice_pitch",
                    models.FloatField(
                        default=1.0, help_text="语音音调（0-2）", verbose_name="音调"
                    ),
                ),
                (
                    "voice_speed",
                    models.FloatField(
                        default=1.0, help_text="语音语速（0-2）", verbose_name="语速"
                    ),
                ),
                (
                    "video_quality",
                    models.CharField(
                        choices=[("720p", "720P"), ("1080p", "1080P"), ("4k", "4K")],
                        default="1080p",
                        help_text="视频输出质量",
                        max_length=10,
                        verbose_name="视频质量",
                    ),
                ),
                (
                    "background_music",
                    models.FileField(
                        blank=True,
                        help_text="视频背景音乐文件",
                        null=True,
                        upload_to="videos/background_music/",
                        verbose_name="背景音乐",
                    ),
                ),
                (
                    "background_music_volume",
                    models.FloatField(
                        default=20.0, help_text="背景音乐音量（0-100）", verbose_name="背景音乐音量"
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("draft", "草稿"),
                            ("pending", "待生成"),
                            ("processing", "生成中"),
                            ("completed", "已完成"),
                            ("failed", "生成失败"),
                            ("cancelled", "已取消"),
                        ],
                        default="draft",
                        help_text="视频生成状态",
                        max_length=20,
                        verbose_name="生成状态",
                    ),
                ),
                (
                    "progress",
                    models.PositiveIntegerField(
                        default=0, help_text="视频生成进度百分比（0-100）", verbose_name="生成进度"
                    ),
                ),
                (
                    "error_message",
                    models.TextField(
                        blank=True, help_text="生成失败时的错误信息", verbose_name="错误信息"
                    ),
                ),
                (
                    "result_video",
                    models.FileField(
                        blank=True,
                        help_text="生成的视频文件",
                        null=True,
                        upload_to="videos/results/",
                        verbose_name="生成视频",
                    ),
                ),
                (
                    "result_video_url",
                    models.URLField(
                        blank=True,
                        help_text="生成视频的在线链接",
                        null=True,
                        verbose_name="视频链接",
                    ),
                ),
                (
                    "video_duration",
                    models.PositiveIntegerField(
                        blank=True,
                        help_text="生成视频的时长（秒）",
                        null=True,
                        verbose_name="视频时长",
                    ),
                ),
                (
                    "video_size",
                    models.PositiveIntegerField(
                        blank=True,
                        help_text="生成视频的文件大小（字节）",
                        null=True,
                        verbose_name="视频大小",
                    ),
                ),
                (
                    "thumbnail",
                    models.ImageField(
                        blank=True,
                        help_text="视频的缩略图",
                        null=True,
                        upload_to="videos/thumbnails/",
                        verbose_name="视频缩略图",
                    ),
                ),
                (
                    "computing_power_consumed",
                    models.PositiveIntegerField(
                        default=0, help_text="本次生成消耗的算力点数", verbose_name="消耗算力"
                    ),
                ),
                (
                    "view_count",
                    models.PositiveIntegerField(
                        default=0, help_text="视频被观看的次数", verbose_name="观看次数"
                    ),
                ),
                (
                    "download_count",
                    models.PositiveIntegerField(
                        default=0, help_text="视频被下载的次数", verbose_name="下载次数"
                    ),
                ),
                (
                    "is_public",
                    models.BooleanField(
                        default=False, help_text="是否公开展示此视频", verbose_name="是否公开"
                    ),
                ),
                (
                    "is_favorite",
                    models.BooleanField(
                        default=False, help_text="用户是否收藏此视频", verbose_name="是否收藏"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "started_at",
                    models.DateTimeField(
                        blank=True,
                        help_text="开始生成视频的时间",
                        null=True,
                        verbose_name="开始生成时间",
                    ),
                ),
                (
                    "completed_at",
                    models.DateTimeField(
                        blank=True,
                        help_text="视频生成完成的时间",
                        null=True,
                        verbose_name="完成时间",
                    ),
                ),
                (
                    "avatar",
                    models.ForeignKey(
                        help_text="使用的数字人形象",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="video_productions",
                        to="avatars.digitalavatar",
                        verbose_name="数字人形象",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="video_productions",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="用户",
                    ),
                ),
                (
                    "voice_model",
                    models.ForeignKey(
                        help_text="使用的语音模型",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="video_productions",
                        to="voices.voicemodel",
                        verbose_name="语音模型",
                    ),
                ),
            ],
            options={
                "verbose_name": "视频作品",
                "verbose_name_plural": "视频作品",
                "db_table": "video_productions",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="VideoShare",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "share_token",
                    models.CharField(
                        help_text="用于分享的唯一令牌",
                        max_length=64,
                        unique=True,
                        verbose_name="分享令牌",
                    ),
                ),
                (
                    "password",
                    models.CharField(
                        blank=True,
                        help_text="访问分享链接的密码",
                        max_length=20,
                        verbose_name="访问密码",
                    ),
                ),
                (
                    "expire_at",
                    models.DateTimeField(
                        blank=True,
                        help_text="分享链接的过期时间",
                        null=True,
                        verbose_name="过期时间",
                    ),
                ),
                (
                    "view_count",
                    models.PositiveIntegerField(
                        default=0, help_text="分享链接被访问的次数", verbose_name="访问次数"
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True, help_text="分享链接是否有效", verbose_name="是否有效"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "video",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="shares",
                        to="videos.videoproduction",
                        verbose_name="视频",
                    ),
                ),
            ],
            options={
                "verbose_name": "视频分享",
                "verbose_name_plural": "视频分享",
                "db_table": "video_shares",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="VideoTemplate",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        help_text="视频模板的名称", max_length=100, verbose_name="模板名称"
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        blank=True, help_text="模板的详细描述", verbose_name="模板描述"
                    ),
                ),
                (
                    "category",
                    models.CharField(
                        choices=[
                            ("business", "商务"),
                            ("education", "教育"),
                            ("entertainment", "娱乐"),
                            ("news", "新闻"),
                            ("marketing", "营销"),
                            ("personal", "个人"),
                        ],
                        help_text="模板的分类",
                        max_length=50,
                        verbose_name="模板分类",
                    ),
                ),
                (
                    "template_content",
                    models.TextField(
                        blank=True, help_text="模板的默认文本内容", verbose_name="模板内容"
                    ),
                ),
                (
                    "template_settings",
                    models.JSONField(
                        blank=True,
                        default=dict,
                        help_text="模板的默认设置参数",
                        verbose_name="模板设置",
                    ),
                ),
                (
                    "thumbnail",
                    models.ImageField(
                        blank=True,
                        help_text="模板的预览图",
                        null=True,
                        upload_to="videos/template_thumbnails/",
                        verbose_name="模板缩略图",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True, help_text="是否启用此模板", verbose_name="是否启用"
                    ),
                ),
                (
                    "usage_count",
                    models.PositiveIntegerField(
                        default=0, help_text="模板被使用的次数", verbose_name="使用次数"
                    ),
                ),
                (
                    "sort_order",
                    models.PositiveIntegerField(
                        default=0, help_text="显示排序，数字越小越靠前", verbose_name="排序"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="created_video_templates",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="创建者",
                    ),
                ),
                (
                    "default_avatar",
                    models.ForeignKey(
                        blank=True,
                        help_text="模板的默认数字人形象",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="default_templates",
                        to="avatars.digitalavatar",
                        verbose_name="默认数字人",
                    ),
                ),
                (
                    "default_voice",
                    models.ForeignKey(
                        blank=True,
                        help_text="模板的默认语音模型",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="default_templates",
                        to="voices.voicemodel",
                        verbose_name="默认语音",
                    ),
                ),
            ],
            options={
                "verbose_name": "视频模板",
                "verbose_name_plural": "视频模板",
                "db_table": "video_templates",
                "ordering": ["sort_order", "-usage_count"],
            },
        ),
        migrations.AddIndex(
            model_name="videoproduction",
            index=models.Index(
                fields=["user", "status"], name="video_produ_user_id_ca9ee3_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="videoproduction",
            index=models.Index(
                fields=["avatar", "created_at"], name="video_produ_avatar__86a824_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="videoproduction",
            index=models.Index(
                fields=["voice_model", "created_at"],
                name="video_produ_voice_m_29b057_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="videoproduction",
            index=models.Index(
                fields=["status", "created_at"], name="video_produ_status_5e6031_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="videoproduction",
            index=models.Index(
                fields=["is_public", "created_at"],
                name="video_produ_is_publ_484354_idx",
            ),
        ),
    ]
