from rest_framework import serializers
from .models import Tool<PERSON>ategory, AITool, ToolUsageLog


class ToolCategorySerializer(serializers.ModelSerializer):
    """工具分类序列化器"""
    
    class Meta:
        model = ToolCategory
        fields = ['id', 'name', 'key', 'icon', 'color', 'sort_order', 'is_active']


class AIToolSerializer(serializers.ModelSerializer):
    """AI工具序列化器"""
    category_name = serializers.CharField(source='category.name', read_only=True)
    category_key = serializers.CharField(source='category.key', read_only=True)
    available = serializers.BooleanField(read_only=True)
    
    class Meta:
        model = AITool
        fields = [
            'id', 'name', 'description', 'icon', 'gradient', 'category', 
            'category_name', 'category_key', 'tags', 'usage_count', 'rating', 
            'status', 'is_new', 'is_pro', 'sort_order', 'route_path', 
            'api_endpoint', 'available', 'created_at', 'updated_at'
        ]


class ToolUsageLogSerializer(serializers.ModelSerializer):
    """工具使用记录序列化器"""
    user_name = serializers.CharField(source='user.username', read_only=True)
    tool_name = serializers.CharField(source='tool.name', read_only=True)
    
    class Meta:
        model = ToolUsageLog
        fields = [
            'id', 'user', 'user_name', 'tool', 'tool_name', 'session_id',
            'input_data', 'output_data', 'processing_time', 'cost_points',
            'status', 'error_message', 'created_at'
        ]
        read_only_fields = ['user', 'created_at']


class ToolUsageCreateSerializer(serializers.ModelSerializer):
    """创建工具使用记录序列化器"""
    
    class Meta:
        model = ToolUsageLog
        fields = [
            'tool', 'session_id', 'input_data', 'output_data', 
            'processing_time', 'cost_points', 'status', 'error_message'
        ]
    
    def create(self, validated_data):
        validated_data['user'] = self.context['request'].user
        return super().create(validated_data)
