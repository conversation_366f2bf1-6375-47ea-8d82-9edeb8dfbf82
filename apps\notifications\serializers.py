"""
通知序列化器
"""
from rest_framework import serializers
from .models import Notification, UserNotificationSettings


class NotificationSerializer(serializers.ModelSerializer):
    """通知序列化器"""
    type_display = serializers.CharField(source='get_type_display', read_only=True)
    priority_display = serializers.CharField(source='get_priority_display', read_only=True)
    extra_data = serializers.SerializerMethodField()
    
    class Meta:
        model = Notification
        fields = [
            'id', 'type', 'type_display', 'title', 'message', 
            'priority', 'priority_display', 'extra_data',
            'is_read', 'created_at', 'read_at'
        ]
        read_only_fields = ['id', 'created_at', 'read_at']
    
    def get_extra_data(self, obj):
        """获取额外数据"""
        return obj.get_extra_data_dict()


class UserNotificationSettingsSerializer(serializers.ModelSerializer):
    """用户通知设置序列化器"""
    
    class Meta:
        model = UserNotificationSettings
        fields = [
            'email_notifications', 'browser_notifications',
            'video_progress_notifications', 'system_notifications',
            'package_notifications'
        ]
