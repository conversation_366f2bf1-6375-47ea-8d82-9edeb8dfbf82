"""
内容审核信号处理器
"""
from django.db.models.signals import post_save
from django.dispatch import receiver
from .services import moderation_service


@receiver(post_save, sender='copywriting.Copywriting')
def auto_moderate_copywriting(sender, instance, created, **kwargs):
    """自动审核文案内容"""
    if created:
        # 新创建的文案自动提交审核
        moderation_service.submit_for_review(
            content_object=instance,
            user=instance.user,
            priority='normal'
        )


@receiver(post_save, sender='videos.VideoProduction')
def auto_moderate_video(sender, instance, created, **kwargs):
    """自动审核视频内容"""
    if created:
        # 新创建的视频自动提交审核
        moderation_service.submit_for_review(
            content_object=instance,
            user=instance.user,
            priority='normal'
        )


@receiver(post_save, sender='avatars.DigitalAvatar')
def auto_moderate_avatar(sender, instance, created, **kwargs):
    """自动审核数字人形象"""
    if created and not instance.owner:
        # 新创建的公共数字人形象需要审核
        moderation_service.submit_for_review(
            content_object=instance,
            user=instance.owner or instance.created_by if hasattr(instance, 'created_by') else None,
            priority='high'
        )


@receiver(post_save, sender='voices.VoiceModel')
def auto_moderate_voice(sender, instance, created, **kwargs):
    """自动审核语音模型"""
    if created and instance.is_public:
        # 新创建的公共语音模型需要审核
        moderation_service.submit_for_review(
            content_object=instance,
            user=instance.owner if hasattr(instance, 'owner') else None,
            priority='high'
        )
