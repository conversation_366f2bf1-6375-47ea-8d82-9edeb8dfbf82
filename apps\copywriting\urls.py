"""
智能文案相关API路由
"""
from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

router = DefaultRouter()
router.register(r'copywriting', views.CopywritingViewSet)
router.register(r'copywriting-templates', views.CopywritingTemplateViewSet)
router.register(r'copywriting-history', views.CopywritingHistoryViewSet)
router.register(r'text-extractions', views.TextExtractionViewSet)

urlpatterns = [
    path('', include(router.urls)),
]
