"""
内容审核数据模型
"""
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.contrib.auth import get_user_model
from django.contrib.contenttypes.models import ContentType
from django.contrib.contenttypes.fields import GenericForeignKey
import json

User = get_user_model()


class ModerationRule(models.Model):
    """审核规则"""
    
    RULE_TYPE_CHOICES = [
        ('keyword', _('关键词过滤')),
        ('length', _('长度限制')),
        ('format', _('格式检查')),
        ('ai_detection', _('AI检测')),
        ('manual', _('人工审核')),
    ]
    
    name = models.CharField(_('规则名称'), max_length=100)
    rule_type = models.CharField(_('规则类型'), max_length=20, choices=RULE_TYPE_CHOICES)
    description = models.TextField(_('规则描述'), blank=True)
    
    # 规则配置（JSON格式）
    config = models.JSONField(_('规则配置'), default=dict, help_text='JSON格式的规则配置')
    
    # 适用的内容类型
    content_types = models.ManyToManyField(
        ContentType,
        verbose_name=_('适用内容类型'),
        help_text='此规则适用的内容类型'
    )
    
    # 状态
    is_active = models.BooleanField(_('启用'), default=True)
    severity = models.CharField(_('严重程度'), max_length=10, choices=[
        ('low', _('低')),
        ('medium', _('中')),
        ('high', _('高')),
        ('critical', _('严重')),
    ], default='medium')
    
    # 时间
    created_at = models.DateTimeField(_('创建时间'), auto_now_add=True)
    updated_at = models.DateTimeField(_('更新时间'), auto_now=True)
    
    class Meta:
        verbose_name = _('审核规则')
        verbose_name_plural = _('审核规则')
        ordering = ['-created_at']
    
    def __str__(self):
        return f'{self.name} ({self.get_rule_type_display()})'
    
    def get_config_dict(self):
        """获取配置字典"""
        return self.config if isinstance(self.config, dict) else {}


class ModerationRequest(models.Model):
    """审核请求"""
    
    STATUS_CHOICES = [
        ('pending', _('待审核')),
        ('approved', _('已通过')),
        ('rejected', _('已拒绝')),
        ('flagged', _('已标记')),
        ('auto_approved', _('自动通过')),
        ('auto_rejected', _('自动拒绝')),
    ]
    
    PRIORITY_CHOICES = [
        ('low', _('低')),
        ('normal', _('普通')),
        ('high', _('高')),
        ('urgent', _('紧急')),
    ]
    
    # 关联的内容对象
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    object_id = models.PositiveIntegerField()
    content_object = GenericForeignKey('content_type', 'object_id')
    
    # 提交者
    submitted_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name=_('提交者'))
    
    # 审核状态
    status = models.CharField(_('状态'), max_length=20, choices=STATUS_CHOICES, default='pending')
    priority = models.CharField(_('优先级'), max_length=10, choices=PRIORITY_CHOICES, default='normal')
    
    # 审核员
    reviewer = models.ForeignKey(
        User, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        related_name='reviewed_requests',
        verbose_name=_('审核员')
    )
    
    # 审核结果
    review_notes = models.TextField(_('审核备注'), blank=True)
    rejection_reason = models.TextField(_('拒绝原因'), blank=True)
    
    # 自动审核结果
    auto_review_score = models.FloatField(_('自动审核分数'), null=True, blank=True)
    auto_review_details = models.JSONField(_('自动审核详情'), default=dict, blank=True)
    
    # 时间
    created_at = models.DateTimeField(_('创建时间'), auto_now_add=True)
    reviewed_at = models.DateTimeField(_('审核时间'), null=True, blank=True)
    
    class Meta:
        verbose_name = _('审核请求')
        verbose_name_plural = _('审核请求')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['status', 'priority']),
            models.Index(fields=['content_type', 'object_id']),
        ]
    
    def __str__(self):
        return f'{self.content_type.name} - {self.get_status_display()}'
    
    def approve(self, reviewer, notes=''):
        """通过审核"""
        from django.utils import timezone
        self.status = 'approved'
        self.reviewer = reviewer
        self.review_notes = notes
        self.reviewed_at = timezone.now()
        self.save()
    
    def reject(self, reviewer, reason, notes=''):
        """拒绝审核"""
        from django.utils import timezone
        self.status = 'rejected'
        self.reviewer = reviewer
        self.rejection_reason = reason
        self.review_notes = notes
        self.reviewed_at = timezone.now()
        self.save()
    
    def flag(self, reviewer, notes=''):
        """标记内容"""
        from django.utils import timezone
        self.status = 'flagged'
        self.reviewer = reviewer
        self.review_notes = notes
        self.reviewed_at = timezone.now()
        self.save()


class ModerationLog(models.Model):
    """审核日志"""
    
    ACTION_CHOICES = [
        ('submit', _('提交审核')),
        ('approve', _('通过审核')),
        ('reject', _('拒绝审核')),
        ('flag', _('标记内容')),
        ('auto_approve', _('自动通过')),
        ('auto_reject', _('自动拒绝')),
        ('rule_triggered', _('规则触发')),
    ]
    
    moderation_request = models.ForeignKey(
        ModerationRequest, 
        on_delete=models.CASCADE,
        related_name='logs',
        verbose_name=_('审核请求')
    )
    
    action = models.CharField(_('操作'), max_length=20, choices=ACTION_CHOICES)
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, verbose_name=_('操作用户'))
    
    # 操作详情
    details = models.JSONField(_('操作详情'), default=dict, blank=True)
    notes = models.TextField(_('备注'), blank=True)
    
    # 时间
    created_at = models.DateTimeField(_('创建时间'), auto_now_add=True)
    
    class Meta:
        verbose_name = _('审核日志')
        verbose_name_plural = _('审核日志')
        ordering = ['-created_at']
    
    def __str__(self):
        return f'{self.get_action_display()} - {self.created_at}'


class ContentFlag(models.Model):
    """内容举报"""
    
    FLAG_TYPE_CHOICES = [
        ('spam', _('垃圾内容')),
        ('inappropriate', _('不当内容')),
        ('copyright', _('版权侵犯')),
        ('harassment', _('骚扰')),
        ('violence', _('暴力内容')),
        ('hate_speech', _('仇恨言论')),
        ('misinformation', _('虚假信息')),
        ('other', _('其他')),
    ]
    
    # 被举报的内容
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    object_id = models.PositiveIntegerField()
    content_object = GenericForeignKey('content_type', 'object_id')
    
    # 举报者
    reporter = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name=_('举报者'))
    
    # 举报信息
    flag_type = models.CharField(_('举报类型'), max_length=20, choices=FLAG_TYPE_CHOICES)
    reason = models.TextField(_('举报原因'))
    
    # 处理状态
    is_resolved = models.BooleanField(_('已处理'), default=False)
    resolution_notes = models.TextField(_('处理备注'), blank=True)
    resolved_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='resolved_flags',
        verbose_name=_('处理人')
    )
    resolved_at = models.DateTimeField(_('处理时间'), null=True, blank=True)
    
    # 时间
    created_at = models.DateTimeField(_('创建时间'), auto_now_add=True)
    
    class Meta:
        verbose_name = _('内容举报')
        verbose_name_plural = _('内容举报')
        ordering = ['-created_at']
        unique_together = ['content_type', 'object_id', 'reporter']
    
    def __str__(self):
        return f'{self.get_flag_type_display()} - {self.content_type.name}'
    
    def resolve(self, resolver, notes=''):
        """处理举报"""
        from django.utils import timezone
        self.is_resolved = True
        self.resolved_by = resolver
        self.resolution_notes = notes
        self.resolved_at = timezone.now()
        self.save()
