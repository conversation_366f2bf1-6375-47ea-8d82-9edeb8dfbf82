"""
内容审核服务
"""
import re
import logging
from django.contrib.contenttypes.models import ContentType
from django.contrib.auth import get_user_model
from .models import ModerationRule, ModerationRequest, ModerationLog, ContentFlag

User = get_user_model()
logger = logging.getLogger(__name__)


class ModerationService:
    """内容审核服务"""
    
    def __init__(self):
        self.rules = ModerationRule.objects.filter(is_active=True)
    
    def submit_for_review(self, content_object, user, priority='normal'):
        """提交内容审核"""
        content_type = ContentType.objects.get_for_model(content_object)
        
        # 检查是否已存在审核请求
        existing_request = ModerationRequest.objects.filter(
            content_type=content_type,
            object_id=content_object.id,
            status__in=['pending', 'flagged']
        ).first()
        
        if existing_request:
            return existing_request
        
        # 创建审核请求
        request = ModerationRequest.objects.create(
            content_type=content_type,
            object_id=content_object.id,
            submitted_by=user,
            priority=priority
        )
        
        # 记录日志
        ModerationLog.objects.create(
            moderation_request=request,
            action='submit',
            user=user,
            notes=f'提交 {content_type.name} 审核'
        )
        
        # 执行自动审核
        self.auto_review(request, content_object)
        
        return request
    
    def auto_review(self, request, content_object):
        """自动审核"""
        content_type = ContentType.objects.get_for_model(content_object)
        applicable_rules = self.rules.filter(content_types=content_type)
        
        violations = []
        total_score = 0
        
        for rule in applicable_rules:
            try:
                result = self.apply_rule(rule, content_object)
                if result['violated']:
                    violations.append({
                        'rule': rule.name,
                        'type': rule.rule_type,
                        'severity': rule.severity,
                        'details': result.get('details', '')
                    })
                    
                    # 计算分数（违规程度）
                    severity_scores = {'low': 1, 'medium': 2, 'high': 3, 'critical': 4}
                    total_score += severity_scores.get(rule.severity, 2)
                    
            except Exception as e:
                logger.error(f'规则 {rule.name} 执行失败: {str(e)}')
        
        # 更新审核请求
        request.auto_review_score = total_score
        request.auto_review_details = {
            'violations': violations,
            'total_violations': len(violations),
            'total_score': total_score
        }
        
        # 根据分数决定自动审核结果
        if total_score == 0:
            # 无违规，自动通过
            request.status = 'auto_approved'
            ModerationLog.objects.create(
                moderation_request=request,
                action='auto_approve',
                details={'score': total_score, 'violations': violations}
            )
        elif total_score >= 5:
            # 严重违规，自动拒绝
            request.status = 'auto_rejected'
            ModerationLog.objects.create(
                moderation_request=request,
                action='auto_reject',
                details={'score': total_score, 'violations': violations}
            )
        else:
            # 需要人工审核
            request.priority = 'high' if total_score >= 3 else 'normal'
            ModerationLog.objects.create(
                moderation_request=request,
                action='rule_triggered',
                details={'score': total_score, 'violations': violations}
            )
        
        request.save()
        return request
    
    def apply_rule(self, rule, content_object):
        """应用审核规则"""
        config = rule.get_config_dict()
        
        if rule.rule_type == 'keyword':
            return self.check_keywords(content_object, config)
        elif rule.rule_type == 'length':
            return self.check_length(content_object, config)
        elif rule.rule_type == 'format':
            return self.check_format(content_object, config)
        elif rule.rule_type == 'ai_detection':
            return self.ai_detection(content_object, config)
        
        return {'violated': False}
    
    def check_keywords(self, content_object, config):
        """关键词检查"""
        keywords = config.get('keywords', [])
        case_sensitive = config.get('case_sensitive', False)
        
        # 获取要检查的文本
        text_fields = config.get('text_fields', ['title', 'description', 'content'])
        text_content = ''
        
        for field in text_fields:
            if hasattr(content_object, field):
                value = getattr(content_object, field, '')
                if value:
                    text_content += str(value) + ' '
        
        if not case_sensitive:
            text_content = text_content.lower()
            keywords = [kw.lower() for kw in keywords]
        
        # 检查关键词
        found_keywords = []
        for keyword in keywords:
            if keyword in text_content:
                found_keywords.append(keyword)
        
        return {
            'violated': len(found_keywords) > 0,
            'details': f'发现违规关键词: {", ".join(found_keywords)}' if found_keywords else ''
        }
    
    def check_length(self, content_object, config):
        """长度检查"""
        field = config.get('field', 'description')
        min_length = config.get('min_length', 0)
        max_length = config.get('max_length', 10000)
        
        if not hasattr(content_object, field):
            return {'violated': False}
        
        value = getattr(content_object, field, '')
        length = len(str(value)) if value else 0
        
        violated = length < min_length or length > max_length
        details = ''
        
        if length < min_length:
            details = f'{field} 长度不足，当前 {length}，最少需要 {min_length}'
        elif length > max_length:
            details = f'{field} 长度超限，当前 {length}，最多允许 {max_length}'
        
        return {
            'violated': violated,
            'details': details
        }
    
    def check_format(self, content_object, config):
        """格式检查"""
        field = config.get('field', 'title')
        pattern = config.get('pattern', '')
        
        if not pattern or not hasattr(content_object, field):
            return {'violated': False}
        
        value = getattr(content_object, field, '')
        if not value:
            return {'violated': False}
        
        try:
            match = re.search(pattern, str(value))
            violated = not match
            
            return {
                'violated': violated,
                'details': f'{field} 格式不符合要求' if violated else ''
            }
        except re.error:
            logger.error(f'正则表达式错误: {pattern}')
            return {'violated': False}
    
    def ai_detection(self, content_object, config):
        """AI检测（占位符，可接入第三方AI审核服务）"""
        # 这里可以接入百度、腾讯、阿里等AI审核服务
        # 暂时返回不违规
        return {'violated': False, 'details': 'AI检测功能待实现'}
    
    def flag_content(self, content_object, reporter, flag_type, reason):
        """举报内容"""
        content_type = ContentType.objects.get_for_model(content_object)
        
        # 检查是否已举报过
        existing_flag = ContentFlag.objects.filter(
            content_type=content_type,
            object_id=content_object.id,
            reporter=reporter
        ).first()
        
        if existing_flag:
            return existing_flag
        
        # 创建举报
        flag = ContentFlag.objects.create(
            content_type=content_type,
            object_id=content_object.id,
            reporter=reporter,
            flag_type=flag_type,
            reason=reason
        )
        
        # 如果举报数量达到阈值，自动提交审核
        flag_count = ContentFlag.objects.filter(
            content_type=content_type,
            object_id=content_object.id,
            is_resolved=False
        ).count()
        
        if flag_count >= 3:  # 3个举报自动审核
            self.submit_for_review(content_object, reporter, priority='high')
        
        return flag
    
    def get_pending_reviews(self, reviewer=None):
        """获取待审核内容"""
        queryset = ModerationRequest.objects.filter(status='pending')
        
        if reviewer:
            # 可以根据审核员权限过滤
            pass
        
        return queryset.order_by('-priority', '-created_at')


# 全局审核服务实例
moderation_service = ModerationService()
