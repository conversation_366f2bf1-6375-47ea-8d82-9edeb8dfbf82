"""
权限控制系统
"""
from rest_framework import permissions
from rest_framework.exceptions import PermissionDenied
from django.contrib.auth.models import Permission
from django.contrib.contenttypes.models import ContentType
from django.core.exceptions import ObjectDoesNotExist
import logging

logger = logging.getLogger('security')


class BasePermission(permissions.BasePermission):
    """基础权限类"""
    
    def has_permission(self, request, view):
        """检查用户是否有访问权限"""
        if not request.user or not request.user.is_authenticated:
            return False
        
        # 超级用户拥有所有权限
        if request.user.is_superuser:
            return True
        
        return True
    
    def has_object_permission(self, request, view, obj):
        """检查用户是否有对象权限"""
        return self.has_permission(request, view)


class IsOwnerOrReadOnly(BasePermission):
    """只有所有者可以修改，其他人只读"""
    
    def has_object_permission(self, request, view, obj):
        # 读取权限对所有人开放
        if request.method in permissions.SAFE_METHODS:
            return True
        
        # 写入权限只给所有者
        return obj.created_by == request.user or obj.user == request.user


class IsOwner(BasePermission):
    """只有所有者可以访问"""
    
    def has_object_permission(self, request, view, obj):
        return obj.created_by == request.user or obj.user == request.user


class IsAdminOrReadOnly(BasePermission):
    """管理员可以修改，其他人只读"""
    
    def has_permission(self, request, view):
        if request.method in permissions.SAFE_METHODS:
            return True
        
        return request.user and request.user.is_staff


class HasModelPermission(BasePermission):
    """基于Django模型权限的检查"""
    
    def get_required_permissions(self, method, model_cls):
        """获取所需权限"""
        app_label = model_cls._meta.app_label
        model_name = model_cls._meta.model_name
        
        if method == 'GET':
            return [f'{app_label}.view_{model_name}']
        elif method == 'POST':
            return [f'{app_label}.add_{model_name}']
        elif method in ['PUT', 'PATCH']:
            return [f'{app_label}.change_{model_name}']
        elif method == 'DELETE':
            return [f'{app_label}.delete_{model_name}']
        
        return []
    
    def has_permission(self, request, view):
        if not super().has_permission(request, view):
            return False
        
        # 获取模型类
        model_cls = getattr(view, 'queryset', None)
        if model_cls is not None:
            model_cls = model_cls.model
        else:
            model_cls = getattr(view, 'model', None)
        
        if not model_cls:
            return True
        
        # 检查权限
        required_perms = self.get_required_permissions(request.method, model_cls)
        return request.user.has_perms(required_perms)


class ResourcePermission(BasePermission):
    """资源权限控制"""
    
    # 权限映射
    PERMISSION_MAP = {
        'avatars': {
            'view': 'avatars.view_avatar',
            'add': 'avatars.add_avatar',
            'change': 'avatars.change_avatar',
            'delete': 'avatars.delete_avatar',
        },
        'voices': {
            'view': 'voices.view_voicemodel',
            'add': 'voices.add_voicemodel',
            'change': 'voices.change_voicemodel',
            'delete': 'voices.delete_voicemodel',
        },
        'videos': {
            'view': 'videos.view_videoproduction',
            'add': 'videos.add_videoproduction',
            'change': 'videos.change_videoproduction',
            'delete': 'videos.delete_videoproduction',
        },
        'copywriting': {
            'view': 'copywriting.view_copywriting',
            'add': 'copywriting.add_copywriting',
            'change': 'copywriting.change_copywriting',
            'delete': 'copywriting.delete_copywriting',
        },
    }
    
    def __init__(self, resource_type, action=None):
        self.resource_type = resource_type
        self.action = action
    
    def has_permission(self, request, view):
        if not super().has_permission(request, view):
            return False
        
        # 确定操作类型
        action = self.action
        if not action:
            if request.method == 'GET':
                action = 'view'
            elif request.method == 'POST':
                action = 'add'
            elif request.method in ['PUT', 'PATCH']:
                action = 'change'
            elif request.method == 'DELETE':
                action = 'delete'
        
        # 获取所需权限
        permissions_map = self.PERMISSION_MAP.get(self.resource_type, {})
        required_permission = permissions_map.get(action)
        
        if not required_permission:
            return True
        
        # 检查权限
        has_perm = request.user.has_perm(required_permission)
        
        if not has_perm:
            logger.warning(
                f"Permission denied: User {request.user.username} lacks {required_permission}",
                extra={
                    'user_id': request.user.id,
                    'resource_type': self.resource_type,
                    'action': action,
                    'required_permission': required_permission,
                }
            )
        
        return has_perm


class QuotaPermission(BasePermission):
    """配额权限控制"""
    
    def __init__(self, resource_type):
        self.resource_type = resource_type
    
    def has_permission(self, request, view):
        if not super().has_permission(request, view):
            return False
        
        # 只对创建操作检查配额
        if request.method != 'POST':
            return True
        
        return self.check_quota(request.user)
    
    def check_quota(self, user):
        """检查用户配额"""
        try:
            profile = user.profile
        except ObjectDoesNotExist:
            return False
        
        # 检查不同资源的配额
        if self.resource_type == 'avatars':
            current_count = user.avatars.count()
            max_count = getattr(profile, 'max_avatars', 10)
        elif self.resource_type == 'voices':
            current_count = user.voice_models.count()
            max_count = getattr(profile, 'max_voices', 5)
        elif self.resource_type == 'videos':
            current_count = user.video_productions.count()
            max_count = getattr(profile, 'max_videos', 20)
        else:
            return True
        
        if current_count >= max_count:
            logger.warning(
                f"Quota exceeded: User {user.username} reached {self.resource_type} limit",
                extra={
                    'user_id': user.id,
                    'resource_type': self.resource_type,
                    'current_count': current_count,
                    'max_count': max_count,
                }
            )
            return False
        
        return True


class CompositePermission(BasePermission):
    """组合权限"""
    
    def __init__(self, *permissions):
        self.permissions = permissions
    
    def has_permission(self, request, view):
        """所有权限都必须通过"""
        for permission in self.permissions:
            if not permission.has_permission(request, view):
                return False
        return True
    
    def has_object_permission(self, request, view, obj):
        """所有权限都必须通过"""
        for permission in self.permissions:
            if hasattr(permission, 'has_object_permission'):
                if not permission.has_object_permission(request, view, obj):
                    return False
        return True


# 常用权限组合
class AvatarPermission(CompositePermission):
    """数字人权限"""
    def __init__(self):
        super().__init__(
            ResourcePermission('avatars'),
            QuotaPermission('avatars')
        )


class VoicePermission(CompositePermission):
    """语音权限"""
    def __init__(self):
        super().__init__(
            ResourcePermission('voices'),
            QuotaPermission('voices')
        )


class VideoPermission(CompositePermission):
    """视频权限"""
    def __init__(self):
        super().__init__(
            ResourcePermission('videos'),
            QuotaPermission('videos')
        )


class CopywritingPermission(CompositePermission):
    """文案权限"""
    def __init__(self):
        super().__init__(
            ResourcePermission('copywriting')
        )
