"""
数字人形象相关序列化器
"""
from rest_framework import serializers
from .models import DigitalAvatar, AvatarCategory, AvatarTag, AvatarUsageLog


class AvatarCategorySerializer(serializers.ModelSerializer):
    """数字人形象分类序列化器"""
    avatar_count = serializers.IntegerField(source='avatars.count', read_only=True)
    
    class Meta:
        model = AvatarCategory
        fields = (
            'id', 'name', 'description', 'icon', 'sort_order', 
            'is_active', 'avatar_count', 'created_at'
        )
        read_only_fields = ('id', 'created_at')


class AvatarTagSerializer(serializers.ModelSerializer):
    """数字人形象标签序列化器"""
    avatar_count = serializers.IntegerField(source='avatars.count', read_only=True)
    
    class Meta:
        model = AvatarTag
        fields = (
            'id', 'name', 'color', 'usage_count', 
            'avatar_count', 'created_at'
        )
        read_only_fields = ('id', 'usage_count', 'created_at')


class DigitalAvatarSerializer(serializers.ModelSerializer):
    """数字人形象序列化器"""
    categories = AvatarCategorySerializer(many=True, read_only=True)
    tags = AvatarTagSerializer(many=True, read_only=True)
    owner_display = serializers.SerializerMethodField()
    model_version_display = serializers.CharField(source='get_model_version_display', read_only=True)
    scene_type_display = serializers.CharField(source='get_scene_type_display', read_only=True)
    video_thumbnail_url = serializers.CharField(read_only=True)

    class Meta:
        model = DigitalAvatar
        fields = (
            'id', 'name', 'description', 'model_version', 'model_version_display',
            'scene_type', 'scene_type_display', 'thumbnail', 'video_preview',
            'video_url', 'video_thumbnail_url', 'is_public', 'is_active',
            'owner', 'owner_display', 'computing_power_cost', 'sort_order',
            'usage_count', 'categories', 'tags', 'created_at', 'updated_at'
        )
        read_only_fields = ('id', 'usage_count', 'created_at', 'updated_at')

    def get_owner_display(self, obj):
        """获取所有者显示名称"""
        if obj.owner:
            return obj.owner.display_name
        return None


class AvatarUsageLogSerializer(serializers.ModelSerializer):
    """数字人形象使用日志序列化器"""
    avatar_name = serializers.CharField(source='avatar.name', read_only=True)
    user_display = serializers.CharField(source='user.display_name', read_only=True)
    
    class Meta:
        model = AvatarUsageLog
        fields = (
            'id', 'avatar', 'avatar_name', 'user', 'user_display',
            'computing_power_consumed', 'purpose', 'created_at'
        )
        read_only_fields = ('id', 'created_at')
