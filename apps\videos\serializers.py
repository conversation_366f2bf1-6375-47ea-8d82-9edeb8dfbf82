"""
视频生成相关序列化器
"""
from rest_framework import serializers
from .models import VideoProduction, VideoTemplate, VideoShare


class VideoProductionSerializer(serializers.ModelSerializer):
    """视频作品序列化器"""
    user_display = serializers.CharField(source='user.display_name', read_only=True)
    avatar_name = serializers.CharField(source='avatar.name', read_only=True)
    voice_model_name = serializers.CharField(source='voice_model.name', read_only=True)
    drive_mode_display = serializers.CharField(source='get_drive_mode_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    video_quality_display = serializers.CharField(source='get_video_quality_display', read_only=True)
    estimated_duration = serializers.CharField(read_only=True)
    video_thumbnail_url = serializers.CharField(read_only=True)
    
    class Meta:
        model = VideoProduction
        fields = (
            'id', 'user', 'user_display', 'title', 'description',
            'avatar', 'avatar_name', 'voice_model', 'voice_model_name',
            'drive_mode', 'drive_mode_display', 'text_content', 'audio_file',
            'audio_url', 'voice_volume', 'voice_pitch', 'voice_speed',
            'video_quality', 'video_quality_display', 'background_music',
            'background_music_volume', 'status', 'status_display', 'progress',
            'error_message', 'result_video', 'result_video_url', 'video_duration',
            'video_size', 'thumbnail', 'video_thumbnail_url', 'computing_power_consumed',
            'view_count', 'download_count', 'is_public', 'is_favorite',
            'estimated_duration', 'created_at', 'updated_at', 'started_at', 'completed_at'
        )
        read_only_fields = (
            'id', 'user', 'status', 'progress', 'error_message',
            'result_video', 'result_video_url', 'video_duration', 'video_size',
            'thumbnail', 'computing_power_consumed', 'view_count', 'download_count',
            'created_at', 'updated_at', 'started_at', 'completed_at'
        )


class VideoTemplateSerializer(serializers.ModelSerializer):
    """视频模板序列化器"""
    category_display = serializers.CharField(source='get_category_display', read_only=True)
    default_avatar_name = serializers.CharField(source='default_avatar.name', read_only=True)
    default_voice_name = serializers.CharField(source='default_voice.name', read_only=True)
    created_by_display = serializers.CharField(source='created_by.display_name', read_only=True)
    
    class Meta:
        model = VideoTemplate
        fields = (
            'id', 'name', 'description', 'category', 'category_display',
            'default_avatar', 'default_avatar_name', 'default_voice', 'default_voice_name',
            'template_content', 'template_settings', 'thumbnail', 'is_active',
            'usage_count', 'sort_order', 'created_by', 'created_by_display',
            'created_at', 'updated_at'
        )
        read_only_fields = (
            'id', 'usage_count', 'created_at', 'updated_at'
        )


class VideoShareSerializer(serializers.ModelSerializer):
    """视频分享序列化器"""
    video_title = serializers.CharField(source='video.title', read_only=True)
    
    class Meta:
        model = VideoShare
        fields = (
            'id', 'video', 'video_title', 'share_token', 'password',
            'expire_at', 'view_count', 'is_active', 'created_at'
        )
        read_only_fields = (
            'id', 'share_token', 'view_count', 'created_at'
        )
