"""
AI工具相关模型
"""
from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone

User = get_user_model()


class AIToolUsageLog(models.Model):
    """AI工具使用日志"""
    
    TOOL_CHOICES = [
        ('ai_chat', 'AI对话'),
        ('ai_painting', 'AI绘画'),
        ('voice_synthesis', '语音合成'),
        ('text_generation', '文本生成'),
    ]
    
    user = models.ForeignKey(
        User, 
        on_delete=models.CASCADE, 
        verbose_name='用户',
        related_name='ai_tool_usage_logs'
    )
    tool_name = models.CharField(
        max_length=50, 
        choices=TOOL_CHOICES,
        verbose_name='工具名称'
    )
    input_data = models.JSONField(
        verbose_name='输入数据',
        help_text='用户输入的数据'
    )
    output_data = models.JSONField(
        verbose_name='输出数据',
        help_text='AI生成的结果数据'
    )
    computing_power_consumed = models.IntegerField(
        default=0,
        verbose_name='消耗算力'
    )
    is_fallback = models.BooleanField(
        default=False,
        verbose_name='是否降级处理',
        help_text='当第三方服务不可用时的降级处理'
    )
    response_time = models.FloatField(
        null=True,
        blank=True,
        verbose_name='响应时间(秒)'
    )
    error_message = models.TextField(
        blank=True,
        verbose_name='错误信息'
    )
    created_at = models.DateTimeField(
        default=timezone.now,
        verbose_name='创建时间'
    )
    
    class Meta:
        verbose_name = 'AI工具使用日志'
        verbose_name_plural = 'AI工具使用日志'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', '-created_at']),
            models.Index(fields=['tool_name', '-created_at']),
        ]
    
    def __str__(self):
        return f"{self.user.username} - {self.get_tool_name_display()} - {self.created_at}"


class AIServiceConfig(models.Model):
    """AI服务配置"""
    
    SERVICE_CHOICES = [
        ('openai', 'OpenAI'),
        ('azure', 'Azure'),
        ('siliconflow', 'SiliconFlow'),
        ('local', '本地服务'),
    ]
    
    service_name = models.CharField(
        max_length=50,
        choices=SERVICE_CHOICES,
        unique=True,
        verbose_name='服务名称'
    )
    api_key = models.CharField(
        max_length=200,
        blank=True,
        verbose_name='API密钥'
    )
    base_url = models.URLField(
        blank=True,
        verbose_name='API基础URL'
    )
    model_name = models.CharField(
        max_length=100,
        blank=True,
        verbose_name='模型名称'
    )
    max_tokens = models.IntegerField(
        default=2000,
        verbose_name='最大Token数'
    )
    temperature = models.FloatField(
        default=0.7,
        verbose_name='温度参数'
    )
    is_active = models.BooleanField(
        default=True,
        verbose_name='是否启用'
    )
    priority = models.IntegerField(
        default=0,
        verbose_name='优先级',
        help_text='数值越大优先级越高'
    )
    created_at = models.DateTimeField(
        default=timezone.now,
        verbose_name='创建时间'
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name='更新时间'
    )
    
    class Meta:
        verbose_name = 'AI服务配置'
        verbose_name_plural = 'AI服务配置'
        ordering = ['-priority', 'service_name']
    
    def __str__(self):
        return f"{self.get_service_name_display()} - {'启用' if self.is_active else '禁用'}"


class AIConversation(models.Model):
    """AI对话记录"""
    
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        verbose_name='用户',
        related_name='ai_conversations'
    )
    title = models.CharField(
        max_length=200,
        blank=True,
        verbose_name='对话标题'
    )
    conversation_id = models.CharField(
        max_length=100,
        unique=True,
        verbose_name='对话ID'
    )
    messages = models.JSONField(
        default=list,
        verbose_name='对话消息',
        help_text='存储完整的对话历史'
    )
    total_tokens = models.IntegerField(
        default=0,
        verbose_name='总Token数'
    )
    total_computing_power = models.IntegerField(
        default=0,
        verbose_name='总消耗算力'
    )
    is_active = models.BooleanField(
        default=True,
        verbose_name='是否活跃'
    )
    created_at = models.DateTimeField(
        default=timezone.now,
        verbose_name='创建时间'
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name='更新时间'
    )
    
    class Meta:
        verbose_name = 'AI对话记录'
        verbose_name_plural = 'AI对话记录'
        ordering = ['-updated_at']
        indexes = [
            models.Index(fields=['user', '-updated_at']),
            models.Index(fields=['conversation_id']),
        ]
    
    def __str__(self):
        return f"{self.user.username} - {self.title or self.conversation_id}"


class AIImageGeneration(models.Model):
    """AI图像生成记录"""
    
    SIZE_CHOICES = [
        ('256x256', '256x256'),
        ('512x512', '512x512'),
        ('1024x1024', '1024x1024'),
        ('1792x1024', '1792x1024'),
        ('1024x1792', '1024x1792'),
    ]
    
    STYLE_CHOICES = [
        ('natural', '自然'),
        ('vivid', '生动'),
        ('realistic', '写实'),
        ('cartoon', '卡通'),
        ('anime', '动漫'),
    ]
    
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        verbose_name='用户',
        related_name='ai_images'
    )
    prompt = models.TextField(
        verbose_name='提示词'
    )
    revised_prompt = models.TextField(
        blank=True,
        verbose_name='修订后的提示词'
    )
    style = models.CharField(
        max_length=20,
        choices=STYLE_CHOICES,
        default='natural',
        verbose_name='风格'
    )
    size = models.CharField(
        max_length=20,
        choices=SIZE_CHOICES,
        default='512x512',
        verbose_name='尺寸'
    )
    image_url = models.URLField(
        blank=True,
        verbose_name='图片URL'
    )
    local_path = models.CharField(
        max_length=500,
        blank=True,
        verbose_name='本地路径'
    )
    computing_power_consumed = models.IntegerField(
        default=0,
        verbose_name='消耗算力'
    )
    generation_time = models.FloatField(
        null=True,
        blank=True,
        verbose_name='生成时间(秒)'
    )
    is_fallback = models.BooleanField(
        default=False,
        verbose_name='是否降级处理'
    )
    created_at = models.DateTimeField(
        default=timezone.now,
        verbose_name='创建时间'
    )
    
    class Meta:
        verbose_name = 'AI图像生成记录'
        verbose_name_plural = 'AI图像生成记录'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', '-created_at']),
            models.Index(fields=['-created_at']),
        ]
    
    def __str__(self):
        return f"{self.user.username} - {self.prompt[:50]}..."
