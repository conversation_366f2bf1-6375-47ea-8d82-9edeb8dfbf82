# Generated by Django 5.2.4 on 2025-07-19 09:39

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="AIServiceConfig",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "service_name",
                    models.CharField(
                        choices=[
                            ("openai", "OpenAI"),
                            ("azure", "Azure"),
                            ("siliconflow", "SiliconFlow"),
                            ("local", "本地服务"),
                        ],
                        max_length=50,
                        unique=True,
                        verbose_name="服务名称",
                    ),
                ),
                (
                    "api_key",
                    models.CharField(
                        blank=True, max_length=200, verbose_name="API密钥"
                    ),
                ),
                ("base_url", models.URLField(blank=True, verbose_name="API基础URL")),
                (
                    "model_name",
                    models.CharField(
                        blank=True, max_length=100, verbose_name="模型名称"
                    ),
                ),
                (
                    "max_tokens",
                    models.IntegerField(default=2000, verbose_name="最大Token数"),
                ),
                (
                    "temperature",
                    models.FloatField(default=0.7, verbose_name="温度参数"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="是否启用"),
                ),
                (
                    "priority",
                    models.IntegerField(
                        default=0, help_text="数值越大优先级越高", verbose_name="优先级"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, verbose_name="创建时间"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
            ],
            options={
                "verbose_name": "AI服务配置",
                "verbose_name_plural": "AI服务配置",
                "ordering": ["-priority", "service_name"],
            },
        ),
        migrations.CreateModel(
            name="AIConversation",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "title",
                    models.CharField(
                        blank=True, max_length=200, verbose_name="对话标题"
                    ),
                ),
                (
                    "conversation_id",
                    models.CharField(
                        max_length=100, unique=True, verbose_name="对话ID"
                    ),
                ),
                (
                    "messages",
                    models.JSONField(
                        default=list,
                        help_text="存储完整的对话历史",
                        verbose_name="对话消息",
                    ),
                ),
                (
                    "total_tokens",
                    models.IntegerField(default=0, verbose_name="总Token数"),
                ),
                (
                    "total_computing_power",
                    models.IntegerField(default=0, verbose_name="总消耗算力"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="是否活跃"),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, verbose_name="创建时间"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="ai_conversations",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="用户",
                    ),
                ),
            ],
            options={
                "verbose_name": "AI对话记录",
                "verbose_name_plural": "AI对话记录",
                "ordering": ["-updated_at"],
                "indexes": [
                    models.Index(
                        fields=["user", "-updated_at"],
                        name="ai_tools_ai_user_id_e6616b_idx",
                    ),
                    models.Index(
                        fields=["conversation_id"],
                        name="ai_tools_ai_convers_10f034_idx",
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="AIImageGeneration",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("prompt", models.TextField(verbose_name="提示词")),
                (
                    "revised_prompt",
                    models.TextField(blank=True, verbose_name="修订后的提示词"),
                ),
                (
                    "style",
                    models.CharField(
                        choices=[
                            ("natural", "自然"),
                            ("vivid", "生动"),
                            ("realistic", "写实"),
                            ("cartoon", "卡通"),
                            ("anime", "动漫"),
                        ],
                        default="natural",
                        max_length=20,
                        verbose_name="风格",
                    ),
                ),
                (
                    "size",
                    models.CharField(
                        choices=[
                            ("256x256", "256x256"),
                            ("512x512", "512x512"),
                            ("1024x1024", "1024x1024"),
                            ("1792x1024", "1792x1024"),
                            ("1024x1792", "1024x1792"),
                        ],
                        default="512x512",
                        max_length=20,
                        verbose_name="尺寸",
                    ),
                ),
                ("image_url", models.URLField(blank=True, verbose_name="图片URL")),
                (
                    "local_path",
                    models.CharField(
                        blank=True, max_length=500, verbose_name="本地路径"
                    ),
                ),
                (
                    "computing_power_consumed",
                    models.IntegerField(default=0, verbose_name="消耗算力"),
                ),
                (
                    "generation_time",
                    models.FloatField(
                        blank=True, null=True, verbose_name="生成时间(秒)"
                    ),
                ),
                (
                    "is_fallback",
                    models.BooleanField(default=False, verbose_name="是否降级处理"),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, verbose_name="创建时间"
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="ai_images",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="用户",
                    ),
                ),
            ],
            options={
                "verbose_name": "AI图像生成记录",
                "verbose_name_plural": "AI图像生成记录",
                "ordering": ["-created_at"],
                "indexes": [
                    models.Index(
                        fields=["user", "-created_at"],
                        name="ai_tools_ai_user_id_1421f1_idx",
                    ),
                    models.Index(
                        fields=["-created_at"], name="ai_tools_ai_created_114e63_idx"
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="AIToolUsageLog",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "tool_name",
                    models.CharField(
                        choices=[
                            ("ai_chat", "AI对话"),
                            ("ai_painting", "AI绘画"),
                            ("voice_synthesis", "语音合成"),
                            ("text_generation", "文本生成"),
                        ],
                        max_length=50,
                        verbose_name="工具名称",
                    ),
                ),
                (
                    "input_data",
                    models.JSONField(
                        help_text="用户输入的数据", verbose_name="输入数据"
                    ),
                ),
                (
                    "output_data",
                    models.JSONField(
                        help_text="AI生成的结果数据", verbose_name="输出数据"
                    ),
                ),
                (
                    "computing_power_consumed",
                    models.IntegerField(default=0, verbose_name="消耗算力"),
                ),
                (
                    "is_fallback",
                    models.BooleanField(
                        default=False,
                        help_text="当第三方服务不可用时的降级处理",
                        verbose_name="是否降级处理",
                    ),
                ),
                (
                    "response_time",
                    models.FloatField(
                        blank=True, null=True, verbose_name="响应时间(秒)"
                    ),
                ),
                (
                    "error_message",
                    models.TextField(blank=True, verbose_name="错误信息"),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, verbose_name="创建时间"
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="ai_tool_usage_logs",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="用户",
                    ),
                ),
            ],
            options={
                "verbose_name": "AI工具使用日志",
                "verbose_name_plural": "AI工具使用日志",
                "ordering": ["-created_at"],
                "indexes": [
                    models.Index(
                        fields=["user", "-created_at"],
                        name="ai_tools_ai_user_id_e6b811_idx",
                    ),
                    models.Index(
                        fields=["tool_name", "-created_at"],
                        name="ai_tools_ai_tool_na_712a62_idx",
                    ),
                ],
            },
        ),
    ]
