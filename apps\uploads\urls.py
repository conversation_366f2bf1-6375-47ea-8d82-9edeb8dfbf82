"""
文件上传API路由
"""
from django.urls import path
from . import views

urlpatterns = [
    # 上传API根目录
    path('', views.UploadListView.as_view(), name='upload-root'),

    # 简单上传
    path('upload/', views.SimpleUploadView.as_view(), name='simple-upload'),

    # 分块上传
    path('chunked-upload/init/', views.ChunkedUploadInitView.as_view(), name='chunked-upload-init'),
    path('chunked-upload/<uuid:upload_id>/', views.ChunkedUploadView.as_view(), name='chunked-upload'),
    path('chunked-upload/<uuid:upload_id>/progress/', views.UploadProgressView.as_view(), name='upload-progress'),

    # 文件列表
    path('files/', views.UploadListView.as_view(), name='upload-list'),
]
