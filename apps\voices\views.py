"""
语音系统相关视图
"""
from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import SearchFilter, OrderingFilter
from apps.common.cache import cache_response, cache_result, invalidate_cache
from django.db import models

from .models import VoiceModel, VoiceCloneRequest, VoiceSynthesisTask
from .serializers import (
    VoiceModelSerializer, VoiceCloneRequestSerializer,
    VoiceSynthesisTaskSerializer
)
from .services.voice_synthesis_service import synthesize_voice


class VoiceModelViewSet(viewsets.ModelViewSet):
    """语音模型视图集"""
    queryset = VoiceModel.objects.all()
    serializer_class = VoiceModelSerializer
    permission_classes = [permissions.IsAuthenticatedOrReadOnly]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['language', 'gender', 'voice_type', 'is_public', 'is_active']
    search_fields = ['name', 'description']
    ordering_fields = ['sort_order', 'usage_count', 'created_at']
    ordering = ['sort_order', '-created_at']
    
    def get_queryset(self):
        """根据用户权限过滤语音模型"""
        queryset = self.queryset.filter(is_active=True)

        # 如果用户未认证，只返回公共语音
        if not self.request.user.is_authenticated:
            queryset = queryset.filter(is_public=True)
        elif not self.request.user.is_staff:
            # 普通用户只能看到公共语音和自己的私有语音
            queryset = queryset.filter(
                models.Q(is_public=True) |
                models.Q(owner=self.request.user)
            )

        return queryset
    
    @action(detail=True, methods=['post'])
    def synthesize(self, request, pk=None):
        """语音合成"""
        voice_model = self.get_object()
        user = request.user

        # 检查用户是否认证
        if not user.is_authenticated:
            return Response({'error': '用户未认证'}, status=status.HTTP_401_UNAUTHORIZED)

        text_content = request.data.get('text_content', '')

        if not text_content:
            return Response(
                {'error': '请提供要合成的文本内容'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # 估算算力消耗（按分钟计算）
        estimated_duration = len(text_content) / 180  # 假设每分钟180字
        computing_power_needed = max(int(estimated_duration * voice_model.computing_power_cost), 1)

        # 检查算力是否足够
        if not hasattr(user, 'computing_power') or user.computing_power < computing_power_needed:
            return Response(
                {'error': f'算力不足，需要 {computing_power_needed} 算力'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # 创建合成任务
        task = VoiceSynthesisTask.objects.create(
            user=user,
            voice_model=voice_model,
            text_content=text_content,
            volume=request.data.get('volume', voice_model.default_volume),
            pitch=request.data.get('pitch', voice_model.default_pitch),
            speed=request.data.get('speed', voice_model.default_speed),
            computing_power_consumed=computing_power_needed
        )

        # 消耗算力
        try:
            user.consume_computing_power(
                computing_power_needed,
                f'语音合成: {voice_model.name}'
            )

            # 增加使用次数
            voice_model.increment_usage()

            # 实际调用第三方API进行语音合成
            try:
                synthesis_result = synthesize_voice(
                    text=text_content,
                    voice_model=voice_model,
                    volume=task.volume,
                    pitch=task.pitch,
                    speed=task.speed
                )
            except Exception as synthesis_error:
                # 如果语音合成服务失败，使用降级处理
                synthesis_result = {
                    'success': True,
                    'audio_url': '/static/audio/sample_voice.mp3',
                    'provider': 'local_fallback',
                    'is_fallback': True,
                    'message': f'第三方服务不可用，使用本地模拟: {str(synthesis_error)}'
                }

            # 更新任务状态
            if synthesis_result['success']:
                task.status = 'completed'
                task.result_audio_url = synthesis_result['audio_url']
                task.provider = synthesis_result.get('provider', 'unknown')
                task.save()

                return Response({
                    'message': '语音合成成功',
                    'task_id': task.id,
                    'audio_url': synthesis_result['audio_url'],
                    'provider': synthesis_result.get('provider'),
                    'is_fallback': synthesis_result.get('is_fallback', False),
                    'estimated_duration': estimated_duration,
                    'computing_power_consumed': computing_power_needed,
                    'remaining_power': user.computing_power
                })
            else:
                task.status = 'failed'
                task.error_message = synthesis_result.get('error', '未知错误')
                task.save()

                return Response({
                    'error': f'语音合成失败: {synthesis_result.get("error")}',
                    'task_id': task.id,
                    'computing_power_consumed': computing_power_needed,
                    'remaining_power': user.computing_power
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            
        except ValueError as e:
            # 删除任务
            task.delete()
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
    
    @action(detail=False, methods=['get'], permission_classes=[permissions.AllowAny])
    @cache_response('voice', timeout=1800)  # 缓存30分钟
    def public_voices(self, request):
        """获取公共语音模型"""
        voices = self.get_queryset().filter(is_public=True)
        page = self.paginate_queryset(voices)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(voices, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def my_voices(self, request):
        """获取我的语音模型"""
        voices = self.get_queryset().filter(owner=request.user)
        page = self.paginate_queryset(voices)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(voices, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def my_models(self, request):
        """获取我的语音模型"""
        # 如果用户未认证，返回空结果
        if not request.user.is_authenticated:
            return Response({
                'count': 0,
                'next': None,
                'previous': None,
                'results': []
            })

        voices = self.get_queryset().filter(owner=request.user)
        page = self.paginate_queryset(voices)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(voices, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['post'])
    def upload_sample(self, request):
        """上传语音样本"""
        audio_file = request.FILES.get('audio')
        if not audio_file:
            return Response(
                {'error': '请选择音频文件'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # 验证文件类型
        allowed_types = ['audio/wav', 'audio/mp3', 'audio/mpeg']
        if audio_file.content_type not in allowed_types:
            return Response(
                {'error': '只支持WAV、MP3格式的音频文件'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # 验证文件大小（50MB）
        if audio_file.size > 50 * 1024 * 1024:
            return Response(
                {'error': '音频文件大小不能超过50MB'},
                status=status.HTTP_400_BAD_REQUEST
            )

        return Response({
            'message': '语音样本上传成功',
            'file_size': audio_file.size,
            'file_name': audio_file.name
        })

    @action(detail=True, methods=['post'], permission_classes=[permissions.AllowAny])
    def preview(self, request, pk=None):
        """语音试听"""
        try:
            voice_model = self.get_object()
            text = request.data.get('text', '你好，这是语音试听功能。')

            # 限制试听文本长度
            if len(text) > 100:
                return Response(
                    {'error': '试听文本不能超过100个字符'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # 如果语音模型有样本音频，直接返回样本
            if voice_model.audio_sample_url:
                return Response({
                    'message': '语音试听',
                    'audio_url': voice_model.audio_sample_url,
                    'text': text,
                    'voice_name': voice_model.name,
                    'language': voice_model.language,
                    'gender': voice_model.gender,
                    'is_sample': True
                })

            # 这里应该调用实际的语音合成服务
            # 暂时返回模拟结果
            return Response({
                'message': '语音试听生成成功',
                'audio_url': '/static/audio/sample_voice.mp3',  # 默认样本音频
                'text': text,
                'voice_name': voice_model.name,
                'language': voice_model.language,
                'gender': voice_model.gender,
                'is_sample': False
            })
        except Exception as e:
            return Response(
                {'error': f'语音试听失败: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class VoiceCloneRequestViewSet(viewsets.ModelViewSet):
    """语音克隆请求视图集"""
    queryset = VoiceCloneRequest.objects.all()
    serializer_class = VoiceCloneRequestSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['status']
    search_fields = ['name', 'description']
    ordering_fields = ['created_at', 'updated_at']
    ordering = ['-created_at']
    
    def get_queryset(self):
        """普通用户只能查看自己的克隆请求"""
        if self.request.user.is_staff:
            return self.queryset
        return self.queryset.filter(user=self.request.user)
    
    def perform_create(self, serializer):
        """创建语音克隆请求时设置用户"""
        serializer.save(user=self.request.user)


class VoiceSynthesisTaskViewSet(viewsets.ModelViewSet):
    """语音合成任务视图集"""
    queryset = VoiceSynthesisTask.objects.all()
    serializer_class = VoiceSynthesisTaskSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['status', 'voice_model']
    search_fields = ['text_content']
    ordering_fields = ['created_at', 'updated_at']
    ordering = ['-created_at']
    
    def get_queryset(self):
        """普通用户只能查看自己的合成任务"""
        if self.request.user.is_staff:
            return self.queryset
        return self.queryset.filter(user=self.request.user)
    
    def get_permissions(self):
        """根据操作类型设置权限"""
        if self.action in ['create', 'update', 'partial_update', 'destroy']:
            permission_classes = [permissions.IsAdminUser]
        else:
            permission_classes = [permissions.IsAuthenticated]
        return [permission() for permission in permission_classes]
