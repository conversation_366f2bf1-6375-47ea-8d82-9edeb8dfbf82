"""
系统配置数据模型
"""
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.cache import cache
from django.contrib.auth import get_user_model
import json

User = get_user_model()


class SystemConfig(models.Model):
    """系统配置"""
    
    CONFIG_TYPE_CHOICES = [
        ('general', _('通用配置')),
        ('computing', _('算力配置')),
        ('payment', _('支付配置')),
        ('ai_service', _('AI服务配置')),
        ('storage', _('存储配置')),
        ('notification', _('通知配置')),
        ('security', _('安全配置')),
        ('feature', _('功能开关')),
    ]
    
    VALUE_TYPE_CHOICES = [
        ('string', _('字符串')),
        ('integer', _('整数')),
        ('float', _('浮点数')),
        ('boolean', _('布尔值')),
        ('json', _('JSON对象')),
        ('text', _('长文本')),
    ]
    
    # 配置标识
    key = models.CharField(_('配置键'), max_length=100, unique=True)
    name = models.CharField(_('配置名称'), max_length=200)
    description = models.TextField(_('配置描述'), blank=True)
    
    # 配置分类
    config_type = models.CharField(_('配置类型'), max_length=20, choices=CONFIG_TYPE_CHOICES)
    
    # 配置值
    value = models.TextField(_('配置值'))
    value_type = models.CharField(_('值类型'), max_length=10, choices=VALUE_TYPE_CHOICES, default='string')
    default_value = models.TextField(_('默认值'), blank=True)
    
    # 验证规则
    validation_rule = models.TextField(_('验证规则'), blank=True, help_text='JSON格式的验证规则')
    
    # 状态
    is_active = models.BooleanField(_('启用'), default=True)
    is_sensitive = models.BooleanField(_('敏感信息'), default=False, help_text='敏感信息在界面上会被隐藏')
    is_readonly = models.BooleanField(_('只读'), default=False)
    
    # 排序
    sort_order = models.IntegerField(_('排序'), default=0)
    
    # 时间
    created_at = models.DateTimeField(_('创建时间'), auto_now_add=True)
    updated_at = models.DateTimeField(_('更新时间'), auto_now=True)
    
    class Meta:
        verbose_name = _('系统配置')
        verbose_name_plural = _('系统配置')
        ordering = ['config_type', 'sort_order', 'key']
    
    def __str__(self):
        return f'{self.name} ({self.key})'
    
    def get_value(self):
        """获取配置值（转换为对应类型）"""
        if not self.value:
            return self.get_default_value()
        
        try:
            if self.value_type == 'integer':
                return int(self.value)
            elif self.value_type == 'float':
                return float(self.value)
            elif self.value_type == 'boolean':
                return self.value.lower() in ['true', '1', 'yes', 'on']
            elif self.value_type == 'json':
                return json.loads(self.value)
            else:
                return self.value
        except (ValueError, json.JSONDecodeError):
            return self.get_default_value()
    
    def get_default_value(self):
        """获取默认值"""
        if not self.default_value:
            return None
        
        try:
            if self.value_type == 'integer':
                return int(self.default_value)
            elif self.value_type == 'float':
                return float(self.default_value)
            elif self.value_type == 'boolean':
                return self.default_value.lower() in ['true', '1', 'yes', 'on']
            elif self.value_type == 'json':
                return json.loads(self.default_value)
            else:
                return self.default_value
        except (ValueError, json.JSONDecodeError):
            return None
    
    def set_value(self, value):
        """设置配置值"""
        if self.value_type == 'json':
            self.value = json.dumps(value, ensure_ascii=False)
        else:
            self.value = str(value)
        
        # 清除缓存
        cache.delete(f'config_{self.key}')
    
    def save(self, *args, **kwargs):
        """保存时清除缓存"""
        super().save(*args, **kwargs)
        cache.delete(f'config_{self.key}')
        cache.delete('all_configs')


class ConfigGroup(models.Model):
    """配置分组"""
    
    name = models.CharField(_('分组名称'), max_length=100)
    description = models.TextField(_('分组描述'), blank=True)
    icon = models.CharField(_('图标'), max_length=50, blank=True)
    sort_order = models.IntegerField(_('排序'), default=0)
    
    # 关联的配置
    configs = models.ManyToManyField(SystemConfig, verbose_name=_('配置项'), blank=True)
    
    # 状态
    is_active = models.BooleanField(_('启用'), default=True)
    
    # 时间
    created_at = models.DateTimeField(_('创建时间'), auto_now_add=True)
    updated_at = models.DateTimeField(_('更新时间'), auto_now=True)
    
    class Meta:
        verbose_name = _('配置分组')
        verbose_name_plural = _('配置分组')
        ordering = ['sort_order', 'name']
    
    def __str__(self):
        return self.name


class ConfigHistory(models.Model):
    """配置变更历史"""
    
    config = models.ForeignKey(
        SystemConfig, 
        on_delete=models.CASCADE,
        related_name='history',
        verbose_name=_('配置项')
    )
    
    # 变更信息
    old_value = models.TextField(_('原值'), blank=True)
    new_value = models.TextField(_('新值'))
    change_reason = models.CharField(_('变更原因'), max_length=200, blank=True)
    
    # 操作者
    changed_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        verbose_name=_('操作者')
    )
    
    # 时间
    created_at = models.DateTimeField(_('变更时间'), auto_now_add=True)
    
    class Meta:
        verbose_name = _('配置变更历史')
        verbose_name_plural = _('配置变更历史')
        ordering = ['-created_at']
    
    def __str__(self):
        return f'{self.config.name} - {self.created_at}'


# 配置管理器
class ConfigManager:
    """配置管理器"""
    
    @staticmethod
    def get(key, default=None):
        """获取配置值"""
        # 先从缓存获取
        cache_key = f'config_{key}'
        value = cache.get(cache_key)
        
        if value is not None:
            return value
        
        # 从数据库获取
        try:
            config = SystemConfig.objects.get(key=key, is_active=True)
            value = config.get_value()
            
            # 缓存配置值（缓存1小时）
            cache.set(cache_key, value, 3600)
            return value
        except SystemConfig.DoesNotExist:
            return default
    
    @staticmethod
    def set(key, value, user=None, reason=''):
        """设置配置值"""
        try:
            config = SystemConfig.objects.get(key=key)
            
            if config.is_readonly:
                raise ValueError(f'配置 {key} 是只读的，无法修改')
            
            # 记录历史
            ConfigHistory.objects.create(
                config=config,
                old_value=config.value,
                new_value=str(value),
                change_reason=reason,
                changed_by=user
            )
            
            # 更新配置
            config.set_value(value)
            config.save()
            
            return True
        except SystemConfig.DoesNotExist:
            return False
    
    @staticmethod
    def get_all(config_type=None):
        """获取所有配置"""
        cache_key = f'all_configs_{config_type}' if config_type else 'all_configs'
        configs = cache.get(cache_key)
        
        if configs is not None:
            return configs
        
        # 从数据库获取
        queryset = SystemConfig.objects.filter(is_active=True)
        if config_type:
            queryset = queryset.filter(config_type=config_type)
        
        configs = {}
        for config in queryset:
            configs[config.key] = config.get_value()
        
        # 缓存配置（缓存30分钟）
        cache.set(cache_key, configs, 1800)
        return configs
    
    @staticmethod
    def refresh_cache():
        """刷新缓存"""
        # 清除所有配置缓存
        cache.delete_many([
            'all_configs',
            'all_configs_general',
            'all_configs_computing',
            'all_configs_payment',
            'all_configs_ai_service',
            'all_configs_storage',
            'all_configs_notification',
            'all_configs_security',
            'all_configs_feature',
        ])
        
        # 清除单个配置缓存
        for config in SystemConfig.objects.all():
            cache.delete(f'config_{config.key}')


# 全局配置管理器实例
config_manager = ConfigManager()
