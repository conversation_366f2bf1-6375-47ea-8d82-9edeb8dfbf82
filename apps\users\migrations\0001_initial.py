# Generated by Django 5.2.4 on 2025-07-15 02:41

import django.contrib.auth.models
import django.contrib.auth.validators
import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("auth", "0012_alter_user_first_name_max_length"),
    ]

    operations = [
        migrations.CreateModel(
            name="User",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("password", models.CharField(max_length=128, verbose_name="password")),
                (
                    "last_login",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="last login"
                    ),
                ),
                (
                    "is_superuser",
                    models.BooleanField(
                        default=False,
                        help_text="Designates that this user has all permissions without explicitly assigning them.",
                        verbose_name="superuser status",
                    ),
                ),
                (
                    "username",
                    models.Char<PERSON>ield(
                        error_messages={
                            "unique": "A user with that username already exists."
                        },
                        help_text="Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.",
                        max_length=150,
                        unique=True,
                        validators=[
                            django.contrib.auth.validators.UnicodeUsernameValidator()
                        ],
                        verbose_name="username",
                    ),
                ),
                (
                    "first_name",
                    models.CharField(
                        blank=True, max_length=150, verbose_name="first name"
                    ),
                ),
                (
                    "last_name",
                    models.CharField(
                        blank=True, max_length=150, verbose_name="last name"
                    ),
                ),
                (
                    "email",
                    models.EmailField(
                        blank=True, max_length=254, verbose_name="email address"
                    ),
                ),
                (
                    "is_staff",
                    models.BooleanField(
                        default=False,
                        help_text="Designates whether the user can log into this admin site.",
                        verbose_name="staff status",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True,
                        help_text="Designates whether this user should be treated as active. Unselect this instead of deleting accounts.",
                        verbose_name="active",
                    ),
                ),
                (
                    "date_joined",
                    models.DateTimeField(
                        default=django.utils.timezone.now, verbose_name="date joined"
                    ),
                ),
                (
                    "phone",
                    models.CharField(
                        blank=True,
                        help_text="用户手机号码",
                        max_length=20,
                        null=True,
                        unique=True,
                        verbose_name="手机号",
                    ),
                ),
                (
                    "avatar",
                    models.ImageField(
                        blank=True,
                        help_text="用户头像图片",
                        null=True,
                        upload_to="avatars/users/",
                        verbose_name="头像",
                    ),
                ),
                (
                    "computing_power",
                    models.PositiveIntegerField(
                        default=0, help_text="用户当前可用算力点数", verbose_name="算力"
                    ),
                ),
                (
                    "total_computing_power",
                    models.PositiveIntegerField(
                        default=0, help_text="用户累计获得的算力点数", verbose_name="总算力"
                    ),
                ),
                (
                    "package_type",
                    models.CharField(
                        choices=[
                            ("free", "免费版"),
                            ("basic", "基础版"),
                            ("pro", "专业版"),
                            ("enterprise", "企业版"),
                        ],
                        default="free",
                        help_text="用户当前套餐类型",
                        max_length=50,
                        verbose_name="套餐类型",
                    ),
                ),
                (
                    "package_expire_date",
                    models.DateTimeField(
                        blank=True,
                        help_text="付费套餐的到期时间",
                        null=True,
                        verbose_name="套餐到期时间",
                    ),
                ),
                (
                    "is_verified",
                    models.BooleanField(
                        default=False, help_text="用户是否已通过手机验证", verbose_name="已验证"
                    ),
                ),
                (
                    "last_login_ip",
                    models.GenericIPAddressField(
                        blank=True,
                        help_text="用户最后一次登录的IP地址",
                        null=True,
                        verbose_name="最后登录IP",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "groups",
                    models.ManyToManyField(
                        blank=True,
                        help_text="The groups this user belongs to. A user will get all permissions granted to each of their groups.",
                        related_name="user_set",
                        related_query_name="user",
                        to="auth.group",
                        verbose_name="groups",
                    ),
                ),
                (
                    "user_permissions",
                    models.ManyToManyField(
                        blank=True,
                        help_text="Specific permissions for this user.",
                        related_name="user_set",
                        related_query_name="user",
                        to="auth.permission",
                        verbose_name="user permissions",
                    ),
                ),
            ],
            options={
                "verbose_name": "用户",
                "verbose_name_plural": "用户",
                "db_table": "users",
                "ordering": ["-created_at"],
            },
            managers=[
                ("objects", django.contrib.auth.models.UserManager()),
            ],
        ),
        migrations.CreateModel(
            name="ComputingPowerLog",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "amount",
                    models.IntegerField(help_text="正数表示增加，负数表示消耗", verbose_name="变动数量"),
                ),
                (
                    "balance_after",
                    models.PositiveIntegerField(
                        help_text="操作后的算力余额", verbose_name="变动后余额"
                    ),
                ),
                (
                    "operation_type",
                    models.CharField(
                        choices=[("add", "增加"), ("consume", "消耗"), ("refund", "退还")],
                        max_length=20,
                        verbose_name="操作类型",
                    ),
                ),
                (
                    "reason",
                    models.CharField(
                        blank=True,
                        help_text="算力变动的具体原因",
                        max_length=200,
                        verbose_name="变动原因",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="computing_power_logs",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="用户",
                    ),
                ),
            ],
            options={
                "verbose_name": "算力日志",
                "verbose_name_plural": "算力日志",
                "db_table": "computing_power_logs",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="UserPackage",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "package_type",
                    models.CharField(
                        choices=[
                            ("basic", "基础版"),
                            ("pro", "专业版"),
                            ("enterprise", "企业版"),
                        ],
                        max_length=50,
                        verbose_name="套餐类型",
                    ),
                ),
                (
                    "duration_days",
                    models.PositiveIntegerField(
                        help_text="套餐有效期天数", verbose_name="套餐天数"
                    ),
                ),
                (
                    "computing_power_included",
                    models.PositiveIntegerField(
                        help_text="套餐包含的算力点数", verbose_name="包含算力"
                    ),
                ),
                (
                    "price",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="套餐价格（元）",
                        max_digits=10,
                        verbose_name="价格",
                    ),
                ),
                (
                    "start_date",
                    models.DateTimeField(help_text="套餐生效时间", verbose_name="开始时间"),
                ),
                (
                    "end_date",
                    models.DateTimeField(help_text="套餐到期时间", verbose_name="结束时间"),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True, help_text="套餐是否处于激活状态", verbose_name="是否激活"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="package_records",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="用户",
                    ),
                ),
            ],
            options={
                "verbose_name": "用户套餐",
                "verbose_name_plural": "用户套餐",
                "db_table": "user_packages",
                "ordering": ["-created_at"],
            },
        ),
    ]
