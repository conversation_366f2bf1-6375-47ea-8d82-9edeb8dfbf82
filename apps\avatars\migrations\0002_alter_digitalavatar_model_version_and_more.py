# Generated by Django 5.2.4 on 2025-07-16 02:02

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("avatars", "0001_initial"),
    ]

    operations = [
        migrations.AlterField(
            model_name="digitalavatar",
            name="model_version",
            field=models.CharField(
                choices=[
                    ("V8模型", "V8模型"),
                    ("V6模型", "V6模型"),
                    ("V5模型", "V5模型"),
                    ("V8模型/V6模型/V5模型", "V8模型/V6模型/V5模型"),
                    ("V6模型/V5模型", "V6模型/V5模型"),
                ],
                default="V8模型",
                help_text="支持的数字人模型版本",
                max_length=50,
                verbose_name="模型版本",
            ),
        ),
        migrations.AlterField(
            model_name="digitalavatar",
            name="scene_type",
            field=models.CharField(
                choices=[
                    ("outdoor", "户外"),
                    ("indoor", "室内"),
                    ("general", "通用"),
                    ("outdoor_sitting", "户外坐姿"),
                    ("sitting_half", "坐姿半身"),
                    ("outdoor_full", "户外全身"),
                    ("outdoor_standing", "户外站姿"),
                    ("standing_material", "站姿素材"),
                    ("indoor_oral", "室内口播"),
                    ("indoor_half", "室内半身"),
                    ("indoor_sitting", "室内坐姿"),
                    ("indoor_full", "室内全身"),
                    ("indoor_drama", "室内戏剧"),
                    ("green_screen_sitting", "绿幕斜坐"),
                    ("green_screen_half_standing", "绿幕半身站"),
                    ("green_screen_half_sitting", "绿幕半身坐"),
                    ("walking_talking", "边说边走"),
                    ("garden_scene", "花园场景"),
                    ("ancient_building", "古建筑"),
                    ("library_scene", "图书馆"),
                    ("restaurant_scene", "餐厅场景"),
                    ("travel_scene", "旅游景区"),
                    ("office_scene", "写字楼"),
                    ("other", "其他场景"),
                ],
                help_text="数字人形象的场景类型",
                max_length=50,
                verbose_name="场景类型",
            ),
        ),
    ]
