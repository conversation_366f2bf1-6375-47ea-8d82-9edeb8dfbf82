"""
Celery配置文件
用于处理AI数字人SaaS平台的异步任务
"""
import os
from celery import Celery
from django.conf import settings

# 设置Django设置模块
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ai_digital_human.settings.development')

# 创建Celery应用
app = Celery('ai_digital_human')

# 使用Django设置配置Celery
app.config_from_object('django.conf:settings', namespace='CELERY')

# 自动发现任务
app.autodiscover_tasks()

# Celery配置
app.conf.update(
    # 任务序列化
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='Asia/Shanghai',
    enable_utc=True,
    
    # 任务路由
    task_routes={
        'apps.voices.tasks.process_voice_clone': {'queue': 'voice_processing'},
        'apps.voices.tasks.synthesize_voice': {'queue': 'voice_processing'},
        'apps.videos.tasks.generate_video': {'queue': 'video_processing'},
        'apps.copywriting.tasks.generate_copywriting': {'queue': 'ai_processing'},
        'apps.ai_tools.tasks.*': {'queue': 'ai_processing'},
    },
    
    # 任务优先级
    task_default_priority=5,
    worker_prefetch_multiplier=1,
    
    # 结果后端
    result_expires=3600,
    
    # 任务重试
    task_acks_late=True,
    task_reject_on_worker_lost=True,
)

@app.task(bind=True)
def debug_task(self):
    """调试任务"""
    print(f'Request: {self.request!r}')
