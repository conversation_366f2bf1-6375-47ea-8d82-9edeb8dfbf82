from django.db import models
from django.conf import settings


class ToolCategory(models.Model):
    """AI工具分类"""
    name = models.CharField(max_length=100, verbose_name='分类名称')
    key = models.CharField(max_length=50, unique=True, verbose_name='分类标识')
    icon = models.CharField(max_length=100, verbose_name='图标名称')
    color = models.CharField(max_length=20, verbose_name='颜色')
    sort_order = models.IntegerField(default=0, verbose_name='排序')
    is_active = models.BooleanField(default=True, verbose_name='是否启用')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        db_table = 'ai_tool_categories'
        verbose_name = 'AI工具分类'
        verbose_name_plural = 'AI工具分类'
        ordering = ['sort_order', 'id']

    def __str__(self):
        return self.name


class AITool(models.Model):
    """AI工具"""
    TOOL_STATUS_CHOICES = [
        ('available', '可用'),
        ('coming_soon', '即将推出'),
        ('maintenance', '维护中'),
        ('disabled', '已禁用'),
    ]

    name = models.CharField(max_length=200, verbose_name='工具名称')
    description = models.TextField(verbose_name='工具描述')
    icon = models.CharField(max_length=100, verbose_name='图标名称')
    gradient = models.CharField(max_length=200, verbose_name='渐变色')
    category = models.ForeignKey(ToolCategory, on_delete=models.CASCADE, verbose_name='分类')
    tags = models.JSONField(default=list, verbose_name='标签')
    usage_count = models.IntegerField(default=0, verbose_name='使用次数')
    rating = models.DecimalField(max_digits=3, decimal_places=1, default=0.0, verbose_name='评分')
    status = models.CharField(max_length=20, choices=TOOL_STATUS_CHOICES, default='available', verbose_name='状态')
    is_new = models.BooleanField(default=False, verbose_name='是否新工具')
    is_pro = models.BooleanField(default=False, verbose_name='是否专业版')
    sort_order = models.IntegerField(default=0, verbose_name='排序')
    route_path = models.CharField(max_length=200, blank=True, verbose_name='路由路径')
    api_endpoint = models.CharField(max_length=200, blank=True, verbose_name='API端点')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        db_table = 'ai_tools'
        verbose_name = 'AI工具'
        verbose_name_plural = 'AI工具'
        ordering = ['sort_order', 'id']

    def __str__(self):
        return self.name

    @property
    def available(self):
        return self.status == 'available'


class ToolUsageLog(models.Model):
    """工具使用记录"""
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, verbose_name='用户')
    tool = models.ForeignKey(AITool, on_delete=models.CASCADE, verbose_name='工具')
    session_id = models.CharField(max_length=100, verbose_name='会话ID')
    input_data = models.JSONField(default=dict, verbose_name='输入数据')
    output_data = models.JSONField(default=dict, verbose_name='输出数据')
    processing_time = models.FloatField(default=0.0, verbose_name='处理时间(秒)')
    cost_points = models.IntegerField(default=0, verbose_name='消耗算力')
    status = models.CharField(max_length=20, default='success', verbose_name='状态')
    error_message = models.TextField(blank=True, verbose_name='错误信息')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')

    class Meta:
        db_table = 'ai_tool_usage_logs'
        verbose_name = '工具使用记录'
        verbose_name_plural = '工具使用记录'
        ordering = ['-created_at']

    def __str__(self):
        return f'{self.user.username} - {self.tool.name} - {self.created_at}'
