# Generated by Django 5.2.1 on 2025-07-16 13:46

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("contenttypes", "0002_remove_content_type_name"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="TaskSchedule",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=200, verbose_name="任务名称")),
                ("description", models.TextField(blank=True, verbose_name="任务描述")),
                (
                    "task_function",
                    models.CharField(max_length=200, verbose_name="任务函数"),
                ),
                (
                    "schedule_type",
                    models.CharField(
                        choices=[
                            ("once", "一次性"),
                            ("interval", "间隔执行"),
                            ("cron", "Cron表达式"),
                            ("daily", "每日"),
                            ("weekly", "每周"),
                            ("monthly", "每月"),
                        ],
                        max_length=20,
                        verbose_name="调度类型",
                    ),
                ),
                (
                    "schedule_config",
                    models.JSONField(default=dict, verbose_name="调度配置"),
                ),
                (
                    "args",
                    models.JSONField(blank=True, default=list, verbose_name="位置参数"),
                ),
                (
                    "kwargs",
                    models.JSONField(blank=True, default=dict, verbose_name="关键字参数"),
                ),
                ("is_active", models.BooleanField(default=True, verbose_name="启用")),
                ("total_runs", models.IntegerField(default=0, verbose_name="总执行次数")),
                ("success_runs", models.IntegerField(default=0, verbose_name="成功次数")),
                ("failed_runs", models.IntegerField(default=0, verbose_name="失败次数")),
                (
                    "last_run_at",
                    models.DateTimeField(blank=True, null=True, verbose_name="上次执行时间"),
                ),
                (
                    "next_run_at",
                    models.DateTimeField(blank=True, null=True, verbose_name="下次执行时间"),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
            ],
            options={
                "verbose_name": "定时任务",
                "verbose_name_plural": "定时任务",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="TaskQueue",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "task_id",
                    models.UUIDField(
                        default=uuid.uuid4, unique=True, verbose_name="任务ID"
                    ),
                ),
                ("name", models.CharField(max_length=200, verbose_name="任务名称")),
                ("description", models.TextField(blank=True, verbose_name="任务描述")),
                ("task_type", models.CharField(max_length=100, verbose_name="任务类型")),
                (
                    "task_function",
                    models.CharField(max_length=200, verbose_name="任务函数"),
                ),
                (
                    "args",
                    models.JSONField(blank=True, default=list, verbose_name="位置参数"),
                ),
                (
                    "kwargs",
                    models.JSONField(blank=True, default=dict, verbose_name="关键字参数"),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "等待中"),
                            ("running", "执行中"),
                            ("completed", "已完成"),
                            ("failed", "失败"),
                            ("cancelled", "已取消"),
                            ("retry", "重试中"),
                        ],
                        default="pending",
                        max_length=20,
                        verbose_name="状态",
                    ),
                ),
                (
                    "priority",
                    models.CharField(
                        choices=[
                            ("low", "低"),
                            ("normal", "普通"),
                            ("high", "高"),
                            ("urgent", "紧急"),
                        ],
                        default="normal",
                        max_length=10,
                        verbose_name="优先级",
                    ),
                ),
                (
                    "worker_id",
                    models.CharField(blank=True, max_length=100, verbose_name="工作进程ID"),
                ),
                (
                    "started_at",
                    models.DateTimeField(blank=True, null=True, verbose_name="开始时间"),
                ),
                (
                    "completed_at",
                    models.DateTimeField(blank=True, null=True, verbose_name="完成时间"),
                ),
                (
                    "result",
                    models.JSONField(blank=True, null=True, verbose_name="执行结果"),
                ),
                ("error_message", models.TextField(blank=True, verbose_name="错误信息")),
                ("traceback", models.TextField(blank=True, verbose_name="错误堆栈")),
                ("max_retries", models.IntegerField(default=3, verbose_name="最大重试次数")),
                ("retry_count", models.IntegerField(default=0, verbose_name="已重试次数")),
                (
                    "retry_delay",
                    models.IntegerField(default=60, verbose_name="重试延迟(秒)"),
                ),
                ("progress", models.IntegerField(default=0, verbose_name="进度百分比")),
                (
                    "progress_message",
                    models.CharField(blank=True, max_length=500, verbose_name="进度消息"),
                ),
                ("object_id", models.PositiveIntegerField(blank=True, null=True)),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "expires_at",
                    models.DateTimeField(blank=True, null=True, verbose_name="过期时间"),
                ),
                (
                    "content_type",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="contenttypes.contenttype",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="创建者",
                    ),
                ),
            ],
            options={
                "verbose_name": "任务队列",
                "verbose_name_plural": "任务队列",
                "ordering": ["-priority", "-created_at"],
            },
        ),
        migrations.CreateModel(
            name="TaskResult",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("worker_id", models.CharField(max_length=100, verbose_name="工作进程ID")),
                ("started_at", models.DateTimeField(verbose_name="开始时间")),
                ("completed_at", models.DateTimeField(verbose_name="完成时间")),
                ("duration", models.FloatField(verbose_name="执行时长(秒)")),
                ("success", models.BooleanField(verbose_name="是否成功")),
                (
                    "result",
                    models.JSONField(blank=True, null=True, verbose_name="执行结果"),
                ),
                ("error_message", models.TextField(blank=True, verbose_name="错误信息")),
                ("traceback", models.TextField(blank=True, verbose_name="错误堆栈")),
                (
                    "memory_usage",
                    models.FloatField(blank=True, null=True, verbose_name="内存使用(MB)"),
                ),
                (
                    "cpu_usage",
                    models.FloatField(blank=True, null=True, verbose_name="CPU使用率"),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "task",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="execution_results",
                        to="tasks.taskqueue",
                        verbose_name="任务",
                    ),
                ),
            ],
            options={
                "verbose_name": "任务执行结果",
                "verbose_name_plural": "任务执行结果",
                "ordering": ["-created_at"],
            },
        ),
        migrations.AddIndex(
            model_name="taskqueue",
            index=models.Index(
                fields=["status", "priority"], name="tasks_taskq_status_9ba6f6_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="taskqueue",
            index=models.Index(
                fields=["task_type", "status"], name="tasks_taskq_task_ty_0c4c9a_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="taskqueue",
            index=models.Index(
                fields=["created_by", "status"], name="tasks_taskq_created_de9d16_idx"
            ),
        ),
    ]
