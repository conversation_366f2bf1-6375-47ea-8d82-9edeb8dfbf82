# Generated by Django 5.2.1 on 2025-07-16 11:21

from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="DailyStatistics",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("date", models.DateField(unique=True, verbose_name="日期")),
                ("new_users", models.IntegerField(default=0, verbose_name="新增用户")),
                ("active_users", models.IntegerField(default=0, verbose_name="活跃用户")),
                (
                    "videos_created",
                    models.IntegerField(default=0, verbose_name="生成视频数"),
                ),
                (
                    "avatars_created",
                    models.IntegerField(default=0, verbose_name="创建数字人数"),
                ),
                (
                    "computing_power_consumed",
                    models.IntegerField(default=0, verbose_name="算力消耗"),
                ),
                (
                    "revenue",
                    models.DecimalField(
                        decimal_places=2, default=0, max_digits=10, verbose_name="收入"
                    ),
                ),
                ("packages_sold", models.IntegerField(default=0, verbose_name="套餐销售数")),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
            ],
            options={
                "verbose_name": "每日统计",
                "verbose_name_plural": "每日统计",
                "ordering": ["-date"],
            },
        ),
        migrations.CreateModel(
            name="SystemStatistics",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("total_users", models.IntegerField(default=0, verbose_name="总用户数")),
                ("active_users", models.IntegerField(default=0, verbose_name="活跃用户数")),
                (
                    "new_users_today",
                    models.IntegerField(default=0, verbose_name="今日新增用户"),
                ),
                ("total_avatars", models.IntegerField(default=0, verbose_name="总数字人数")),
                (
                    "public_avatars",
                    models.IntegerField(default=0, verbose_name="公共数字人数"),
                ),
                (
                    "private_avatars",
                    models.IntegerField(default=0, verbose_name="私有数字人数"),
                ),
                ("total_videos", models.IntegerField(default=0, verbose_name="总视频数")),
                ("videos_today", models.IntegerField(default=0, verbose_name="今日生成视频")),
                (
                    "total_computing_power",
                    models.BigIntegerField(default=0, verbose_name="总算力消耗"),
                ),
                (
                    "computing_power_today",
                    models.IntegerField(default=0, verbose_name="今日算力消耗"),
                ),
                (
                    "total_revenue",
                    models.DecimalField(
                        decimal_places=2, default=0, max_digits=12, verbose_name="总收入"
                    ),
                ),
                (
                    "revenue_today",
                    models.DecimalField(
                        decimal_places=2, default=0, max_digits=10, verbose_name="今日收入"
                    ),
                ),
                (
                    "active_packages",
                    models.IntegerField(default=0, verbose_name="活跃套餐数"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
            ],
            options={
                "verbose_name": "系统统计",
                "verbose_name_plural": "系统统计",
            },
        ),
    ]
