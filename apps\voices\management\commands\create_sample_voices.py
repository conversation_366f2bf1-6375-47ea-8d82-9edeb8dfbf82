"""
创建示例声音数据
基于前端VoiceClone.vue中的硬编码数据
"""
from django.core.management.base import BaseCommand
from apps.voices.models import VoiceModel


class Command(BaseCommand):
    help = '创建示例声音数据'

    def handle(self, *args, **options):
        self.stdout.write('开始创建示例声音数据...')

        # 基于前端VoiceClone.vue的硬编码数据创建声音
        voices_data = [
            # 英语声音
            {'name': 'Emma', 'language': 'en-us', 'gender': 'female', 'voice_type': 'standard', 'description': '标准美式英语女声'},
            {'name': 'Liam', 'language': 'en-us', 'gender': 'male', 'voice_type': 'standard', 'description': '标准美式英语男声'},
            {'name': 'Olivia', 'language': 'en-us', 'gender': 'female', 'voice_type': 'sweet', 'description': '甜美美式英语女声'},
            {'name': 'Noah', 'language': 'en-us', 'gender': 'male', 'voice_type': 'magnetic', 'description': '磁性美式英语男声'},
            {'name': 'Ava', 'language': 'en-us', 'gender': 'female', 'voice_type': 'gentle', 'description': '温柔美式英语女声'},
            {'name': 'William', 'language': 'en-us', 'gender': 'male', 'voice_type': 'news', 'description': '新闻播报美式英语男声'},
            {'name': 'Sophia', 'language': 'en-us', 'gender': 'female', 'voice_type': 'intellectual', 'description': '知性美式英语女声'},
            {'name': 'James', 'language': 'en-us', 'gender': 'male', 'voice_type': 'dramatic', 'description': '戏剧美式英语男声'},

            # 方言声音
            {'name': '小芳', 'language': 'yue', 'gender': 'female', 'voice_type': 'sweet', 'description': '甜美粤语女声'},
            {'name': '阿强', 'language': 'yue', 'gender': 'male', 'voice_type': 'standard', 'description': '标准粤语男声'},
            {'name': '小梅', 'language': 'wu', 'gender': 'female', 'voice_type': 'gentle', 'description': '温柔上海话女声'},
            {'name': '老王', 'language': 'wu', 'gender': 'male', 'voice_type': 'magnetic', 'description': '磁性上海话男声'},
            {'name': '川妹子', 'language': 'sichuan', 'gender': 'female', 'voice_type': 'lively', 'description': '活泼四川话女声'},
            {'name': '川哥', 'language': 'sichuan', 'gender': 'male', 'voice_type': 'standard', 'description': '标准四川话男声'},
            {'name': '湘妹', 'language': 'hunan', 'gender': 'female', 'voice_type': 'cute', 'description': '可爱湖南话女声'},
            {'name': '豫哥', 'language': 'henan', 'gender': 'male', 'voice_type': 'standard', 'description': '标准河南话男声'},
            {'name': '津妹', 'language': 'tianjin', 'gender': 'female', 'voice_type': 'sweet', 'description': '甜美天津话女声'},
            {'name': '鄂哥', 'language': 'hubei', 'gender': 'male', 'voice_type': 'magnetic', 'description': '磁性湖北话男声'},
            {'name': '东北妹', 'language': 'dongbei', 'gender': 'female', 'voice_type': 'lively', 'description': '活泼东北话女声'},
            {'name': '桂哥', 'language': 'guangxi', 'gender': 'male', 'voice_type': 'standard', 'description': '标准广西话男声'},
            {'name': '渝妹', 'language': 'chongqing', 'gender': 'female', 'voice_type': 'gentle', 'description': '温柔重庆话女声'},

            # 日语声音
            {'name': 'さくら', 'language': 'ja-jp', 'gender': 'female', 'voice_type': 'sweet', 'description': '甜美日语女声'},
            {'name': 'たろう', 'language': 'ja-jp', 'gender': 'male', 'voice_type': 'standard', 'description': '标准日语男声'},
            {'name': 'ゆき', 'language': 'ja-jp', 'gender': 'female', 'voice_type': 'gentle', 'description': '温柔日语女声'},
            {'name': 'ひろし', 'language': 'ja-jp', 'gender': 'male', 'voice_type': 'magnetic', 'description': '磁性日语男声'},
            {'name': 'あい', 'language': 'ja-jp', 'gender': 'female', 'voice_type': 'cute', 'description': '可爱日语女声'},
            {'name': 'けん', 'language': 'ja-jp', 'gender': 'male', 'voice_type': 'news', 'description': '新闻播报日语男声'},

            # 韩语声音
            {'name': '지은', 'language': 'ko-kr', 'gender': 'female', 'voice_type': 'sweet', 'description': '甜美韩语女声'},
            {'name': '민수', 'language': 'ko-kr', 'gender': 'male', 'voice_type': 'standard', 'description': '标准韩语男声'},
            {'name': '수진', 'language': 'ko-kr', 'gender': 'female', 'voice_type': 'gentle', 'description': '温柔韩语女声'},
            {'name': '준호', 'language': 'ko-kr', 'gender': 'male', 'voice_type': 'magnetic', 'description': '磁性韩语男声'},
            {'name': '예린', 'language': 'ko-kr', 'gender': 'female', 'voice_type': 'cute', 'description': '可爱韩语女声'},
            {'name': '현우', 'language': 'ko-kr', 'gender': 'male', 'voice_type': 'news', 'description': '新闻播报韩语男声'},

            # 中文声音
            {'name': '小雅', 'language': 'zh-cn', 'gender': 'female', 'voice_type': 'sweet', 'description': '甜美中文女声'},
            {'name': '小明', 'language': 'zh-cn', 'gender': 'male', 'voice_type': 'standard', 'description': '标准中文男声'},
            {'name': '小慧', 'language': 'zh-cn', 'gender': 'female', 'voice_type': 'gentle', 'description': '温柔中文女声'},
            {'name': '小强', 'language': 'zh-cn', 'gender': 'male', 'voice_type': 'magnetic', 'description': '磁性中文男声'},
        ]

        for voice_data in voices_data:
            voice, created = VoiceModel.objects.get_or_create(
                name=voice_data['name'],
                language=voice_data['language'],
                defaults=voice_data
            )
            if created:
                self.stdout.write(f'创建声音: {voice.name} ({voice.get_language_display()})')

        self.stdout.write(self.style.SUCCESS('示例声音数据创建完成！'))
