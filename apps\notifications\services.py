"""
通知服务
"""
import json
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync
from django.contrib.auth import get_user_model
from .models import Notification, NotificationTemplate

User = get_user_model()


class NotificationService:
    """通知服务类"""
    
    def __init__(self):
        self.channel_layer = get_channel_layer()
    
    def create_notification(self, user, notification_type, title, message, 
                          priority='normal', extra_data=None):
        """创建通知"""
        notification = Notification.objects.create(
            user=user,
            type=notification_type,
            title=title,
            message=message,
            priority=priority
        )
        
        if extra_data:
            notification.set_extra_data_dict(extra_data)
            notification.save()
        
        # 发送WebSocket通知
        self.send_notification_to_user(user.id, {
            'id': notification.id,
            'type': notification_type,
            'title': title,
            'message': message,
            'priority': priority,
            'created_at': notification.created_at.isoformat(),
            'extra_data': extra_data or {}
        })
        
        return notification
    
    def create_notification_from_template(self, user, template_type, context=None):
        """从模板创建通知"""
        try:
            template = NotificationTemplate.objects.get(
                type=template_type, 
                is_active=True
            )
            title, message = template.render(context or {})
            
            return self.create_notification(
                user=user,
                notification_type=template_type,
                title=title,
                message=message,
                extra_data=context
            )
        except NotificationTemplate.DoesNotExist:
            # 如果模板不存在，创建默认通知
            return self.create_notification(
                user=user,
                notification_type=template_type,
                title='系统通知',
                message='您有新的通知',
                extra_data=context
            )
    
    def send_notification_to_user(self, user_id, notification_data):
        """发送通知给特定用户"""
        if self.channel_layer:
            async_to_sync(self.channel_layer.group_send)(
                f"notifications_{user_id}",
                {
                    'type': 'notification_message',
                    'notification': notification_data
                }
            )
    
    def send_progress_update(self, user_id, task_id, progress, message=''):
        """发送进度更新"""
        if self.channel_layer:
            async_to_sync(self.channel_layer.group_send)(
                f"notifications_{user_id}",
                {
                    'type': 'progress_update',
                    'task_id': task_id,
                    'progress': progress,
                    'message': message
                }
            )
    
    def send_video_progress(self, video_id, progress, status, message=''):
        """发送视频生成进度"""
        if self.channel_layer:
            async_to_sync(self.channel_layer.group_send)(
                f"video_progress_{video_id}",
                {
                    'type': 'progress_update',
                    'progress': progress,
                    'status': status,
                    'message': message,
                    'timestamp': json.dumps(timezone.now(), default=str)
                }
            )
    
    def send_system_message(self, user_id, message, level='info'):
        """发送系统消息"""
        if self.channel_layer:
            async_to_sync(self.channel_layer.group_send)(
                f"notifications_{user_id}",
                {
                    'type': 'system_message',
                    'message': message,
                    'level': level
                }
            )
    
    def notify_video_complete(self, video):
        """通知视频生成完成"""
        self.create_notification_from_template(
            user=video.user,
            template_type='video_complete',
            context={
                'video_title': video.title,
                'video_id': video.id,
                'duration': video.video_duration or '未知'
            }
        )
    
    def notify_voice_complete(self, voice_task):
        """通知语音合成完成"""
        self.create_notification_from_template(
            user=voice_task.user,
            template_type='voice_complete',
            context={
                'voice_name': voice_task.voice_model.name,
                'task_id': voice_task.id
            }
        )
    
    def notify_copywriting_complete(self, copywriting):
        """通知文案生成完成"""
        self.create_notification_from_template(
            user=copywriting.user,
            template_type='copywriting_complete',
            context={
                'title': copywriting.title,
                'word_count': copywriting.actual_word_count
            }
        )
    
    def notify_package_expire(self, user, package, days_left):
        """通知套餐即将过期"""
        self.create_notification_from_template(
            user=user,
            template_type='package_expire',
            context={
                'package_type': package.get_package_type_display(),
                'days_left': days_left
            }
        )
    
    def notify_computing_low(self, user, current_power, threshold=100):
        """通知算力不足"""
        if current_power <= threshold:
            self.create_notification_from_template(
                user=user,
                template_type='computing_low',
                context={
                    'current_power': current_power,
                    'threshold': threshold
                }
            )
    
    def notify_error(self, user, error_message, context=None):
        """通知错误"""
        self.create_notification_from_template(
            user=user,
            template_type='error',
            context={
                'error_message': error_message,
                **(context or {})
            }
        )
    
    def get_user_notifications(self, user, limit=20, unread_only=False):
        """获取用户通知"""
        queryset = Notification.objects.filter(user=user)
        
        if unread_only:
            queryset = queryset.filter(is_read=False)
        
        return queryset.order_by('-created_at')[:limit]
    
    def mark_all_read(self, user):
        """标记所有通知为已读"""
        from django.utils import timezone
        Notification.objects.filter(
            user=user, 
            is_read=False
        ).update(
            is_read=True, 
            read_at=timezone.now()
        )


# 全局通知服务实例
notification_service = NotificationService()
