"""
智能文案相关序列化器
"""
from rest_framework import serializers
from .models import Copywriting, CopywritingTemplate, CopywritingHistory, TextExtraction


class CopywritingSerializer(serializers.ModelSerializer):
    """智能文案序列化器"""
    user_display = serializers.CharField(source='user.display_name', read_only=True)
    scene_type_display = serializers.CharField(source='get_scene_type_display', read_only=True)
    word_count_display = serializers.CharField(source='get_word_count_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    language_display = serializers.CharField(source='get_language_display', read_only=True)
    estimated_duration = serializers.CharField(read_only=True)
    
    class Meta:
        model = Copywriting
        fields = (
            'id', 'user', 'user_display', 'title', 'scene_type', 'scene_type_display',
            'theme_description', 'word_count', 'word_count_display', 'language',
            'language_display', 'status', 'status_display', 'generated_content',
            'actual_word_count', 'error_message', 'computing_power_consumed',
            'is_saved_as_draft', 'estimated_duration', 'created_at', 'updated_at', 'completed_at'
        )
        read_only_fields = (
            'id', 'user', 'status', 'generated_content', 'actual_word_count',
            'error_message', 'computing_power_consumed', 'created_at', 'updated_at', 'completed_at'
        )


class CopywritingTemplateSerializer(serializers.ModelSerializer):
    """文案模板序列化器"""
    scene_type_display = serializers.CharField(source='get_scene_type_display', read_only=True)
    created_by_display = serializers.CharField(source='created_by.display_name', read_only=True)
    
    class Meta:
        model = CopywritingTemplate
        fields = (
            'id', 'name', 'description', 'scene_type', 'scene_type_display',
            'template_content', 'variables', 'is_active', 'usage_count',
            'created_by', 'created_by_display', 'created_at', 'updated_at'
        )
        read_only_fields = (
            'id', 'usage_count', 'created_at', 'updated_at'
        )


class CopywritingHistorySerializer(serializers.ModelSerializer):
    """文案历史序列化器"""
    copywriting_title = serializers.CharField(source='copywriting.title', read_only=True)
    
    class Meta:
        model = CopywritingHistory
        fields = (
            'id', 'copywriting', 'copywriting_title', 'version', 'content',
            'word_count', 'generation_params', 'created_at'
        )
        read_only_fields = ('id', 'created_at')


class TextExtractionSerializer(serializers.ModelSerializer):
    """文案提取序列化器"""
    user_display = serializers.CharField(source='user.display_name', read_only=True)
    source_type_display = serializers.CharField(source='get_source_type_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    
    class Meta:
        model = TextExtraction
        fields = (
            'id', 'user', 'user_display', 'source_type', 'source_type_display',
            'source_file', 'source_url', 'status', 'status_display',
            'extracted_text', 'confidence_score', 'error_message',
            'computing_power_consumed', 'created_at', 'updated_at', 'completed_at'
        )
        read_only_fields = (
            'id', 'user', 'status', 'extracted_text', 'confidence_score',
            'error_message', 'computing_power_consumed', 'created_at',
            'updated_at', 'completed_at'
        )
