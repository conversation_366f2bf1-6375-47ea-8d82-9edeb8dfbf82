from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from digital_works.models import DigitalWork, WorkTemplate
from datetime import datetime, timedelta
import random

User = get_user_model()


class Command(BaseCommand):
    help = '填充数字作品示例数据'

    def handle(self, *args, **options):
        self.stdout.write('开始填充数字作品数据...')

        # 获取或创建测试用户
        admin_user, created = User.objects.get_or_create(
            username='admin',
            defaults={
                'email': '<EMAIL>',
                'is_staff': True,
                'is_superuser': True
            }
        )
        if created:
            admin_user.set_password('admin123')
            admin_user.save()

        # 创建作品模板
        templates_data = [
            {
                'name': '商务介绍模板',
                'description': '适合企业产品介绍和商务展示的专业模板',
                'thumbnail': '/static/images/templates/business.jpg',
                'preview_video': '/static/videos/templates/business_preview.mp4',
                'default_avatar': '商务男士',
                'default_voice': '专业男声',
                'default_script': '欢迎了解我们的产品和服务，我们致力于为客户提供最优质的解决方案。',
                'category': 'business',
                'tags': ['商务', '专业', '介绍'],
                'is_active': True,
                'is_premium': False,
                'sort_order': 1,
                'usage_count': 156
            },
            {
                'name': '教育培训模板',
                'description': '专为在线教育和培训课程设计的模板',
                'thumbnail': '/static/images/templates/education.jpg',
                'preview_video': '/static/videos/templates/education_preview.mp4',
                'default_avatar': '知性女士',
                'default_voice': '温和女声',
                'default_script': '今天我们将学习一个重要的概念，请大家认真听讲并积极参与讨论。',
                'category': 'education',
                'tags': ['教育', '培训', '学习'],
                'is_active': True,
                'is_premium': True,
                'sort_order': 2,
                'usage_count': 89
            },
            {
                'name': '营销推广模板',
                'description': '高转化率的营销推广视频模板',
                'thumbnail': '/static/images/templates/marketing.jpg',
                'preview_video': '/static/videos/templates/marketing_preview.mp4',
                'default_avatar': '活力女士',
                'default_voice': '活泼女声',
                'default_script': '限时优惠！现在购买享受8折优惠，机会难得，不要错过！',
                'category': 'marketing',
                'tags': ['营销', '推广', '优惠'],
                'is_active': True,
                'is_premium': True,
                'sort_order': 3,
                'usage_count': 234
            }
        ]

        for template_data in templates_data:
            template, created = WorkTemplate.objects.get_or_create(
                name=template_data['name'],
                defaults=template_data
            )
            if created:
                self.stdout.write(f'创建模板: {template.name}')

        # 创建数字作品
        works_data = [
            {
                'name': '产品介绍视频',
                'description': '公司最新产品的详细介绍视频',
                'thumbnail': '/static/images/works/product_intro_thumb.jpg',
                'video_url': '/static/videos/works/product_intro.mp4',
                'duration': '02:35',
                'status': 'completed',
                'avatar_name': '商务男士',
                'voice_name': '专业男声',
                'script_content': '欢迎了解我们的最新产品，这款产品具有创新的设计和强大的功能...',
                'computing_power_cost': 50,
                'generation_time': 125.5,
                'file_size': 45678912,  # 约43.5MB
                'resolution': '1920x1080',
                'frame_rate': '30fps'
            },
            {
                'name': '企业文化宣传',
                'description': '展示企业文化和价值观的宣传视频',
                'thumbnail': '/static/images/works/culture_thumb.jpg',
                'video_url': '/static/videos/works/culture.mp4',
                'duration': '01:48',
                'status': 'completed',
                'avatar_name': '知性女士',
                'voice_name': '温和女声',
                'script_content': '我们的企业文化注重创新、协作和客户至上的理念...',
                'computing_power_cost': 35,
                'generation_time': 89.2,
                'file_size': 32145678,  # 约30.6MB
                'resolution': '1920x1080',
                'frame_rate': '30fps'
            },
            {
                'name': '新品发布会预告',
                'description': '即将举行的新品发布会预告片',
                'thumbnail': '/static/images/works/launch_thumb.jpg',
                'video_url': '',
                'duration': '',
                'status': 'processing',
                'avatar_name': '活力女士',
                'voice_name': '活泼女声',
                'script_content': '激动人心的时刻即将到来！我们的新品发布会将为您带来惊喜...',
                'computing_power_cost': 60,
                'generation_time': 0.0,
                'file_size': 0,
                'resolution': '1920x1080',
                'frame_rate': '30fps'
            }
        ]

        for i, work_data in enumerate(works_data):
            work_data['user'] = admin_user
            
            # 设置创建时间（最近几天）
            days_ago = random.randint(1, 7)
            work_data['created_at'] = datetime.now() - timedelta(days=days_ago)
            
            # 如果是已完成的作品，设置完成时间
            if work_data['status'] == 'completed':
                work_data['completed_at'] = work_data['created_at'] + timedelta(
                    seconds=work_data['generation_time']
                )

            work, created = DigitalWork.objects.get_or_create(
                name=work_data['name'],
                user=admin_user,
                defaults=work_data
            )
            if created:
                self.stdout.write(f'创建作品: {work.name}')

        self.stdout.write(self.style.SUCCESS('数字作品数据填充完成！'))
