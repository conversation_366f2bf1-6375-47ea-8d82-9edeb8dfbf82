# Generated by Django 5.2.1 on 2025-07-16 12:35

import apps.uploads.models
import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="ChunkedUpload",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "upload_id",
                    models.UUIDField(
                        default=uuid.uuid4, unique=True, verbose_name="上传ID"
                    ),
                ),
                ("filename", models.CharField(max_length=255, verbose_name="文件名")),
                ("total_size", models.BigIntegerField(verbose_name="总大小")),
                (
                    "chunk_size",
                    models.IntegerField(default=1048576, verbose_name="块大小"),
                ),
                (
                    "uploaded_size",
                    models.BigIntegerField(default=0, verbose_name="已上传大小"),
                ),
                (
                    "is_completed",
                    models.BooleanField(default=False, verbose_name="是否完成"),
                ),
                (
                    "file_type",
                    models.CharField(blank=True, max_length=100, verbose_name="文件类型"),
                ),
                (
                    "upload_type",
                    models.CharField(
                        choices=[
                            ("avatar", "数字人形象"),
                            ("voice", "语音文件"),
                            ("video", "视频文件"),
                            ("image", "图片文件"),
                            ("document", "文档文件"),
                            ("other", "其他文件"),
                        ],
                        max_length=20,
                        verbose_name="上传类型",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                ("expires_at", models.DateTimeField(verbose_name="过期时间")),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="用户",
                    ),
                ),
            ],
            options={
                "verbose_name": "分块上传",
                "verbose_name_plural": "分块上传",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="UploadChunk",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("chunk_number", models.IntegerField(verbose_name="块编号")),
                ("chunk_data", models.BinaryField(verbose_name="块数据")),
                ("chunk_size", models.IntegerField(verbose_name="块大小")),
                (
                    "checksum",
                    models.CharField(blank=True, max_length=64, verbose_name="校验和"),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "chunked_upload",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="chunks",
                        to="uploads.chunkedupload",
                        verbose_name="分块上传",
                    ),
                ),
            ],
            options={
                "verbose_name": "上传块",
                "verbose_name_plural": "上传块",
                "ordering": ["chunk_number"],
                "unique_together": {("chunked_upload", "chunk_number")},
            },
        ),
        migrations.CreateModel(
            name="UploadFile",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "upload_type",
                    models.CharField(
                        choices=[
                            ("avatar", "数字人形象"),
                            ("voice", "语音文件"),
                            ("video", "视频文件"),
                            ("image", "图片文件"),
                            ("document", "文档文件"),
                            ("other", "其他文件"),
                        ],
                        max_length=20,
                        verbose_name="上传类型",
                    ),
                ),
                (
                    "original_filename",
                    models.CharField(max_length=255, verbose_name="原始文件名"),
                ),
                (
                    "file",
                    models.FileField(
                        upload_to=apps.uploads.models.upload_to_path, verbose_name="文件"
                    ),
                ),
                ("file_size", models.BigIntegerField(default=0, verbose_name="文件大小")),
                (
                    "file_type",
                    models.CharField(blank=True, max_length=100, verbose_name="文件类型"),
                ),
                (
                    "file_hash",
                    models.CharField(blank=True, max_length=64, verbose_name="文件哈希"),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("uploading", "上传中"),
                            ("completed", "上传完成"),
                            ("failed", "上传失败"),
                            ("processing", "处理中"),
                            ("processed", "处理完成"),
                        ],
                        default="uploading",
                        max_length=20,
                        verbose_name="状态",
                    ),
                ),
                ("progress", models.IntegerField(default=0, verbose_name="上传进度")),
                ("error_message", models.TextField(blank=True, verbose_name="错误信息")),
                (
                    "metadata",
                    models.JSONField(blank=True, default=dict, verbose_name="元数据"),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "completed_at",
                    models.DateTimeField(blank=True, null=True, verbose_name="完成时间"),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="用户",
                    ),
                ),
            ],
            options={
                "verbose_name": "上传文件",
                "verbose_name_plural": "上传文件",
                "ordering": ["-created_at"],
                "indexes": [
                    models.Index(
                        fields=["user", "status"], name="uploads_upl_user_id_01cd89_idx"
                    ),
                    models.Index(
                        fields=["upload_type", "created_at"],
                        name="uploads_upl_upload__476de0_idx",
                    ),
                ],
            },
        ),
    ]
