"""
套餐系统视图
"""
from django.shortcuts import render
from django.db.models import Sum, Q
from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.utils import timezone
from .models import PackageCategory, Package
from .serializers import (
    PackageCategorySerializer, PackageSerializer, PackageListSerializer,
    RechargePackageSerializer
)


class PackageCategoryViewSet(viewsets.ReadOnlyModelViewSet):
    """套餐分类视图集"""
    queryset = PackageCategory.objects.filter(is_active=True)
    serializer_class = PackageCategorySerializer
    permission_classes = [permissions.AllowAny]
    authentication_classes = []


class PackageViewSet(viewsets.ReadOnlyModelViewSet):
    """套餐视图集"""
    queryset = Package.objects.filter(is_active=True)
    serializer_class = PackageSerializer
    permission_classes = [permissions.AllowAny]
    authentication_classes = []

    def get_serializer_class(self):
        if self.action == 'list':
            return PackageListSerializer
        return PackageSerializer

    def get_queryset(self):
        queryset = super().get_queryset()

        # 按类型筛选
        package_type = self.request.query_params.get('type')
        if package_type:
            queryset = queryset.filter(package_type=package_type)

        # 按分类筛选
        category = self.request.query_params.get('category')
        if category:
            queryset = queryset.filter(category_id=category)

        return queryset.select_related('category')

    @action(detail=False, methods=['get'])
    def computing_power(self, request):
        """获取算力套餐"""
        packages = self.get_queryset().filter(package_type='computing_power')
        serializer = PackageListSerializer(packages, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def membership(self, request):
        """获取会员套餐"""
        packages = self.get_queryset().filter(package_type='membership')
        serializer = PackageListSerializer(packages, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def recommended(self, request):
        """获取推荐套餐"""
        packages = self.get_queryset().filter(is_recommended=True)
        serializer = PackageListSerializer(packages, many=True)
        return Response(serializer.data)


class RechargeViewSet(viewsets.ViewSet):
    """充值套餐视图集"""
    permission_classes = [permissions.AllowAny]
    authentication_classes = []

    @action(detail=False, methods=['get'])
    def packages(self, request):
        """获取充值套餐列表"""
        # 返回硬编码的充值套餐数据，与前端RechargeCenter.vue中的数据一致
        packages_data = [
            {'id': 1, 'amount': 100, 'price': 10, 'bonus': 0},
            {'id': 2, 'amount': 500, 'price': 45, 'bonus': 50},
            {'id': 3, 'amount': 1000, 'price': 80, 'bonus': 200},
            {'id': 4, 'amount': 2000, 'price': 150, 'bonus': 500},
            {'id': 5, 'amount': 5000, 'price': 350, 'bonus': 1500},
            {'id': 6, 'amount': 10000, 'price': 650, 'bonus': 3500}
        ]

        serializer = RechargePackageSerializer(packages_data, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['post'])
    def purchase(self, request):
        """购买充值套餐"""
        package_id = request.data.get('package_id')
        if not package_id:
            return Response(
                {'error': '套餐ID不能为空'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # 查找套餐
        packages_data = [
            {'id': 1, 'amount': 100, 'price': 10, 'bonus': 0},
            {'id': 2, 'amount': 500, 'price': 45, 'bonus': 50},
            {'id': 3, 'amount': 1000, 'price': 80, 'bonus': 200},
            {'id': 4, 'amount': 2000, 'price': 150, 'bonus': 500},
            {'id': 5, 'amount': 5000, 'price': 350, 'bonus': 1500},
            {'id': 6, 'amount': 10000, 'price': 650, 'bonus': 3500}
        ]

        package = next((p for p in packages_data if p['id'] == int(package_id)), None)
        if not package:
            return Response(
                {'error': '套餐不存在'},
                status=status.HTTP_404_NOT_FOUND
            )

        # 暂时模拟购买成功
        return Response({
            'message': f'充值{package["amount"]}算力成功',
            'amount': package['amount'],
            'bonus': package['bonus'],
            'total': package['amount'] + package['bonus'],
            'price': package['price']
        })
