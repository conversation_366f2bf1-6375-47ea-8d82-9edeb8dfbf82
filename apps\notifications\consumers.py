"""
WebSocket消费者
"""
import json
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from django.contrib.auth import get_user_model

User = get_user_model()


class NotificationConsumer(AsyncWebsocketConsumer):
    """通知WebSocket消费者"""
    
    async def connect(self):
        """连接处理"""
        self.user = self.scope["user"]
        
        # 只允许认证用户连接
        if self.user.is_anonymous:
            await self.close()
            return
        
        # 加入用户专属的通知组
        self.notification_group_name = f"notifications_{self.user.id}"
        
        await self.channel_layer.group_add(
            self.notification_group_name,
            self.channel_name
        )
        
        await self.accept()
        
        # 发送连接成功消息
        await self.send(text_data=json.dumps({
            'type': 'connection_established',
            'message': '通知连接已建立'
        }))
    
    async def disconnect(self, close_code):
        """断开连接处理"""
        if hasattr(self, 'notification_group_name'):
            await self.channel_layer.group_discard(
                self.notification_group_name,
                self.channel_name
            )
    
    async def receive(self, text_data):
        """接收消息处理"""
        try:
            text_data_json = json.loads(text_data)
            message_type = text_data_json.get('type')
            
            if message_type == 'mark_read':
                # 标记通知为已读
                notification_id = text_data_json.get('notification_id')
                if notification_id:
                    await self.mark_notification_read(notification_id)
            
            elif message_type == 'get_unread_count':
                # 获取未读通知数量
                count = await self.get_unread_count()
                await self.send(text_data=json.dumps({
                    'type': 'unread_count',
                    'count': count
                }))
        
        except json.JSONDecodeError:
            await self.send(text_data=json.dumps({
                'type': 'error',
                'message': '无效的JSON数据'
            }))
    
    async def notification_message(self, event):
        """发送通知消息"""
        await self.send(text_data=json.dumps({
            'type': 'notification',
            'notification': event['notification']
        }))
    
    async def progress_update(self, event):
        """发送进度更新"""
        await self.send(text_data=json.dumps({
            'type': 'progress_update',
            'task_id': event['task_id'],
            'progress': event['progress'],
            'message': event.get('message', '')
        }))
    
    async def system_message(self, event):
        """发送系统消息"""
        await self.send(text_data=json.dumps({
            'type': 'system_message',
            'message': event['message'],
            'level': event.get('level', 'info')
        }))
    
    @database_sync_to_async
    def mark_notification_read(self, notification_id):
        """标记通知为已读"""
        from .models import Notification
        try:
            notification = Notification.objects.get(
                id=notification_id, 
                user=self.user
            )
            notification.mark_as_read()
            return True
        except Notification.DoesNotExist:
            return False
    
    @database_sync_to_async
    def get_unread_count(self):
        """获取未读通知数量"""
        from .models import Notification
        return Notification.objects.filter(
            user=self.user, 
            is_read=False
        ).count()


class VideoProgressConsumer(AsyncWebsocketConsumer):
    """视频生成进度WebSocket消费者"""
    
    async def connect(self):
        """连接处理"""
        self.user = self.scope["user"]
        self.video_id = self.scope['url_route']['kwargs']['video_id']
        
        # 只允许认证用户连接
        if self.user.is_anonymous:
            await self.close()
            return
        
        # 验证用户是否有权限查看此视频
        has_permission = await self.check_video_permission()
        if not has_permission:
            await self.close()
            return
        
        # 加入视频进度组
        self.progress_group_name = f"video_progress_{self.video_id}"
        
        await self.channel_layer.group_add(
            self.progress_group_name,
            self.channel_name
        )
        
        await self.accept()
        
        # 发送当前进度
        current_progress = await self.get_current_progress()
        await self.send(text_data=json.dumps({
            'type': 'current_progress',
            'progress': current_progress
        }))
    
    async def disconnect(self, close_code):
        """断开连接处理"""
        if hasattr(self, 'progress_group_name'):
            await self.channel_layer.group_discard(
                self.progress_group_name,
                self.channel_name
            )
    
    async def progress_update(self, event):
        """发送进度更新"""
        await self.send(text_data=json.dumps({
            'type': 'progress_update',
            'progress': event['progress'],
            'status': event.get('status'),
            'message': event.get('message', ''),
            'timestamp': event.get('timestamp')
        }))
    
    @database_sync_to_async
    def check_video_permission(self):
        """检查用户是否有权限查看视频"""
        from apps.videos.models import VideoProduction
        try:
            video = VideoProduction.objects.get(id=self.video_id)
            return video.user == self.user or self.user.is_staff
        except VideoProduction.DoesNotExist:
            return False
    
    @database_sync_to_async
    def get_current_progress(self):
        """获取当前进度"""
        from apps.videos.models import VideoProduction
        try:
            video = VideoProduction.objects.get(id=self.video_id)
            return {
                'progress': video.progress,
                'status': video.status,
                'error_message': video.error_message
            }
        except VideoProduction.DoesNotExist:
            return {'progress': 0, 'status': 'not_found'}
