"""
套餐系统序列化器
"""
from rest_framework import serializers
from .models import PackageCategory, Package


class PackageCategorySerializer(serializers.ModelSerializer):
    """套餐分类序列化器"""
    
    class Meta:
        model = PackageCategory
        fields = [
            'id', 'name', 'description', 'sort_order', 
            'is_active', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']


class PackageSerializer(serializers.ModelSerializer):
    """套餐序列化器"""
    
    category_name = serializers.CharField(source='category.name', read_only=True)
    discount_percentage = serializers.ReadOnlyField()
    total_computing_power = serializers.ReadOnlyField()
    
    class Meta:
        model = Package
        fields = [
            'id', 'category', 'category_name', 'name', 'description', 
            'package_type', 'price', 'original_price', 'discount_percentage',
            'computing_power_amount', 'bonus_computing_power', 'total_computing_power',
            'synthesis_duration', 'voice_clone_count', 'avatar_count',
            'duration_type', 'duration_value', 'features',
            'is_active', 'is_recommended', 'sort_order', 'sales_count',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['sales_count', 'created_at', 'updated_at']



class PackageListSerializer(serializers.ModelSerializer):
    """套餐列表序列化器（简化版）"""
    
    category_name = serializers.CharField(source='category.name', read_only=True)
    discount_percentage = serializers.ReadOnlyField()
    total_computing_power = serializers.ReadOnlyField()
    
    class Meta:
        model = Package
        fields = [
            'id', 'category_name', 'name', 'package_type', 
            'price', 'original_price', 'discount_percentage',
            'computing_power_amount', 'bonus_computing_power', 'total_computing_power',
            'synthesis_duration', 'voice_clone_count', 'avatar_count',
            'duration_type', 'duration_value', 'features',
            'is_recommended', 'sales_count'
        ]


class RechargePackageSerializer(serializers.Serializer):
    """充值套餐序列化器（用于前端显示）"""

    id = serializers.IntegerField()
    amount = serializers.IntegerField()
    price = serializers.DecimalField(max_digits=10, decimal_places=2)
    bonus = serializers.IntegerField(default=0)
