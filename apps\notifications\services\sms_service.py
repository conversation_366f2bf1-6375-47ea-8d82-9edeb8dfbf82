"""
短信服务
支持阿里云短信、腾讯云短信等多种短信服务商
"""
import json
import logging
import random
import string
from typing import Dict, Any, Optional
from django.core.cache import cache
from django.utils import timezone
from datetime import timedelta
from apps.config.services import api_config

logger = logging.getLogger(__name__)

try:
    from alibabacloud_dysmsapi20170525.client import Client as DysmsapiClient
    from alibabacloud_tea_openapi import models as open_api_models
    from alibabacloud_dysmsapi20170525 import models as dysmsapi_models
    ALIYUN_SMS_AVAILABLE = True
except ImportError:
    ALIYUN_SMS_AVAILABLE = False
    logger.warning("阿里云短信SDK未安装")

try:
    from tencentcloud.common import credential
    from tencentcloud.sms.v20210111 import sms_client, models as sms_models
    TENCENT_SMS_AVAILABLE = True
except ImportError:
    TENCENT_SMS_AVAILABLE = False
    logger.warning("腾讯云短信SDK未安装")


class AliyunSMSService:
    """阿里云短信服务"""
    
    def __init__(self):
        self.client = None
        self._initialize_service()
    
    def _initialize_service(self):
        """初始化阿里云短信服务"""
        if not ALIYUN_SMS_AVAILABLE:
            return
        
        try:
            config = api_config.get_aliyun_sms_config()
            
            if not all([config['access_key'], config['secret_key']]):
                logger.warning("阿里云短信配置不完整")
                return
            
            # 配置客户端
            config_obj = open_api_models.Config(
                access_key_id=config['access_key'],
                access_key_secret=config['secret_key']
            )
            config_obj.endpoint = 'dysmsapi.aliyuncs.com'
            
            self.client = DysmsapiClient(config_obj)
            logger.info("阿里云短信服务初始化成功")
            
        except Exception as e:
            logger.error(f"阿里云短信服务初始化失败: {e}")
            self.client = None
    
    def is_available(self) -> bool:
        """检查服务是否可用"""
        return ALIYUN_SMS_AVAILABLE and self.client is not None
    
    def send_sms(
        self,
        phone_number: str,
        template_code: str,
        template_params: Dict[str, str] = None,
        sign_name: str = None
    ) -> Dict[str, Any]:
        """
        发送短信
        
        Args:
            phone_number: 手机号码
            template_code: 短信模板代码
            template_params: 模板参数
            sign_name: 短信签名
        
        Returns:
            发送结果
        """
        if not self.is_available():
            return {
                'success': False,
                'error': '阿里云短信服务不可用，请检查配置'
            }
        
        try:
            config = api_config.get_aliyun_sms_config()
            
            # 构建请求
            request = dysmsapi_models.SendSmsRequest(
                phone_numbers=phone_number,
                sign_name=sign_name or config.get('sign_name', ''),
                template_code=template_code,
                template_param=json.dumps(template_params or {})
            )
            
            # 发送短信
            response = self.client.send_sms(request)
            
            if response.body.code == 'OK':
                return {
                    'success': True,
                    'message': '短信发送成功',
                    'biz_id': response.body.biz_id,
                    'request_id': response.body.request_id
                }
            else:
                return {
                    'success': False,
                    'error': f'短信发送失败: {response.body.message}',
                    'code': response.body.code
                }
                
        except Exception as e:
            logger.error(f"阿里云短信发送失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }


class TencentSMSService:
    """腾讯云短信服务"""
    
    def __init__(self):
        self.client = None
        self._initialize_service()
    
    def _initialize_service(self):
        """初始化腾讯云短信服务"""
        if not TENCENT_SMS_AVAILABLE:
            return
        
        try:
            config = api_config.get_tencent_sms_config()
            
            if not all([config['secret_id'], config['secret_key']]):
                logger.warning("腾讯云短信配置不完整")
                return
            
            # 实例化认证对象
            cred = credential.Credential(
                config['secret_id'],
                config['secret_key']
            )
            
            # 实例化客户端
            self.client = sms_client.SmsClient(
                cred,
                config.get('region', 'ap-beijing')
            )
            
            logger.info("腾讯云短信服务初始化成功")
            
        except Exception as e:
            logger.error(f"腾讯云短信服务初始化失败: {e}")
            self.client = None
    
    def is_available(self) -> bool:
        """检查服务是否可用"""
        return TENCENT_SMS_AVAILABLE and self.client is not None
    
    def send_sms(
        self,
        phone_number: str,
        template_id: str,
        template_params: list = None,
        sign_name: str = None
    ) -> Dict[str, Any]:
        """发送短信"""
        if not self.is_available():
            return {
                'success': False,
                'error': '腾讯云短信服务不可用，请检查配置'
            }
        
        try:
            config = api_config.get_tencent_sms_config()
            
            # 构建请求
            request = sms_models.SendSmsRequest()
            request.PhoneNumberSet = [phone_number]
            request.SmsSdkAppId = config.get('app_id')
            request.SignName = sign_name or config.get('sign_name', '')
            request.TemplateId = template_id
            request.TemplateParamSet = template_params or []
            
            # 发送短信
            response = self.client.SendSms(request)
            
            if response.SendStatusSet[0].Code == 'Ok':
                return {
                    'success': True,
                    'message': '短信发送成功',
                    'serial_no': response.SendStatusSet[0].SerialNo,
                    'request_id': response.RequestId
                }
            else:
                return {
                    'success': False,
                    'error': f'短信发送失败: {response.SendStatusSet[0].Message}',
                    'code': response.SendStatusSet[0].Code
                }
                
        except Exception as e:
            logger.error(f"腾讯云短信发送失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }


class SMSService:
    """统一短信服务接口"""
    
    def __init__(self):
        self.aliyun = AliyunSMSService()
        self.tencent = TencentSMSService()
        self.default_provider = self._get_default_provider()
    
    def _get_default_provider(self) -> str:
        """获取默认短信服务商"""
        if self.aliyun.is_available():
            return 'aliyun'
        elif self.tencent.is_available():
            return 'tencent'
        else:
            return None
    
    def send_verification_code(
        self,
        phone_number: str,
        code_type: str = 'login',
        provider: str = None
    ) -> Dict[str, Any]:
        """
        发送验证码
        
        Args:
            phone_number: 手机号码
            code_type: 验证码类型 (login, register, reset_password)
            provider: 短信服务商
        
        Returns:
            发送结果
        """
        provider = provider or self.default_provider
        
        if not provider:
            return {
                'success': False,
                'error': '没有可用的短信服务'
            }
        
        # 检查发送频率限制
        rate_limit_key = f"sms_rate_limit:{phone_number}"
        if cache.get(rate_limit_key):
            return {
                'success': False,
                'error': '发送过于频繁，请稍后再试'
            }
        
        # 生成验证码
        verification_code = self._generate_verification_code()
        
        # 缓存验证码
        cache_key = f"sms_code:{phone_number}:{code_type}"
        cache.set(cache_key, verification_code, 300)  # 5分钟有效期
        
        # 设置发送频率限制
        cache.set(rate_limit_key, True, 60)  # 1分钟内不能重复发送
        
        # 选择模板
        template_config = self._get_template_config(code_type, provider)
        
        # 发送短信
        if provider == 'aliyun':
            result = self.aliyun.send_sms(
                phone_number=phone_number,
                template_code=template_config['template_code'],
                template_params={'code': verification_code}
            )
        elif provider == 'tencent':
            result = self.tencent.send_sms(
                phone_number=phone_number,
                template_id=template_config['template_id'],
                template_params=[verification_code]
            )
        else:
            return {
                'success': False,
                'error': f'不支持的短信服务商: {provider}'
            }
        
        if result['success']:
            # 记录发送日志
            self._log_sms_send(phone_number, code_type, verification_code)
        
        return result
    
    def verify_code(
        self,
        phone_number: str,
        code: str,
        code_type: str = 'login'
    ) -> bool:
        """
        验证验证码
        
        Args:
            phone_number: 手机号码
            code: 验证码
            code_type: 验证码类型
        
        Returns:
            验证结果
        """
        cache_key = f"sms_code:{phone_number}:{code_type}"
        cached_code = cache.get(cache_key)
        
        if not cached_code:
            return False
        
        if cached_code == code:
            # 验证成功后删除缓存
            cache.delete(cache_key)
            return True
        
        return False
    
    def _generate_verification_code(self, length: int = 6) -> str:
        """生成验证码"""
        return ''.join(random.choices(string.digits, k=length))
    
    def _get_template_config(self, code_type: str, provider: str) -> Dict[str, str]:
        """获取短信模板配置"""
        templates = {
            'aliyun': {
                'login': {'template_code': 'SMS_123456789'},
                'register': {'template_code': 'SMS_123456790'},
                'reset_password': {'template_code': 'SMS_123456791'}
            },
            'tencent': {
                'login': {'template_id': '123456'},
                'register': {'template_id': '123457'},
                'reset_password': {'template_id': '123458'}
            }
        }
        
        return templates.get(provider, {}).get(code_type, {})
    
    def _log_sms_send(self, phone_number: str, code_type: str, code: str):
        """记录短信发送日志"""
        logger.info(f"短信发送成功: {phone_number}, 类型: {code_type}, 验证码: {code}")


# 创建全局实例
sms_service = SMSService()


def send_verification_code(phone_number: str, code_type: str = 'login') -> Dict[str, Any]:
    """发送验证码的便捷函数"""
    return sms_service.send_verification_code(phone_number, code_type)


def verify_sms_code(phone_number: str, code: str, code_type: str = 'login') -> bool:
    """验证短信验证码的便捷函数"""
    return sms_service.verify_code(phone_number, code, code_type)
