"""
用户相关视图
"""
from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.views import APIView
from django.contrib.auth import login, logout
from django.utils.translation import gettext_lazy as _
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import SearchFilter, OrderingFilter
from rest_framework_simplejwt.tokens import RefreshToken
from django.db.models import Count, Sum, Q
from django.utils import timezone
from datetime import timedelta
from apps.common.cache import cache_response, cache_result, invalidate_cache
from apps.common.throttling import LoginRateThrottle, RegisterRateThrottle

from .models import User, ComputingPowerLog, UserPackage
from .serializers import (
    UserSerializer, UserProfileSerializer, LoginSerializer, 
    RegisterSerializer, ComputingPowerLogSerializer, UserPackageSerializer
)


class UserViewSet(viewsets.ModelViewSet):
    """用户管理视图集"""
    queryset = User.objects.all()
    serializer_class = UserSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['is_active', 'is_verified', 'package_type']
    search_fields = ['username', 'email', 'phone', 'first_name', 'last_name']
    ordering_fields = ['date_joined', 'computing_power']
    ordering = ['-date_joined']
    
    def get_permissions(self):
        """根据操作类型设置权限"""
        if self.action == 'create':
            permission_classes = [permissions.AllowAny]
        elif self.action in ['update', 'partial_update', 'destroy']:
            permission_classes = [permissions.IsAdminUser]
        else:
            permission_classes = [permissions.IsAuthenticated]
        return [permission() for permission in permission_classes]
    
    @action(detail=True, methods=['post'])
    def add_computing_power(self, request, pk=None):
        """为用户增加算力"""
        user = self.get_object()
        amount = request.data.get('amount', 0)
        reason = request.data.get('reason', '管理员手动添加')
        
        try:
            amount = int(amount)
            if amount <= 0:
                return Response(
                    {'error': '算力数量必须大于0'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            user.add_computing_power(amount, reason)
            return Response({
                'message': f'成功为用户 {user.display_name} 增加 {amount} 算力',
                'current_power': user.computing_power
            })
        except (ValueError, TypeError):
            return Response(
                {'error': '算力数量必须是有效的数字'},
                status=status.HTTP_400_BAD_REQUEST
            )
    
    @action(detail=True, methods=['post'])
    def consume_computing_power(self, request, pk=None):
        """消耗用户算力"""
        user = self.get_object()
        amount = request.data.get('amount', 0)
        reason = request.data.get('reason', '管理员手动扣除')
        
        try:
            amount = int(amount)
            if amount <= 0:
                return Response(
                    {'error': '算力数量必须大于0'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            user.consume_computing_power(amount, reason)
            return Response({
                'message': f'成功为用户 {user.display_name} 扣除 {amount} 算力',
                'current_power': user.computing_power
            })
        except ValueError as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
        except (TypeError):
            return Response(
                {'error': '算力数量必须是有效的数字'},
                status=status.HTTP_400_BAD_REQUEST
            )


@method_decorator(csrf_exempt, name='dispatch')
class LoginView(APIView):
    """用户登录"""
    permission_classes = [permissions.AllowAny]
    throttle_classes = [LoginRateThrottle]
    
    def post(self, request):
        serializer = LoginSerializer(data=request.data)
        if serializer.is_valid():
            user = serializer.validated_data['user']
            login(request, user)

            # 更新最后登录IP
            user.last_login_ip = self.get_client_ip(request)
            user.save(update_fields=['last_login_ip'])

            # 生成JWT token
            refresh = RefreshToken.for_user(user)
            access_token = refresh.access_token

            return Response({
                'message': _('登录成功'),
                'token': str(access_token),
                'refresh': str(refresh),
                'user': UserProfileSerializer(user).data
            })
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    def get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


@method_decorator(csrf_exempt, name='dispatch')
class LogoutView(APIView):
    """用户登出"""
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request):
        logout(request)
        return Response({'message': _('登出成功')})


@method_decorator(csrf_exempt, name='dispatch')
class RegisterView(APIView):
    """用户注册"""
    permission_classes = [permissions.AllowAny]
    throttle_classes = [RegisterRateThrottle]
    
    def post(self, request):
        serializer = RegisterSerializer(data=request.data)
        if serializer.is_valid():
            user = serializer.save()
            return Response({
                'message': _('注册成功'),
                'user': UserProfileSerializer(user).data
            }, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@method_decorator(csrf_exempt, name='dispatch')
class ProfileView(APIView):
    """用户资料"""
    permission_classes = [permissions.AllowAny]
    authentication_classes = []  # 完全禁用认证

    def get(self, request):
        """获取用户资料"""
        # 如果用户已认证，返回真实用户信息
        if request.user and request.user.is_authenticated:
            serializer = UserProfileSerializer(request.user)
            return Response(serializer.data)

        # 如果用户未认证，返回默认的游客信息
        default_user_data = {
            'id': None,
            'username': '135****2363',
            'email': '',
            'first_name': '',
            'last_name': '',
            'phone': '135****2363',
            'avatar': None,
            'computing_power_balance': 1000,
            'package_type': 'free',
            'membership_expire_date': None,
            'is_active': False,
            'date_joined': None,
            'last_login': None
        }
        return Response(default_user_data)

    def put(self, request):
        """更新用户资料"""
        serializer = UserProfileSerializer(request.user, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response({
                'message': _('资料更新成功'),
                'user': serializer.data
            })
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class ChangePasswordView(APIView):
    """修改密码"""
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        old_password = request.data.get('old_password')
        new_password = request.data.get('new_password')

        if not old_password or not new_password:
            return Response(
                {'error': _('请提供旧密码和新密码')},
                status=status.HTTP_400_BAD_REQUEST
            )

        user = request.user
        if not user.check_password(old_password):
            return Response(
                {'error': _('旧密码不正确')},
                status=status.HTTP_400_BAD_REQUEST
            )

        user.set_password(new_password)
        user.save()

        return Response({'message': _('密码修改成功')})


class UploadAvatarView(APIView):
    """上传头像"""
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        avatar_file = request.FILES.get('avatar')
        if not avatar_file:
            return Response(
                {'error': _('请选择头像文件')},
                status=status.HTTP_400_BAD_REQUEST
            )

        # 验证文件类型
        allowed_types = ['image/jpeg', 'image/png', 'image/gif']
        if avatar_file.content_type not in allowed_types:
            return Response(
                {'error': _('只支持JPG、PNG、GIF格式的图片')},
                status=status.HTTP_400_BAD_REQUEST
            )

        # 验证文件大小（5MB）
        if avatar_file.size > 5 * 1024 * 1024:
            return Response(
                {'error': _('头像文件大小不能超过5MB')},
                status=status.HTTP_400_BAD_REQUEST
            )

        user = request.user
        user.avatar = avatar_file
        user.save()

        return Response({
            'message': _('头像上传成功'),
            'avatar_url': user.avatar.url if user.avatar else None
        })


class ComputingPowerView(APIView):
    """算力管理"""
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        """获取用户算力信息"""
        user = request.user
        return Response({
            'computing_power': getattr(user, 'computing_power', 0),
            'total_computing_power': getattr(user, 'total_computing_power', 0),
            'package_type': getattr(user, 'package_type', 'free'),
            'package_expire_date': getattr(user, 'package_expire_date', None)
        })


class ConsumeComputingPowerView(APIView):
    """消耗算力"""
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        amount = request.data.get('amount', 0)
        purpose = request.data.get('purpose', '')

        try:
            amount = int(amount)
            if amount <= 0:
                raise ValueError()
        except (ValueError, TypeError):
            return Response(
                {'error': _('算力数量必须是正整数')},
                status=status.HTTP_400_BAD_REQUEST
            )

        user = request.user
        try:
            user.consume_computing_power(amount, purpose)
            return Response({
                'message': _('算力消耗成功'),
                'remaining_power': user.computing_power
            })
        except ValueError as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )


class AddComputingPowerView(APIView):
    """增加算力"""
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        amount = request.data.get('amount', 0)
        reason = request.data.get('reason', '')

        try:
            amount = int(amount)
            if amount <= 0:
                raise ValueError()
        except (ValueError, TypeError):
            return Response(
                {'error': _('算力数量必须是正整数')},
                status=status.HTTP_400_BAD_REQUEST
            )

        user = request.user
        user.add_computing_power(amount, reason)

        return Response({
            'message': _('算力增加成功'),
            'current_power': user.computing_power
        })


class ComputingPowerLogViewSet(viewsets.ReadOnlyModelViewSet):
    """算力日志视图集"""
    queryset = ComputingPowerLog.objects.all()
    serializer_class = ComputingPowerLogSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['operation_type', 'user']
    search_fields = ['reason', 'user__username', 'user__phone']
    ordering_fields = ['created_at', 'amount']
    ordering = ['-created_at']
    
    def get_queryset(self):
        """普通用户只能查看自己的算力日志"""
        if self.request.user.is_staff:
            return self.queryset
        return self.queryset.filter(user=self.request.user)


class UserPackageViewSet(viewsets.ModelViewSet):
    """用户套餐视图集"""
    queryset = UserPackage.objects.all()
    serializer_class = UserPackageSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['package_type', 'is_active', 'user']
    search_fields = ['user__username', 'user__phone']
    ordering_fields = ['created_at', 'start_date', 'end_date']
    ordering = ['-created_at']
    
    def get_queryset(self):
        """普通用户只能查看自己的套餐记录"""
        if self.request.user.is_staff:
            return self.queryset
        return self.queryset.filter(user=self.request.user)
    
    def get_permissions(self):
        """根据操作类型设置权限"""
        if self.action in ['create', 'update', 'partial_update', 'destroy']:
            permission_classes = [permissions.IsAdminUser]
        else:
            permission_classes = [permissions.IsAuthenticated]
        return [permission() for permission in permission_classes]


class UserStatsView(APIView):
    """用户统计数据API"""
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        """获取用户统计数据"""
        user = request.user

        # 基础统计
        stats = {
            'user_info': {
                'username': user.username,
                'computing_power': getattr(user, 'computing_power', 0),
                'total_computing_power': getattr(user, 'total_computing_power', 0),
                'package_type': getattr(user, 'package_type', 'free'),
                'is_verified': getattr(user, 'is_verified', False),
                'member_since': user.date_joined.strftime('%Y-%m-%d'),
            },
            'usage_stats': {
                'total_videos': 0,
                'total_avatars_used': 0,
                'total_voices_used': 0,
                'total_copywriting': 0,
                'total_computing_consumed': 0,
            },
            'recent_activity': {
                'videos_this_week': 0,
                'videos_this_month': 0,
                'computing_consumed_week': 0,
                'computing_consumed_month': 0,
            }
        }

        # 简化统计计算，避免复杂查询导致的问题
        try:
            # 计算算力消耗统计
            computing_logs = ComputingPowerLog.objects.filter(
                user=user,
                operation_type='consume'
            )

            total_consumed = computing_logs.aggregate(
                total=Sum('amount')
            )['total'] or 0

            stats['usage_stats']['total_computing_consumed'] = total_consumed
        except Exception:
            # 如果查询失败，使用默认值
            pass

        try:
            from apps.avatars.models import AvatarUsageLog
            avatars_used = AvatarUsageLog.objects.filter(user=user).values('avatar').distinct().count()
            stats['usage_stats']['total_avatars_used'] = avatars_used
        except ImportError:
            pass

        try:
            from apps.voices.models import VoiceSynthesisTask
            voices_used = VoiceSynthesisTask.objects.filter(user=user).values('voice_model').distinct().count()
            stats['usage_stats']['total_voices_used'] = voices_used
        except ImportError:
            pass

        try:
            from apps.copywriting.models import Copywriting
            copywriting_count = Copywriting.objects.filter(user=user).count()
            stats['usage_stats']['total_copywriting'] = copywriting_count
        except ImportError:
            pass

        return Response(stats)


class UserUsageHistoryView(APIView):
    """用户使用历史API"""
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        """获取用户使用历史"""
        user = request.user
        page_size = int(request.GET.get('page_size', 20))
        page = int(request.GET.get('page', 1))

        # 获取算力日志
        computing_logs = ComputingPowerLog.objects.filter(user=user).order_by('-created_at')

        # 分页
        start = (page - 1) * page_size
        end = start + page_size
        logs = computing_logs[start:end]

        # 构建历史记录
        history = []
        for log in logs:
            history.append({
                'id': log.id,
                'type': 'computing_power',
                'operation': log.operation_type,
                'amount': log.amount,
                'balance_after': log.balance_after,
                'reason': log.reason,
                'created_at': log.created_at.isoformat(),
            })

        # 尝试获取其他使用记录
        try:
            from apps.videos.models import VideoProduction
            videos = VideoProduction.objects.filter(user=user).order_by('-created_at')[:10]
            for video in videos:
                history.append({
                    'id': f'video_{video.id}',
                    'type': 'video_production',
                    'title': video.title,
                    'status': video.status,
                    'computing_power_consumed': video.computing_power_consumed,
                    'created_at': video.created_at.isoformat(),
                })
        except ImportError:
            pass

        # 按时间排序
        history.sort(key=lambda x: x['created_at'], reverse=True)

        return Response({
            'results': history[:page_size],
            'count': computing_logs.count(),
            'page': page,
            'page_size': page_size,
        })
