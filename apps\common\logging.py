"""
结构化日志系统
"""
import json
import logging
import traceback
from datetime import datetime
from django.conf import settings
from django.utils import timezone
from django.contrib.auth import get_user_model

User = get_user_model()


class StructuredFormatter(logging.Formatter):
    """结构化日志格式化器"""
    
    def format(self, record):
        """格式化日志记录"""
        log_data = {
            'timestamp': datetime.utcnow().isoformat() + 'Z',
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno,
        }
        
        # 添加额外的上下文信息
        if hasattr(record, 'request_id'):
            log_data['request_id'] = record.request_id
        
        if hasattr(record, 'user_id'):
            log_data['user_id'] = record.user_id
        
        if hasattr(record, 'ip_address'):
            log_data['ip_address'] = record.ip_address
        
        if hasattr(record, 'user_agent'):
            log_data['user_agent'] = record.user_agent
        
        if hasattr(record, 'url'):
            log_data['url'] = record.url
        
        if hasattr(record, 'method'):
            log_data['method'] = record.method
        
        if hasattr(record, 'status_code'):
            log_data['status_code'] = record.status_code
        
        if hasattr(record, 'duration'):
            log_data['duration'] = record.duration
        
        # 添加异常信息
        if record.exc_info:
            log_data['exception'] = {
                'type': record.exc_info[0].__name__,
                'message': str(record.exc_info[1]),
                'traceback': traceback.format_exception(*record.exc_info)
            }
        
        # 添加额外字段
        if hasattr(record, 'extra_data'):
            log_data.update(record.extra_data)
        
        return json.dumps(log_data, ensure_ascii=False)


class DatabaseLogHandler(logging.Handler):
    """数据库日志处理器"""
    
    def emit(self, record):
        """发送日志记录到数据库"""
        try:
            from .models import SystemLog
            
            # 创建日志记录
            log_entry = SystemLog(
                level=record.levelname,
                logger=record.name,
                message=record.getMessage(),
                module=record.module,
                function=record.funcName,
                line_number=record.lineno,
                timestamp=timezone.now()
            )
            
            # 添加请求信息
            if hasattr(record, 'request_id'):
                log_entry.request_id = record.request_id
            
            if hasattr(record, 'user_id'):
                try:
                    log_entry.user = User.objects.get(id=record.user_id)
                except User.DoesNotExist:
                    pass
            
            if hasattr(record, 'ip_address'):
                log_entry.ip_address = record.ip_address
            
            if hasattr(record, 'url'):
                log_entry.url = record.url
            
            if hasattr(record, 'method'):
                log_entry.method = record.method
            
            if hasattr(record, 'status_code'):
                log_entry.status_code = record.status_code
            
            # 添加异常信息
            if record.exc_info:
                log_entry.exception_type = record.exc_info[0].__name__
                log_entry.exception_message = str(record.exc_info[1])
                log_entry.traceback = ''.join(traceback.format_exception(*record.exc_info))
            
            # 添加额外数据
            if hasattr(record, 'extra_data'):
                log_entry.extra_data = record.extra_data
            
            log_entry.save()
            
        except Exception as e:
            # 避免日志记录失败导致应用崩溃
            print(f"Failed to save log to database: {e}")


class LoggerMixin:
    """日志记录混入类"""
    
    def get_logger(self):
        """获取日志记录器"""
        return logging.getLogger(self.__class__.__module__)
    
    def log_info(self, message, **kwargs):
        """记录信息日志"""
        logger = self.get_logger()
        logger.info(message, extra=kwargs)
    
    def log_warning(self, message, **kwargs):
        """记录警告日志"""
        logger = self.get_logger()
        logger.warning(message, extra=kwargs)
    
    def log_error(self, message, exception=None, **kwargs):
        """记录错误日志"""
        logger = self.get_logger()
        if exception:
            logger.error(message, exc_info=exception, extra=kwargs)
        else:
            logger.error(message, extra=kwargs)
    
    def log_debug(self, message, **kwargs):
        """记录调试日志"""
        logger = self.get_logger()
        logger.debug(message, extra=kwargs)


class RequestLoggerMiddleware:
    """请求日志中间件"""
    
    def __init__(self, get_response):
        self.get_response = get_response
        self.logger = logging.getLogger('django.request')
    
    def __call__(self, request):
        import time
        import uuid
        
        # 生成请求ID
        request.id = str(uuid.uuid4())
        start_time = time.time()
        
        # 记录请求开始
        self.logger.info(
            f"Request started: {request.method} {request.path}",
            extra={
                'request_id': request.id,
                'method': request.method,
                'url': request.build_absolute_uri(),
                'ip_address': self.get_client_ip(request),
                'user_agent': request.META.get('HTTP_USER_AGENT', ''),
                'user_id': request.user.id if hasattr(request, 'user') and request.user.is_authenticated else None,
            }
        )
        
        response = self.get_response(request)
        
        # 计算请求时间
        duration = time.time() - start_time
        
        # 记录请求完成
        self.logger.info(
            f"Request completed: {response.status_code} - {duration:.3f}s",
            extra={
                'request_id': request.id,
                'status_code': response.status_code,
                'duration': duration,
                'content_length': len(response.content) if hasattr(response, 'content') else 0,
            }
        )
        
        return response
    
    def get_client_ip(self, request):
        """获取客户端IP"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class APILoggerMixin:
    """API日志记录混入"""
    
    def log_api_call(self, request, action, **kwargs):
        """记录API调用"""
        logger = logging.getLogger('api')
        
        extra_data = {
            'request_id': getattr(request, 'id', None),
            'user_id': request.user.id if request.user.is_authenticated else None,
            'ip_address': self.get_client_ip(request),
            'action': action,
            'endpoint': request.path,
            'method': request.method,
        }
        extra_data.update(kwargs)
        
        logger.info(f"API call: {action}", extra=extra_data)
    
    def log_api_error(self, request, action, error, **kwargs):
        """记录API错误"""
        logger = logging.getLogger('api.error')
        
        extra_data = {
            'request_id': getattr(request, 'id', None),
            'user_id': request.user.id if request.user.is_authenticated else None,
            'ip_address': self.get_client_ip(request),
            'action': action,
            'endpoint': request.path,
            'method': request.method,
            'error_type': error.__class__.__name__,
            'error_message': str(error),
        }
        extra_data.update(kwargs)
        
        logger.error(f"API error: {action}", exc_info=error, extra=extra_data)
    
    def get_client_ip(self, request):
        """获取客户端IP"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class BusinessLoggerMixin:
    """业务日志记录混入"""
    
    def log_business_event(self, event_type, description, user=None, **kwargs):
        """记录业务事件"""
        logger = logging.getLogger('business')
        
        extra_data = {
            'event_type': event_type,
            'user_id': user.id if user else None,
            'extra_data': kwargs
        }
        
        logger.info(f"Business event: {description}", extra=extra_data)
    
    def log_user_action(self, user, action, description, **kwargs):
        """记录用户操作"""
        logger = logging.getLogger('user_action')
        
        extra_data = {
            'user_id': user.id,
            'username': user.username,
            'action': action,
            'extra_data': kwargs
        }
        
        logger.info(f"User action: {description}", extra=extra_data)
    
    def log_system_event(self, event_type, description, **kwargs):
        """记录系统事件"""
        logger = logging.getLogger('system')
        
        extra_data = {
            'event_type': event_type,
            'extra_data': kwargs
        }
        
        logger.info(f"System event: {description}", extra=extra_data)


class SecurityLoggerMixin:
    """安全日志记录混入"""
    
    def log_security_event(self, event_type, description, request=None, user=None, **kwargs):
        """记录安全事件"""
        logger = logging.getLogger('security')
        
        extra_data = {
            'event_type': event_type,
            'user_id': user.id if user else None,
            'ip_address': self.get_client_ip(request) if request else None,
            'user_agent': request.META.get('HTTP_USER_AGENT', '') if request else '',
            'extra_data': kwargs
        }
        
        logger.warning(f"Security event: {description}", extra=extra_data)
    
    def log_login_attempt(self, request, username, success=True, **kwargs):
        """记录登录尝试"""
        event_type = 'login_success' if success else 'login_failure'
        description = f"Login {'successful' if success else 'failed'} for user: {username}"
        
        extra_data = {
            'username': username,
            'success': success,
        }
        extra_data.update(kwargs)
        
        self.log_security_event(event_type, description, request, **extra_data)
    
    def log_permission_denied(self, request, resource, action, **kwargs):
        """记录权限拒绝"""
        description = f"Permission denied: {action} on {resource}"
        
        extra_data = {
            'resource': resource,
            'action': action,
        }
        extra_data.update(kwargs)
        
        self.log_security_event('permission_denied', description, request, **extra_data)
    
    def get_client_ip(self, request):
        """获取客户端IP"""
        if not request:
            return None
        
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


# 便捷的日志记录函数
def log_info(message, logger_name='app', **kwargs):
    """记录信息日志"""
    logger = logging.getLogger(logger_name)
    logger.info(message, extra=kwargs)


def log_warning(message, logger_name='app', **kwargs):
    """记录警告日志"""
    logger = logging.getLogger(logger_name)
    logger.warning(message, extra=kwargs)


def log_error(message, exception=None, logger_name='app', **kwargs):
    """记录错误日志"""
    logger = logging.getLogger(logger_name)
    if exception:
        logger.error(message, exc_info=exception, extra=kwargs)
    else:
        logger.error(message, extra=kwargs)


def log_debug(message, logger_name='app', **kwargs):
    """记录调试日志"""
    logger = logging.getLogger(logger_name)
    logger.debug(message, extra=kwargs)
