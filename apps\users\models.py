"""
用户模型
基于对https://hm.umi6.com/平台的逆向工程分析
"""
from django.contrib.auth.models import AbstractUser
from django.db import models
from django.utils.translation import gettext_lazy as _


class User(AbstractUser):
    """
    自定义用户模型
    扩展Django默认用户模型以支持AI数字人平台功能
    """
    phone = models.CharField(
        _('手机号'),
        max_length=20,
        unique=True,
        null=True,
        blank=True,
        help_text=_('用户手机号码')
    )
    
    avatar = models.ImageField(
        _('头像'),
        upload_to='avatars/users/',
        null=True,
        blank=True,
        help_text=_('用户头像图片')
    )
    
    computing_power = models.PositiveIntegerField(
        _('算力'),
        default=0,
        help_text=_('用户当前可用算力点数')
    )
    
    total_computing_power = models.PositiveIntegerField(
        _('总算力'),
        default=0,
        help_text=_('用户累计获得的算力点数')
    )
    
    package_type = models.CharField(
        _('套餐类型'),
        max_length=50,
        default='free',
        choices=[
            ('free', _('免费版')),
            ('basic', _('基础版')),
            ('pro', _('专业版')),
            ('enterprise', _('企业版')),
        ],
        help_text=_('用户当前套餐类型')
    )
    
    package_expire_date = models.DateTimeField(
        _('套餐到期时间'),
        null=True,
        blank=True,
        help_text=_('付费套餐的到期时间')
    )
    
    is_verified = models.BooleanField(
        _('已验证'),
        default=False,
        help_text=_('用户是否已通过手机验证')
    )
    
    last_login_ip = models.GenericIPAddressField(
        _('最后登录IP'),
        null=True,
        blank=True,
        help_text=_('用户最后一次登录的IP地址')
    )
    
    created_at = models.DateTimeField(
        _('创建时间'),
        auto_now_add=True
    )
    
    updated_at = models.DateTimeField(
        _('更新时间'),
        auto_now=True
    )

    class Meta:
        verbose_name = _('用户')
        verbose_name_plural = _('用户')
        db_table = 'users'
        ordering = ['-created_at']

    def __str__(self):
        return self.username or self.phone or self.email

    @property
    def display_name(self):
        """获取用户显示名称"""
        return self.get_full_name() or self.username or self.phone

    def add_computing_power(self, amount, reason=''):
        """增加算力"""
        self.computing_power += amount
        self.total_computing_power += amount
        self.save(update_fields=['computing_power', 'total_computing_power'])
        
        # 记录算力变动日志
        ComputingPowerLog.objects.create(
            user=self,
            amount=amount,
            balance_after=self.computing_power,
            operation_type='add',
            reason=reason
        )

    def consume_computing_power(self, amount, reason=''):
        """消耗算力"""
        if self.computing_power < amount:
            raise ValueError(_('算力不足'))
        
        self.computing_power -= amount
        self.save(update_fields=['computing_power'])
        
        # 记录算力变动日志
        ComputingPowerLog.objects.create(
            user=self,
            amount=-amount,
            balance_after=self.computing_power,
            operation_type='consume',
            reason=reason
        )


class ComputingPowerLog(models.Model):
    """算力变动日志"""
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='computing_power_logs',
        verbose_name=_('用户')
    )
    
    amount = models.IntegerField(
        _('变动数量'),
        help_text=_('正数表示增加，负数表示消耗')
    )
    
    balance_after = models.PositiveIntegerField(
        _('变动后余额'),
        help_text=_('操作后的算力余额')
    )
    
    operation_type = models.CharField(
        _('操作类型'),
        max_length=20,
        choices=[
            ('add', _('增加')),
            ('consume', _('消耗')),
            ('refund', _('退还')),
        ]
    )
    
    reason = models.CharField(
        _('变动原因'),
        max_length=200,
        blank=True,
        help_text=_('算力变动的具体原因')
    )
    
    created_at = models.DateTimeField(
        _('创建时间'),
        auto_now_add=True
    )

    class Meta:
        verbose_name = _('算力日志')
        verbose_name_plural = _('算力日志')
        db_table = 'computing_power_logs'
        ordering = ['-created_at']

    def __str__(self):
        return f'{self.user.display_name} {self.operation_type} {abs(self.amount)} 算力'


class UserPackage(models.Model):
    """用户套餐记录"""
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='package_records',
        verbose_name=_('用户')
    )
    
    package_type = models.CharField(
        _('套餐类型'),
        max_length=50,
        choices=[
            ('basic', _('基础版')),
            ('pro', _('专业版')),
            ('enterprise', _('企业版')),
        ]
    )
    
    duration_days = models.PositiveIntegerField(
        _('套餐天数'),
        help_text=_('套餐有效期天数')
    )
    
    computing_power_included = models.PositiveIntegerField(
        _('包含算力'),
        help_text=_('套餐包含的算力点数')
    )
    
    price = models.DecimalField(
        _('价格'),
        max_digits=10,
        decimal_places=2,
        help_text=_('套餐价格（元）')
    )
    
    start_date = models.DateTimeField(
        _('开始时间'),
        help_text=_('套餐生效时间')
    )
    
    end_date = models.DateTimeField(
        _('结束时间'),
        help_text=_('套餐到期时间')
    )
    
    is_active = models.BooleanField(
        _('是否激活'),
        default=True,
        help_text=_('套餐是否处于激活状态')
    )
    
    created_at = models.DateTimeField(
        _('创建时间'),
        auto_now_add=True
    )

    class Meta:
        verbose_name = _('用户套餐')
        verbose_name_plural = _('用户套餐')
        db_table = 'user_packages'
        ordering = ['-created_at']

    def __str__(self):
        return f'{self.user.display_name} - {self.get_package_type_display()}'
