"""
自定义异常处理
"""
import logging
from rest_framework.views import exception_handler
from rest_framework.response import Response
from rest_framework import status
from django.http import Http404
from django.core.exceptions import PermissionDenied, ValidationError
from django.db import IntegrityError

logger = logging.getLogger(__name__)


def custom_exception_handler(exc, context):
    """自定义异常处理器"""
    
    # 获取标准的异常响应
    response = exception_handler(exc, context)
    
    # 获取请求信息
    request = context.get('request')
    view = context.get('view')
    
    # 记录异常信息
    if request:
        logger.error(
            f"API异常: {exc.__class__.__name__}: {str(exc)} "
            f"- 用户: {getattr(request.user, 'username', 'Anonymous')} "
            f"- 路径: {request.path} "
            f"- 方法: {request.method} "
            f"- 视图: {view.__class__.__name__ if view else 'Unknown'}"
        )
    
    # 如果是标准的DRF异常，直接返回
    if response is not None:
        custom_response_data = {
            'success': False,
            'error': True,
            'message': '请求处理失败',
            'code': 'UNKNOWN_ERROR',
            'details': response.data,
            'status_code': response.status_code,
            'timestamp': None,
            'path': request.path if request else None,
            'method': request.method if request else None,
            'request_id': getattr(request, 'id', None) if request else None
        }

        # 添加时间戳
        from django.utils import timezone
        custom_response_data['timestamp'] = timezone.now().isoformat()

        # 根据异常类型自定义消息和代码
        from rest_framework.exceptions import (
            ValidationError as DRFValidationError,
            AuthenticationFailed, PermissionDenied as DRFPermissionDenied,
            NotFound, MethodNotAllowed, Throttled, ParseError
        )

        if isinstance(exc, DRFValidationError):
            custom_response_data['message'] = '数据验证失败'
            custom_response_data['code'] = 'VALIDATION_ERROR'
            if isinstance(exc.detail, dict):
                custom_response_data['field_errors'] = exc.detail
            elif isinstance(exc.detail, list):
                custom_response_data['message'] = exc.detail[0] if exc.detail else '数据验证失败'
            else:
                custom_response_data['message'] = str(exc.detail)

        elif isinstance(exc, AuthenticationFailed):
            custom_response_data['message'] = '身份认证失败'
            custom_response_data['code'] = 'AUTHENTICATION_FAILED'

        elif isinstance(exc, DRFPermissionDenied):
            custom_response_data['message'] = '权限不足'
            custom_response_data['code'] = 'PERMISSION_DENIED'

        elif isinstance(exc, NotFound):
            custom_response_data['message'] = '请求的资源不存在'
            custom_response_data['code'] = 'NOT_FOUND'

        elif isinstance(exc, MethodNotAllowed):
            custom_response_data['message'] = '请求方法不被允许'
            custom_response_data['code'] = 'METHOD_NOT_ALLOWED'
            custom_response_data['allowed_methods'] = getattr(exc, 'allowed_methods', [])

        elif isinstance(exc, Throttled):
            custom_response_data['message'] = '请求过于频繁，请稍后再试'
            custom_response_data['code'] = 'THROTTLED'
            if hasattr(exc, 'wait') and exc.wait:
                custom_response_data['retry_after'] = exc.wait

        elif isinstance(exc, ParseError):
            custom_response_data['message'] = '请求数据解析失败'
            custom_response_data['code'] = 'PARSE_ERROR'

        elif hasattr(exc, 'detail'):
            if isinstance(exc.detail, dict):
                custom_response_data['message'] = '数据验证失败'
                custom_response_data['field_errors'] = exc.detail
            elif isinstance(exc.detail, list):
                custom_response_data['message'] = exc.detail[0] if exc.detail else '请求处理失败'
            else:
                custom_response_data['message'] = str(exc.detail)

        response.data = custom_response_data
        return response
    
    # 处理Django原生异常
    if isinstance(exc, Http404):
        return Response({
            'error': True,
            'message': '请求的资源不存在',
            'status_code': 404
        }, status=status.HTTP_404_NOT_FOUND)
    
    elif isinstance(exc, PermissionDenied):
        return Response({
            'error': True,
            'message': '权限不足',
            'status_code': 403
        }, status=status.HTTP_403_FORBIDDEN)
    
    elif isinstance(exc, ValidationError):
        return Response({
            'error': True,
            'message': '数据验证失败',
            'details': exc.message_dict if hasattr(exc, 'message_dict') else str(exc),
            'status_code': 400
        }, status=status.HTTP_400_BAD_REQUEST)
    
    elif isinstance(exc, IntegrityError):
        return Response({
            'error': True,
            'message': '数据完整性错误，可能存在重复数据',
            'status_code': 400
        }, status=status.HTTP_400_BAD_REQUEST)
    
    # 处理其他未捕获的异常
    logger.critical(
        f"未处理的异常: {exc.__class__.__name__}: {str(exc)}",
        exc_info=True
    )
    
    return Response({
        'error': True,
        'message': '服务器内部错误，请稍后重试',
        'status_code': 500
    }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class BusinessException(Exception):
    """业务异常基类"""
    
    def __init__(self, message, code=None, details=None):
        self.message = message
        self.code = code or 'BUSINESS_ERROR'
        self.details = details or {}
        super().__init__(self.message)


class ValidationException(BusinessException):
    """验证异常"""
    
    def __init__(self, message, field=None, details=None):
        self.field = field
        super().__init__(message, 'VALIDATION_ERROR', details)


class AuthenticationException(BusinessException):
    """认证异常"""
    
    def __init__(self, message='认证失败'):
        super().__init__(message, 'AUTHENTICATION_ERROR')


class AuthorizationException(BusinessException):
    """授权异常"""
    
    def __init__(self, message='权限不足'):
        super().__init__(message, 'AUTHORIZATION_ERROR')


class ResourceNotFoundException(BusinessException):
    """资源不存在异常"""
    
    def __init__(self, resource_type='资源', resource_id=None):
        message = f'{resource_type}不存在'
        if resource_id:
            message += f' (ID: {resource_id})'
        super().__init__(message, 'RESOURCE_NOT_FOUND')


class ResourceConflictException(BusinessException):
    """资源冲突异常"""
    
    def __init__(self, message='资源冲突'):
        super().__init__(message, 'RESOURCE_CONFLICT')


class ServiceUnavailableException(BusinessException):
    """服务不可用异常"""
    
    def __init__(self, service_name='服务'):
        message = f'{service_name}暂时不可用，请稍后重试'
        super().__init__(message, 'SERVICE_UNAVAILABLE')


class RateLimitException(BusinessException):
    """频率限制异常"""
    
    def __init__(self, message='请求过于频繁，请稍后重试'):
        super().__init__(message, 'RATE_LIMIT_EXCEEDED')


class InsufficientResourceException(BusinessException):
    """资源不足异常"""
    
    def __init__(self, resource_type='资源'):
        message = f'{resource_type}不足'
        super().__init__(message, 'INSUFFICIENT_RESOURCE')


# 异常处理装饰器
def handle_exceptions(func):
    """异常处理装饰器"""
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except BusinessException as e:
            logger.warning(f"业务异常: {e.code}: {e.message}")
            raise
        except Exception as e:
            logger.error(f"未预期异常: {e.__class__.__name__}: {str(e)}", exc_info=True)
            raise BusinessException('系统错误，请稍后重试')
    
    return wrapper


# 异常上下文管理器
class ExceptionContext:
    """异常上下文管理器"""
    
    def __init__(self, operation_name, user=None):
        self.operation_name = operation_name
        self.user = user
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if exc_type is not None:
            user_info = f"用户: {self.user.username}" if self.user else "匿名用户"
            logger.error(
                f"操作失败 - {self.operation_name} - {user_info} - "
                f"{exc_type.__name__}: {str(exc_val)}",
                exc_info=True
            )
        return False  # 不抑制异常
