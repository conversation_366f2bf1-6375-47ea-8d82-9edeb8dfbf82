"""
OpenAI API集成服务 - 干净版本
"""
import logging
from typing import Dict, Any, Optional, List
from django.conf import settings
from apps.config.services import api_config

logger = logging.getLogger(__name__)


class OpenAIService:
    """OpenAI API服务"""

    def __init__(self):
        self.client = None
        self._initialize_client()

    def _initialize_client(self):
        """初始化OpenAI客户端"""
        try:
            # 重置OpenAI模块状态
            import openai
            openai.api_key = None
            openai.organization = None
            openai.base_url = None
            openai.http_client = None
            openai.api_type = None
            openai.api_version = None
            
            config = api_config.get_openai_config()

            if not config['api_key']:
                logger.warning("OpenAI API密钥未配置")
                return

            # 使用最干净的初始化方式
            try:
                self.client = openai.OpenAI(api_key=config['api_key'])
                logger.info("OpenAI客户端初始化成功")
            except Exception as e:
                logger.error(f"OpenAI客户端初始化失败: {e}")
                self.client = None

        except Exception as e:
            logger.error(f"OpenAI客户端初始化过程失败: {e}")
            self.client = None

    def is_available(self) -> bool:
        """检查服务是否可用"""
        return self.client is not None

    def chat_completion(
        self, 
        messages: List[Dict[str, str]], 
        model: Optional[str] = None,
        max_tokens: Optional[int] = None,
        temperature: float = 0.7,
        **kwargs
    ) -> Dict[str, Any]:
        """
        聊天完成API
        """
        if not self.is_available():
            return {
                'success': False,
                'error': 'OpenAI服务不可用',
                'is_fallback': True,
                'service_status': 'unavailable'
            }
        
        try:
            config = api_config.get_openai_config()
            
            if model is None:
                model = config.get('model', 'gpt-3.5-turbo')
            if max_tokens is None:
                max_tokens = config.get('max_tokens', 2000)

            response = self.client.chat.completions.create(
                model=model,
                messages=messages,
                max_tokens=max_tokens,
                temperature=temperature,
                **kwargs
            )
            
            return {
                'success': True,
                'response': response.choices[0].message.content,
                'usage': dict(response.usage),
                'model': response.model,
                'is_fallback': False,
                'service_status': 'active'
            }
            
        except Exception as e:
            logger.error(f"OpenAI聊天完成失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'is_fallback': True,
                'service_status': 'error'
            }

    def generate_image(
        self, 
        prompt: str, 
        size: str = "1024x1024",
        quality: str = "standard",
        n: int = 1,
        **kwargs
    ) -> Dict[str, Any]:
        """
        图像生成API
        """
        if not self.is_available():
            return {
                'success': False,
                'error': 'OpenAI服务不可用',
                'is_fallback': True,
                'service_status': 'unavailable'
            }
        
        try:
            response = self.client.images.generate(
                model="dall-e-3",
                prompt=prompt,
                size=size,
                quality=quality,
                n=n,
                **kwargs
            )
            
            return {
                'success': True,
                'image_url': response.data[0].url,
                'revised_prompt': response.data[0].revised_prompt,
                'is_fallback': False,
                'service_status': 'active'
            }
            
        except Exception as e:
            logger.error(f"OpenAI图像生成失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'is_fallback': True,
                'service_status': 'error'
            }


# 创建全局实例
openai_service = OpenAIService()


def get_openai_service() -> OpenAIService:
    """获取OpenAI服务实例"""
    return openai_service


# 便捷函数
def chat_with_ai(message: str, context: List[Dict[str, str]] = None) -> Dict[str, Any]:
    """AI对话便捷函数"""
    messages = context or []
    messages.append({"role": "user", "content": message})
    
    return openai_service.chat_completion(messages)


def generate_ai_image(prompt: str, size: str = "1024x1024") -> Dict[str, Any]:
    """AI图像生成便捷函数"""
    return openai_service.generate_image(prompt, size)
