# Generated by Django 5.2.1 on 2025-07-16 11:32

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("packages", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="PackageFeature",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        help_text="功能特性的名称", max_length=100, verbose_name="功能名称"
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        blank=True, help_text="功能特性的详细描述", verbose_name="功能描述"
                    ),
                ),
                (
                    "feature_type",
                    models.CharField(
                        choices=[
                            ("basic", "基础功能"),
                            ("advanced", "高级功能"),
                            ("premium", "专业功能"),
                            ("enterprise", "企业功能"),
                            ("limit", "限制说明"),
                            ("support", "支持服务"),
                        ],
                        default="basic",
                        help_text="功能特性的类型",
                        max_length=20,
                        verbose_name="功能类型",
                    ),
                ),
                (
                    "icon",
                    models.CharField(
                        blank=True,
                        help_text="Material Icons图标名称",
                        max_length=50,
                        verbose_name="图标",
                    ),
                ),
                (
                    "is_highlight",
                    models.BooleanField(
                        default=False,
                        help_text="是否在套餐介绍中突出显示此功能",
                        verbose_name="是否突出显示",
                    ),
                ),
                (
                    "sort_order",
                    models.PositiveIntegerField(
                        default=0, help_text="显示排序，数字越小越靠前", verbose_name="排序"
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True, help_text="是否启用此功能特性", verbose_name="是否启用"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
            ],
            options={
                "verbose_name": "套餐功能",
                "verbose_name_plural": "套餐功能",
                "db_table": "package_features",
                "ordering": ["sort_order", "name"],
                "indexes": [
                    models.Index(
                        fields=["feature_type", "is_active"],
                        name="package_fea_feature_9bec47_idx",
                    ),
                    models.Index(
                        fields=["is_highlight"], name="package_fea_is_high_36cd7f_idx"
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="PackageFeatureRelation",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "is_included",
                    models.BooleanField(
                        default=True, help_text="此套餐是否包含该功能", verbose_name="是否包含"
                    ),
                ),
                (
                    "limit_value",
                    models.CharField(
                        blank=True,
                        help_text='功能的限制值，如"每月100次"、"无限制"等',
                        max_length=100,
                        verbose_name="限制值",
                    ),
                ),
                (
                    "custom_description",
                    models.TextField(
                        blank=True, help_text="针对此套餐的功能自定义描述", verbose_name="自定义描述"
                    ),
                ),
                (
                    "sort_order",
                    models.PositiveIntegerField(
                        default=0, help_text="在套餐中的显示排序", verbose_name="排序"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "feature",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="package_relations",
                        to="packages.packagefeature",
                        verbose_name="功能",
                    ),
                ),
                (
                    "package",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="feature_relations",
                        to="packages.package",
                        verbose_name="套餐",
                    ),
                ),
            ],
            options={
                "verbose_name": "套餐功能关联",
                "verbose_name_plural": "套餐功能关联",
                "db_table": "package_feature_relations",
                "ordering": ["sort_order", "feature__sort_order"],
                "unique_together": {("package", "feature")},
            },
        ),
    ]
