"""
错误处理中间件
"""
import uuid
import time
import logging
import traceback
from django.http import JsonResponse
from django.conf import settings
from django.utils.deprecation import MiddlewareMixin
from django.core.exceptions import SuspiciousOperation
from apps.common.exceptions import BusinessException

logger = logging.getLogger(__name__)


class ErrorHandlingMiddleware(MiddlewareMixin):
    """错误处理中间件"""
    
    def process_request(self, request):
        """处理请求"""
        # 为每个请求生成唯一ID
        request.id = str(uuid.uuid4())
        request.start_time = time.time()
        
        # 记录请求信息
        logger.info(f'请求开始: {request.method} {request.path}', extra={
            'request_id': request.id,
            'method': request.method,
            'path': request.path,
            'user': str(request.user) if hasattr(request, 'user') else 'Anonymous',
            'ip': self.get_client_ip(request),
            'user_agent': request.META.get('HTTP_USER_AGENT', ''),
        })
        
        return None
    
    def process_response(self, request, response):
        """处理响应"""
        if hasattr(request, 'start_time'):
            duration = time.time() - request.start_time
            
            # 记录响应信息
            logger.info(f'请求完成: {response.status_code} - {duration:.3f}s', extra={
                'request_id': getattr(request, 'id', 'unknown'),
                'status_code': response.status_code,
                'duration': duration,
                'content_length': len(response.content) if hasattr(response, 'content') else 0,
            })
        
        return response
    
    def process_exception(self, request, exception):
        """处理异常"""
        request_id = getattr(request, 'id', 'unknown')
        
        # 记录异常详情
        logger.error(f'请求异常: {exception.__class__.__name__}: {str(exception)}', extra={
            'request_id': request_id,
            'exception_type': exception.__class__.__name__,
            'exception_message': str(exception),
            'path': request.path,
            'method': request.method,
            'user': str(request.user) if hasattr(request, 'user') else 'Anonymous',
            'ip': self.get_client_ip(request),
            'traceback': traceback.format_exc(),
        })
        
        # 对于API请求，返回JSON错误响应
        if request.path.startswith('/api/'):
            return self.create_error_response(request, exception)
        
        # 对于非API请求，让Django默认处理
        return None
    
    def create_error_response(self, request, exception):
        """创建错误响应"""
        from django.utils import timezone
        
        error_data = {
            'success': False,
            'error': True,
            'message': '服务器内部错误',
            'code': 'INTERNAL_SERVER_ERROR',
            'status_code': 500,
            'timestamp': timezone.now().isoformat(),
            'path': request.path,
            'method': request.method,
            'request_id': getattr(request, 'id', None),
        }
        
        # 根据异常类型设置响应
        if isinstance(exception, BusinessException):
            error_data.update({
                'message': exception.message,
                'code': exception.code,
                'status_code': getattr(exception, 'status_code', 400),
                'details': getattr(exception, 'details', None),
            })
            status_code = getattr(exception, 'status_code', 400)
        
        elif isinstance(exception, SuspiciousOperation):
            error_data.update({
                'message': '可疑操作',
                'code': 'SUSPICIOUS_OPERATION',
                'status_code': 400,
            })
            status_code = 400
        
        else:
            # 未知异常
            if settings.DEBUG:
                error_data['debug_message'] = str(exception)
                error_data['traceback'] = traceback.format_exc()
            
            status_code = 500
        
        return JsonResponse(error_data, status=status_code)
    
    def get_client_ip(self, request):
        """获取客户端IP"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class RequestLoggingMiddleware(MiddlewareMixin):
    """请求日志中间件"""
    
    def process_request(self, request):
        """记录请求详情"""
        # 只记录API请求
        if not request.path.startswith('/api/'):
            return None
        
        # 过滤敏感信息
        filtered_data = self.filter_sensitive_data(request)
        
        logger.info('API请求详情', extra={
            'request_id': getattr(request, 'id', 'unknown'),
            'method': request.method,
            'path': request.path,
            'query_params': dict(request.GET),
            'content_type': request.content_type,
            'body_size': len(request.body) if hasattr(request, 'body') else 0,
            'headers': self.get_safe_headers(request),
            'user': str(request.user) if hasattr(request, 'user') else 'Anonymous',
        })
        
        return None
    
    def filter_sensitive_data(self, request):
        """过滤敏感数据"""
        sensitive_fields = [
            'password', 'token', 'secret', 'key', 'authorization',
            'csrf', 'session', 'cookie'
        ]
        
        filtered_data = {}
        
        # 过滤POST数据
        if hasattr(request, 'POST') and request.POST:
            for key, value in request.POST.items():
                if any(field in key.lower() for field in sensitive_fields):
                    filtered_data[key] = '***'
                else:
                    filtered_data[key] = value
        
        return filtered_data
    
    def get_safe_headers(self, request):
        """获取安全的请求头"""
        safe_headers = {}
        sensitive_headers = [
            'authorization', 'cookie', 'x-csrftoken', 'x-api-key'
        ]
        
        for key, value in request.META.items():
            if key.startswith('HTTP_'):
                header_name = key[5:].lower().replace('_', '-')
                if any(sensitive in header_name for sensitive in sensitive_headers):
                    safe_headers[header_name] = '***'
                else:
                    safe_headers[header_name] = value
        
        return safe_headers


class SecurityMiddleware(MiddlewareMixin):
    """安全中间件"""
    
    def process_request(self, request):
        """安全检查"""
        # 检查请求大小
        if hasattr(request, 'body') and len(request.body) > 50 * 1024 * 1024:  # 50MB
            logger.warning(f'请求体过大: {len(request.body)} bytes', extra={
                'request_id': getattr(request, 'id', 'unknown'),
                'path': request.path,
                'ip': self.get_client_ip(request),
            })
        
        # 检查可疑路径
        suspicious_patterns = [
            '..', '/etc/', '/proc/', 'cmd.exe', 'powershell',
            '<script', 'javascript:', 'vbscript:', 'onload=',
        ]
        
        for pattern in suspicious_patterns:
            if pattern in request.path.lower():
                logger.warning(f'可疑请求路径: {request.path}', extra={
                    'request_id': getattr(request, 'id', 'unknown'),
                    'pattern': pattern,
                    'ip': self.get_client_ip(request),
                })
                break
        
        return None
    
    def process_response(self, request, response):
        """添加安全头"""
        # 添加安全响应头
        response['X-Content-Type-Options'] = 'nosniff'
        response['X-Frame-Options'] = 'DENY'
        response['X-XSS-Protection'] = '1; mode=block'
        response['Referrer-Policy'] = 'strict-origin-when-cross-origin'
        
        # 对于API响应，添加额外的安全头
        if request.path.startswith('/api/'):
            response['Cache-Control'] = 'no-cache, no-store, must-revalidate'
            response['Pragma'] = 'no-cache'
            response['Expires'] = '0'
        
        return response
    
    def get_client_ip(self, request):
        """获取客户端IP"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class PerformanceMiddleware(MiddlewareMixin):
    """性能监控中间件"""
    
    def process_request(self, request):
        """开始性能监控"""
        request.performance_start = time.time()
        return None
    
    def process_response(self, request, response):
        """记录性能数据"""
        if hasattr(request, 'performance_start'):
            duration = time.time() - request.performance_start
            
            # 记录慢请求
            if duration > 2.0:  # 超过2秒的请求
                logger.warning(f'慢请求: {duration:.3f}s', extra={
                    'request_id': getattr(request, 'id', 'unknown'),
                    'method': request.method,
                    'path': request.path,
                    'duration': duration,
                    'status_code': response.status_code,
                })
            
            # 添加性能头
            response['X-Response-Time'] = f'{duration:.3f}s'
        
        return response
