from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import SearchFilter, OrderingFilter
from django.db.models import Count, Sum, Avg
from .models import DigitalWork, WorkTemplate
from .serializers import (
    DigitalWorkSerializer, DigitalWorkCreateSerializer,
    WorkTemplateSerializer, WorkStatsSerializer
)


class DigitalWorkViewSet(viewsets.ModelViewSet):
    """数字人作品视图集"""
    serializer_class = DigitalWorkSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['status', 'avatar_name', 'voice_name']
    search_fields = ['name', 'description', 'script_content']
    ordering_fields = ['created_at', 'updated_at', 'completed_at', 'duration']
    ordering = ['-created_at']

    def get_queryset(self):
        return DigitalWork.objects.filter(user=self.request.user)

    def get_serializer_class(self):
        if self.action == 'create':
            return DigitalWorkCreateSerializer
        return DigitalWorkSerializer

    @action(detail=False, methods=['get'])
    def statistics(self, request):
        """获取作品统计信息"""
        queryset = self.get_queryset()

        stats = {
            'total_works': queryset.count(),
            'completed_works': queryset.filter(status='completed').count(),
            'processing_works': queryset.filter(status='processing').count(),
            'failed_works': queryset.filter(status='failed').count(),
            'total_duration': '00:00:00',  # 需要计算总时长
            'total_computing_power': queryset.aggregate(
                total=Sum('computing_power_cost')
            )['total'] or 0,
            'avg_generation_time': queryset.filter(
                status='completed'
            ).aggregate(
                avg=Avg('generation_time')
            )['avg'] or 0.0
        }

        serializer = WorkStatsSerializer(stats)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def download(self, request, pk=None):
        """下载作品"""
        work = self.get_object()
        if not work.can_download:
            return Response(
                {'error': '作品不可下载'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # 这里可以实现实际的下载逻辑
        # 比如生成下载链接、记录下载日志等

        return Response({
            'download_url': work.video_url,
            'filename': f'{work.name}.mp4'
        })

    @action(detail=True, methods=['post'])
    def regenerate(self, request, pk=None):
        """重新生成作品"""
        work = self.get_object()
        if work.status == 'processing':
            return Response(
                {'error': '作品正在生成中'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # 重置状态为等待中
        work.status = 'pending'
        work.completed_at = None
        work.save()

        # 这里可以触发实际的生成任务

        return Response({'message': '重新生成任务已提交'})


class WorkTemplateViewSet(viewsets.ReadOnlyModelViewSet):
    """作品模板视图集"""
    queryset = WorkTemplate.objects.filter(is_active=True)
    serializer_class = WorkTemplateSerializer
    permission_classes = [permissions.AllowAny]  # 允许所有用户查看模板
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['category', 'is_premium']
    search_fields = ['name', 'description']
    ordering_fields = ['sort_order', 'usage_count', 'created_at']
    ordering = ['sort_order', '-created_at']

    @action(detail=True, methods=['post'])
    def use_template(self, request, pk=None):
        """使用模板"""
        template = self.get_object()

        # 增加使用次数
        template.usage_count += 1
        template.save(update_fields=['usage_count'])

        return Response({
            'template_id': template.id,
            'default_avatar': template.default_avatar,
            'default_voice': template.default_voice,
            'default_script': template.default_script
        })
