# Generated by Django 5.2.4 on 2025-07-16 04:17

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="PackageCategory",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        help_text="套餐分类的名称", max_length=50, verbose_name="分类名称"
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        blank=True, help_text="套餐分类的描述", verbose_name="分类描述"
                    ),
                ),
                (
                    "sort_order",
                    models.PositiveIntegerField(
                        default=0, help_text="显示排序，数字越小越靠前", verbose_name="排序"
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True, help_text="是否启用此分类", verbose_name="是否启用"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
            ],
            options={
                "verbose_name": "套餐分类",
                "verbose_name_plural": "套餐分类",
                "db_table": "package_categories",
                "ordering": ["sort_order", "name"],
            },
        ),
        migrations.CreateModel(
            name="Package",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        help_text="套餐的名称", max_length=100, verbose_name="套餐名称"
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        blank=True, help_text="套餐的详细描述", verbose_name="套餐描述"
                    ),
                ),
                (
                    "package_type",
                    models.CharField(
                        choices=[
                            ("computing_power", "算力套餐"),
                            ("membership", "会员套餐"),
                            ("voice_clone", "语音克隆套餐"),
                            ("synthesis_time", "合成时长套餐"),
                            ("combo", "组合套餐"),
                        ],
                        help_text="套餐的类型",
                        max_length=20,
                        verbose_name="套餐类型",
                    ),
                ),
                (
                    "price",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="套餐价格（元）",
                        max_digits=10,
                        verbose_name="价格",
                    ),
                ),
                (
                    "original_price",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="套餐原价（元），用于显示折扣",
                        max_digits=10,
                        null=True,
                        verbose_name="原价",
                    ),
                ),
                (
                    "computing_power_amount",
                    models.PositiveIntegerField(
                        default=0, help_text="套餐包含的算力点数", verbose_name="算力数量"
                    ),
                ),
                (
                    "bonus_computing_power",
                    models.PositiveIntegerField(
                        default=0, help_text="购买套餐赠送的额外算力点数", verbose_name="赠送算力"
                    ),
                ),
                (
                    "synthesis_duration",
                    models.PositiveIntegerField(
                        default=0, help_text="套餐包含的语音合成时长（秒）", verbose_name="合成时长"
                    ),
                ),
                (
                    "voice_clone_count",
                    models.PositiveIntegerField(
                        default=0, help_text="套餐包含的语音克隆次数", verbose_name="语音克隆数量"
                    ),
                ),
                (
                    "avatar_count",
                    models.PositiveIntegerField(
                        default=0, help_text="套餐包含的数字人形象数量", verbose_name="数字人形象数量"
                    ),
                ),
                (
                    "duration_type",
                    models.CharField(
                        choices=[
                            ("permanent", "永久"),
                            ("days", "天数"),
                            ("months", "月数"),
                            ("years", "年数"),
                        ],
                        default="permanent",
                        help_text="套餐有效期类型",
                        max_length=20,
                        verbose_name="有效期类型",
                    ),
                ),
                (
                    "duration_value",
                    models.PositiveIntegerField(
                        default=0, help_text="有效期的具体数值", verbose_name="有效期数值"
                    ),
                ),
                (
                    "features",
                    models.JSONField(
                        blank=True,
                        default=list,
                        help_text="套餐包含的特性列表",
                        verbose_name="特性列表",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True, help_text="是否启用此套餐", verbose_name="是否启用"
                    ),
                ),
                (
                    "is_recommended",
                    models.BooleanField(
                        default=False, help_text="是否为推荐套餐", verbose_name="是否推荐"
                    ),
                ),
                (
                    "sort_order",
                    models.PositiveIntegerField(
                        default=0, help_text="显示排序，数字越小越靠前", verbose_name="排序"
                    ),
                ),
                (
                    "sales_count",
                    models.PositiveIntegerField(
                        default=0, help_text="套餐销售数量", verbose_name="销售数量"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "category",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="packages",
                        to="packages.packagecategory",
                        verbose_name="套餐分类",
                    ),
                ),
            ],
            options={
                "verbose_name": "套餐",
                "verbose_name_plural": "套餐",
                "db_table": "packages",
                "ordering": ["sort_order", "-created_at"],
                "indexes": [
                    models.Index(
                        fields=["package_type", "is_active"],
                        name="packages_package_7846fe_idx",
                    ),
                    models.Index(
                        fields=["category", "is_active"],
                        name="packages_categor_dc144e_idx",
                    ),
                ],
            },
        ),
    ]
