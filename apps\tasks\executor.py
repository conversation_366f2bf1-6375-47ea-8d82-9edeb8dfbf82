"""
任务执行器
"""
import os
import time
import traceback
import logging
import threading
import multiprocessing
from datetime import datetime, timedelta
from django.utils import timezone
from django.conf import settings
from django.db import transaction
from .models import TaskQueue, TaskSchedule, TaskResult

logger = logging.getLogger(__name__)


class TaskRegistry:
    """任务注册表"""
    
    def __init__(self):
        self._tasks = {}
    
    def register(self, name, func):
        """注册任务函数"""
        self._tasks[name] = func
        logger.info(f"注册任务函数: {name}")
    
    def get(self, name):
        """获取任务函数"""
        return self._tasks.get(name)
    
    def list_tasks(self):
        """列出所有任务"""
        return list(self._tasks.keys())


# 全局任务注册表
task_registry = TaskRegistry()


def task(name=None):
    """任务装饰器"""
    def decorator(func):
        task_name = name or func.__name__
        task_registry.register(task_name, func)
        return func
    return decorator


class TaskExecutor:
    """任务执行器"""
    
    def __init__(self, worker_id=None, max_workers=None):
        self.worker_id = worker_id or f"worker_{os.getpid()}_{int(time.time())}"
        self.max_workers = max_workers or getattr(settings, 'TASK_MAX_WORKERS', 4)
        self.running = False
        self.workers = []
        self.shutdown_event = threading.Event()
    
    def start(self):
        """启动任务执行器"""
        if self.running:
            logger.warning("任务执行器已在运行")
            return
        
        self.running = True
        logger.info(f"启动任务执行器: {self.worker_id}, 最大工作进程数: {self.max_workers}")
        
        # 启动工作进程
        for i in range(self.max_workers):
            worker = TaskWorker(f"{self.worker_id}_worker_{i}", self.shutdown_event)
            worker.start()
            self.workers.append(worker)
        
        # 启动调度器
        scheduler = TaskScheduler(self.shutdown_event)
        scheduler.start()
        self.workers.append(scheduler)
        
        logger.info("任务执行器启动完成")
    
    def stop(self):
        """停止任务执行器"""
        if not self.running:
            return
        
        logger.info("正在停止任务执行器...")
        self.running = False
        self.shutdown_event.set()
        
        # 等待所有工作进程结束
        for worker in self.workers:
            worker.join(timeout=30)
            if worker.is_alive():
                logger.warning(f"工作进程 {worker.name} 未能正常结束")
        
        self.workers.clear()
        logger.info("任务执行器已停止")
    
    def submit_task(self, task_function, *args, **kwargs):
        """提交任务"""
        task_name = kwargs.pop('task_name', task_function)
        task_description = kwargs.pop('task_description', '')
        priority = kwargs.pop('priority', 'normal')
        max_retries = kwargs.pop('max_retries', 3)
        created_by = kwargs.pop('created_by', None)
        
        task = TaskQueue.objects.create(
            name=task_name,
            description=task_description,
            task_type='async',
            task_function=task_function,
            args=list(args),
            kwargs=kwargs,
            priority=priority,
            max_retries=max_retries,
            created_by=created_by
        )
        
        logger.info(f"任务已提交: {task.task_id} - {task.name}")
        return task


class TaskWorker(threading.Thread):
    """任务工作进程"""
    
    def __init__(self, worker_id, shutdown_event):
        super().__init__(name=worker_id, daemon=True)
        self.worker_id = worker_id
        self.shutdown_event = shutdown_event
        self.current_task = None
    
    def run(self):
        """运行工作进程"""
        logger.info(f"工作进程启动: {self.worker_id}")
        
        while not self.shutdown_event.is_set():
            try:
                # 获取待执行任务
                task = self.get_next_task()
                if not task:
                    time.sleep(1)
                    continue
                
                # 执行任务
                self.execute_task(task)
                
            except Exception as e:
                logger.error(f"工作进程异常: {str(e)}", exc_info=True)
                time.sleep(5)
        
        logger.info(f"工作进程结束: {self.worker_id}")
    
    def get_next_task(self):
        """获取下一个待执行任务"""
        try:
            with transaction.atomic():
                task = TaskQueue.objects.select_for_update().filter(
                    status='pending'
                ).order_by('-priority', 'created_at').first()
                
                if task:
                    task.mark_started(self.worker_id)
                    return task
                
        except Exception as e:
            logger.error(f"获取任务失败: {str(e)}")
        
        return None
    
    def execute_task(self, task):
        """执行任务"""
        self.current_task = task
        start_time = timezone.now()
        
        try:
            logger.info(f"开始执行任务: {task.task_id} - {task.name}")
            
            # 获取任务函数
            task_func = task_registry.get(task.task_function)
            if not task_func:
                raise ValueError(f"未找到任务函数: {task.task_function}")
            
            # 执行任务
            result = task_func(*task.args, **task.kwargs)
            
            # 标记完成
            task.mark_completed(result)
            
            # 记录执行结果
            end_time = timezone.now()
            duration = (end_time - start_time).total_seconds()
            
            TaskResult.objects.create(
                task=task,
                worker_id=self.worker_id,
                started_at=start_time,
                completed_at=end_time,
                duration=duration,
                success=True,
                result=result
            )
            
            logger.info(f"任务执行成功: {task.task_id} - 耗时: {duration:.2f}秒")
            
        except Exception as e:
            error_message = str(e)
            error_traceback = traceback.format_exc()
            
            logger.error(f"任务执行失败: {task.task_id} - {error_message}")
            
            # 检查是否可以重试
            if task.can_retry():
                task.retry_count += 1
                task.status = 'pending'
                task.save(update_fields=['retry_count', 'status', 'updated_at'])
                logger.info(f"任务将重试: {task.task_id} - 第{task.retry_count}次重试")
            else:
                task.mark_failed(error_message, error_traceback)
            
            # 记录执行结果
            end_time = timezone.now()
            duration = (end_time - start_time).total_seconds()
            
            TaskResult.objects.create(
                task=task,
                worker_id=self.worker_id,
                started_at=start_time,
                completed_at=end_time,
                duration=duration,
                success=False,
                error_message=error_message,
                traceback=error_traceback
            )
        
        finally:
            self.current_task = None


class TaskScheduler(threading.Thread):
    """任务调度器"""
    
    def __init__(self, shutdown_event):
        super().__init__(name="TaskScheduler", daemon=True)
        self.shutdown_event = shutdown_event
    
    def run(self):
        """运行调度器"""
        logger.info("任务调度器启动")
        
        while not self.shutdown_event.is_set():
            try:
                self.check_scheduled_tasks()
                self.cleanup_old_tasks()
                time.sleep(60)  # 每分钟检查一次
                
            except Exception as e:
                logger.error(f"调度器异常: {str(e)}", exc_info=True)
                time.sleep(60)
        
        logger.info("任务调度器结束")
    
    def check_scheduled_tasks(self):
        """检查定时任务"""
        now = timezone.now()
        
        # 获取需要执行的定时任务
        scheduled_tasks = TaskSchedule.objects.filter(
            is_active=True,
            next_run_at__lte=now
        )
        
        for schedule in scheduled_tasks:
            try:
                # 创建任务实例
                task = TaskQueue.objects.create(
                    name=f"[定时] {schedule.name}",
                    description=schedule.description,
                    task_type='scheduled',
                    task_function=schedule.task_function,
                    args=schedule.args,
                    kwargs=schedule.kwargs,
                    priority='normal',
                    created_by_id=1  # 系统用户
                )
                
                # 更新调度信息
                schedule.total_runs += 1
                schedule.last_run_at = now
                schedule.calculate_next_run()
                schedule.save()
                
                logger.info(f"定时任务已创建: {task.task_id} - {schedule.name}")
                
            except Exception as e:
                logger.error(f"创建定时任务失败: {schedule.name} - {str(e)}")
    
    def cleanup_old_tasks(self):
        """清理旧任务"""
        # 清理7天前的已完成任务
        cutoff_date = timezone.now() - timedelta(days=7)
        
        old_tasks = TaskQueue.objects.filter(
            status__in=['completed', 'failed', 'cancelled'],
            completed_at__lt=cutoff_date
        )
        
        count = old_tasks.count()
        if count > 0:
            old_tasks.delete()
            logger.info(f"清理了 {count} 个旧任务")


# 全局任务执行器实例
task_executor = TaskExecutor()
