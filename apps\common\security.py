"""
安全防护模块
"""
import hashlib
import hmac
import secrets
import time
import re
from datetime import datetime, timedelta
from django.conf import settings
from django.core.cache import cache
from django.http import HttpResponseForbidden
from django.utils import timezone
from django.contrib.auth import get_user_model
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import base64
import logging

User = get_user_model()
logger = logging.getLogger('security')


class DataEncryption:
    """数据加密工具"""
    
    def __init__(self, password=None):
        self.password = password or getattr(settings, 'SECRET_KEY', 'default-key')
        self.fernet = self._get_fernet()
    
    def _get_fernet(self):
        """获取Fernet加密器"""
        password = self.password.encode()
        salt = b'salt_'  # 在生产环境中应该使用随机盐
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(password))
        return Fernet(key)
    
    def encrypt(self, data):
        """加密数据"""
        if isinstance(data, str):
            data = data.encode()
        return self.fernet.encrypt(data).decode()
    
    def decrypt(self, encrypted_data):
        """解密数据"""
        if isinstance(encrypted_data, str):
            encrypted_data = encrypted_data.encode()
        return self.fernet.decrypt(encrypted_data).decode()
    
    def encrypt_sensitive_data(self, data_dict):
        """加密敏感数据字典"""
        encrypted = {}
        sensitive_fields = ['password', 'token', 'secret', 'key', 'phone', 'email']
        
        for key, value in data_dict.items():
            if any(field in key.lower() for field in sensitive_fields):
                encrypted[key] = self.encrypt(str(value))
            else:
                encrypted[key] = value
        
        return encrypted


class SecurityValidator:
    """安全验证器"""
    
    # SQL注入检测模式
    SQL_INJECTION_PATTERNS = [
        r"(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)",
        r"(\b(OR|AND)\s+\d+\s*=\s*\d+)",
        r"(\b(OR|AND)\s+['\"]?\w+['\"]?\s*=\s*['\"]?\w+['\"]?)",
        r"(--|#|/\*|\*/)",
        r"(\bUNION\s+SELECT\b)",
        r"(\bINTO\s+OUTFILE\b)",
    ]
    
    # XSS检测模式
    XSS_PATTERNS = [
        r"<script[^>]*>.*?</script>",
        r"javascript:",
        r"on\w+\s*=",
        r"<iframe[^>]*>",
        r"<object[^>]*>",
        r"<embed[^>]*>",
        r"<link[^>]*>",
        r"<meta[^>]*>",
    ]
    
    # 路径遍历检测模式
    PATH_TRAVERSAL_PATTERNS = [
        r"\.\./",
        r"\.\.\\",
        r"%2e%2e%2f",
        r"%2e%2e%5c",
        r"..%2f",
        r"..%5c",
    ]
    
    @classmethod
    def check_sql_injection(cls, input_string):
        """检测SQL注入"""
        if not input_string:
            return False
        
        input_lower = input_string.lower()
        for pattern in cls.SQL_INJECTION_PATTERNS:
            if re.search(pattern, input_lower, re.IGNORECASE):
                return True
        return False
    
    @classmethod
    def check_xss(cls, input_string):
        """检测XSS攻击"""
        if not input_string:
            return False
        
        for pattern in cls.XSS_PATTERNS:
            if re.search(pattern, input_string, re.IGNORECASE):
                return True
        return False
    
    @classmethod
    def check_path_traversal(cls, input_string):
        """检测路径遍历攻击"""
        if not input_string:
            return False
        
        for pattern in cls.PATH_TRAVERSAL_PATTERNS:
            if re.search(pattern, input_string, re.IGNORECASE):
                return True
        return False
    
    @classmethod
    def validate_input(cls, input_string, check_types=None):
        """综合输入验证"""
        if not input_string:
            return True, []
        
        check_types = check_types or ['sql', 'xss', 'path']
        threats = []
        
        if 'sql' in check_types and cls.check_sql_injection(input_string):
            threats.append('SQL注入')
        
        if 'xss' in check_types and cls.check_xss(input_string):
            threats.append('XSS攻击')
        
        if 'path' in check_types and cls.check_path_traversal(input_string):
            threats.append('路径遍历')
        
        return len(threats) == 0, threats


class RateLimiter:
    """增强版频率限制器"""
    
    def __init__(self, key_prefix='rate_limit'):
        self.key_prefix = key_prefix
    
    def is_allowed(self, identifier, limit, window, burst_limit=None):
        """检查是否允许请求"""
        cache_key = f"{self.key_prefix}:{identifier}"
        current_time = int(time.time())
        window_start = current_time - window
        
        # 获取当前窗口内的请求记录
        requests = cache.get(cache_key, [])
        
        # 清理过期的请求记录
        requests = [req_time for req_time in requests if req_time > window_start]
        
        # 检查基本限制
        if len(requests) >= limit:
            return False, f"超出频率限制: {limit}次/{window}秒"
        
        # 检查突发限制
        if burst_limit:
            recent_requests = [req_time for req_time in requests if req_time > current_time - 60]
            if len(recent_requests) >= burst_limit:
                return False, f"超出突发限制: {burst_limit}次/分钟"
        
        # 记录当前请求
        requests.append(current_time)
        cache.set(cache_key, requests, window + 60)
        
        return True, None
    
    def get_remaining_requests(self, identifier, limit, window):
        """获取剩余请求次数"""
        cache_key = f"{self.key_prefix}:{identifier}"
        current_time = int(time.time())
        window_start = current_time - window
        
        requests = cache.get(cache_key, [])
        requests = [req_time for req_time in requests if req_time > window_start]
        
        return max(0, limit - len(requests))
    
    def reset_limit(self, identifier):
        """重置限制"""
        cache_key = f"{self.key_prefix}:{identifier}"
        cache.delete(cache_key)


class SecurityAuditor:
    """安全审计器"""
    
    def __init__(self):
        self.logger = logging.getLogger('security')
    
    def log_security_event(self, event_type, description, request=None, user=None, severity='medium', **kwargs):
        """记录安全事件"""
        from .models import SecurityLog
        
        # 获取请求信息
        ip_address = None
        user_agent = ''
        url = ''
        method = ''
        
        if request:
            ip_address = self.get_client_ip(request)
            user_agent = request.META.get('HTTP_USER_AGENT', '')
            url = request.build_absolute_uri()
            method = request.method
        
        # 创建安全日志
        security_log = SecurityLog.objects.create(
            event_type=event_type,
            severity=severity,
            description=description,
            user=user,
            username=user.username if user else '',
            ip_address=ip_address,
            user_agent=user_agent,
            url=url,
            method=method,
            extra_data=kwargs
        )
        
        # 记录到日志文件
        self.logger.warning(
            f"Security event: {description}",
            extra={
                'event_type': event_type,
                'severity': severity,
                'user_id': user.id if user else None,
                'ip_address': ip_address,
                'user_agent': user_agent,
                'url': url,
                'method': method,
                'extra_data': kwargs
            }
        )
        
        return security_log
    
    def log_login_attempt(self, request, username, success=True, **kwargs):
        """记录登录尝试"""
        event_type = 'login_success' if success else 'login_failure'
        description = f"用户 {username} 登录{'成功' if success else '失败'}"
        severity = 'low' if success else 'medium'
        
        return self.log_security_event(
            event_type=event_type,
            description=description,
            request=request,
            severity=severity,
            username=username,
            **kwargs
        )
    
    def log_permission_denied(self, request, resource, action, user=None, **kwargs):
        """记录权限拒绝"""
        description = f"权限拒绝: 用户尝试{action} {resource}"
        
        return self.log_security_event(
            event_type='permission_denied',
            description=description,
            request=request,
            user=user,
            severity='medium',
            resource=resource,
            action=action,
            **kwargs
        )
    
    def log_suspicious_activity(self, request, activity_type, description, user=None, **kwargs):
        """记录可疑活动"""
        return self.log_security_event(
            event_type='suspicious_activity',
            description=f"可疑活动: {description}",
            request=request,
            user=user,
            severity='high',
            activity_type=activity_type,
            **kwargs
        )
    
    def log_attack_attempt(self, request, attack_type, description, **kwargs):
        """记录攻击尝试"""
        event_type_map = {
            'sql_injection': 'sql_injection_attempt',
            'xss': 'xss_attempt',
            'csrf': 'csrf_failure',
        }
        
        event_type = event_type_map.get(attack_type, 'other')
        
        return self.log_security_event(
            event_type=event_type,
            description=f"{attack_type.upper()}攻击尝试: {description}",
            request=request,
            severity='high',
            attack_type=attack_type,
            **kwargs
        )
    
    def get_client_ip(self, request):
        """获取客户端IP"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class SecurityMiddleware:
    """安全中间件"""
    
    def __init__(self, get_response):
        self.get_response = get_response
        self.validator = SecurityValidator()
        self.auditor = SecurityAuditor()
        self.rate_limiter = RateLimiter()
    
    def __call__(self, request):
        # 安全检查
        if not self.security_check(request):
            return HttpResponseForbidden("请求被安全策略拒绝")
        
        response = self.get_response(request)
        
        # 添加安全头
        self.add_security_headers(response)
        
        return response
    
    def security_check(self, request):
        """安全检查"""
        # 检查请求参数
        for key, value in request.GET.items():
            if isinstance(value, str):
                is_safe, threats = self.validator.validate_input(value)
                if not is_safe:
                    self.auditor.log_attack_attempt(
                        request=request,
                        attack_type=threats[0].lower().replace(' ', '_'),
                        description=f"在GET参数 {key} 中检测到 {', '.join(threats)}",
                        parameter=key,
                        value=value
                    )
                    return False
        
        # 检查POST数据
        if hasattr(request, 'POST'):
            for key, value in request.POST.items():
                if isinstance(value, str):
                    is_safe, threats = self.validator.validate_input(value)
                    if not is_safe:
                        self.auditor.log_attack_attempt(
                            request=request,
                            attack_type=threats[0].lower().replace(' ', '_'),
                            description=f"在POST参数 {key} 中检测到 {', '.join(threats)}",
                            parameter=key,
                            value=value
                        )
                        return False
        
        # 频率限制检查
        client_ip = self.auditor.get_client_ip(request)
        allowed, message = self.rate_limiter.is_allowed(
            identifier=client_ip,
            limit=100,  # 每分钟100次请求
            window=60,
            burst_limit=20  # 突发限制20次/分钟
        )
        
        if not allowed:
            self.auditor.log_security_event(
                event_type='rate_limit_exceeded',
                description=f"IP {client_ip} 超出频率限制",
                request=request,
                severity='medium',
                limit_message=message
            )
            return False
        
        return True
    
    def add_security_headers(self, response):
        """添加安全头"""
        # 防止点击劫持
        response['X-Frame-Options'] = 'DENY'
        
        # 防止MIME类型嗅探
        response['X-Content-Type-Options'] = 'nosniff'
        
        # XSS保护
        response['X-XSS-Protection'] = '1; mode=block'
        
        # 强制HTTPS
        response['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
        
        # 内容安全策略
        response['Content-Security-Policy'] = (
            "default-src 'self'; "
            "script-src 'self' 'unsafe-inline' 'unsafe-eval'; "
            "style-src 'self' 'unsafe-inline'; "
            "img-src 'self' data: https:; "
            "font-src 'self' https:; "
            "connect-src 'self' https:; "
            "frame-ancestors 'none';"
        )
        
        # 引用策略
        response['Referrer-Policy'] = 'strict-origin-when-cross-origin'
        
        # 权限策略
        response['Permissions-Policy'] = (
            "geolocation=(), "
            "microphone=(), "
            "camera=(), "
            "payment=(), "
            "usb=(), "
            "magnetometer=(), "
            "gyroscope=(), "
            "speaker=()"
        )


# 全局实例
data_encryption = DataEncryption()
security_validator = SecurityValidator()
security_auditor = SecurityAuditor()
rate_limiter = RateLimiter()
