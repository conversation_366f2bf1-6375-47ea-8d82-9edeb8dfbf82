"""
数字人形象模型
基于对https://hm.umi6.com/平台的逆向工程分析
"""
from django.db import models
from django.conf import settings
from django.utils.translation import gettext_lazy as _


class DigitalAvatar(models.Model):
    """数字人形象模型"""
    
    MODEL_VERSION_CHOICES = [
        ('V8模型', 'V8模型'),
        ('V6模型', 'V6模型'),
        ('V5模型', 'V5模型'),
        ('V8模型/V6模型/V5模型', 'V8模型/V6模型/V5模型'),
        ('V6模型/V5模型', 'V6模型/V5模型'),
    ]

    SCENE_TYPE_CHOICES = [
        ('outdoor', '户外'),
        ('indoor', '室内'),
        ('general', '通用'),
        ('outdoor_sitting', '户外坐姿'),
        ('sitting_half', '坐姿半身'),
        ('outdoor_full', '户外全身'),
        ('outdoor_standing', '户外站姿'),
        ('standing_material', '站姿素材'),
        ('indoor_oral', '室内口播'),
        ('indoor_half', '室内半身'),
        ('indoor_sitting', '室内坐姿'),
        ('indoor_full', '室内全身'),
        ('indoor_drama', '室内戏剧'),
        ('green_screen_sitting', '绿幕斜坐'),
        ('green_screen_half_standing', '绿幕半身站'),
        ('green_screen_half_sitting', '绿幕半身坐'),
        ('walking_talking', '边说边走'),
        ('garden_scene', '花园场景'),
        ('ancient_building', '古建筑'),
        ('library_scene', '图书馆'),
        ('restaurant_scene', '餐厅场景'),
        ('travel_scene', '旅游景区'),
        ('office_scene', '写字楼'),
        ('other', '其他场景'),
    ]
    
    name = models.CharField(
        _('形象名称'),
        max_length=100,
        help_text=_('数字人形象的名称')
    )
    
    description = models.TextField(
        _('形象描述'),
        blank=True,
        help_text=_('数字人形象的详细描述')
    )
    
    model_version = models.CharField(
        _('模型版本'),
        max_length=50,
        choices=MODEL_VERSION_CHOICES,
        default='V8模型',
        help_text=_('支持的数字人模型版本')
    )
    
    scene_type = models.CharField(
        _('场景类型'),
        max_length=50,
        choices=SCENE_TYPE_CHOICES,
        help_text=_('数字人形象的场景类型')
    )
    
    thumbnail = models.ImageField(
        _('缩略图'),
        upload_to='avatars/thumbnails/',
        help_text=_('数字人形象的预览图片')
    )
    
    video_preview = models.FileField(
        _('视频预览'),
        upload_to='avatars/videos/',
        null=True,
        blank=True,
        help_text=_('数字人形象的视频预览文件')
    )
    
    video_url = models.URLField(
        _('视频链接'),
        null=True,
        blank=True,
        help_text=_('数字人形象的在线视频链接')
    )
    
    is_public = models.BooleanField(
        _('是否公开'),
        default=True,
        help_text=_('是否为公共数字人形象，所有用户可用')
    )
    
    is_active = models.BooleanField(
        _('是否启用'),
        default=True,
        help_text=_('是否启用此数字人形象')
    )
    
    owner = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='owned_avatars',
        null=True,
        blank=True,
        verbose_name=_('所有者'),
        help_text=_('私有数字人形象的所有者，公共形象此字段为空')
    )
    
    computing_power_cost = models.PositiveIntegerField(
        _('算力消耗'),
        default=10,
        help_text=_('使用此数字人形象生成视频所需的算力点数')
    )
    
    sort_order = models.PositiveIntegerField(
        _('排序'),
        default=0,
        help_text=_('显示排序，数字越小越靠前')
    )
    
    usage_count = models.PositiveIntegerField(
        _('使用次数'),
        default=0,
        help_text=_('此数字人形象被使用的总次数')
    )
    
    created_at = models.DateTimeField(
        _('创建时间'),
        auto_now_add=True
    )
    
    updated_at = models.DateTimeField(
        _('更新时间'),
        auto_now=True
    )

    class Meta:
        verbose_name = _('数字人形象')
        verbose_name_plural = _('数字人形象')
        db_table = 'digital_avatars'
        ordering = ['sort_order', '-created_at']
        indexes = [
            models.Index(fields=['is_public', 'is_active']),
            models.Index(fields=['model_version']),
            models.Index(fields=['scene_type']),
            models.Index(fields=['owner']),
        ]

    def __str__(self):
        return self.name

    def increment_usage(self):
        """增加使用次数"""
        self.usage_count += 1
        self.save(update_fields=['usage_count'])

    @property
    def display_model_versions(self):
        """获取支持的模型版本显示文本"""
        return f"{self.model_version}模型"

    @property
    def video_thumbnail_url(self):
        """获取视频缩略图URL"""
        if self.video_url:
            # 阿里云OSS视频缩略图处理
            if 'aliyuncs.com' in self.video_url:
                return f"{self.video_url}?x-oss-process=video/snapshot,t_0,m_fast,ar_auto"
        return None


class AvatarCategory(models.Model):
    """数字人形象分类"""
    
    name = models.CharField(
        _('分类名称'),
        max_length=50,
        unique=True,
        help_text=_('数字人形象分类名称')
    )
    
    description = models.TextField(
        _('分类描述'),
        blank=True,
        help_text=_('分类的详细描述')
    )
    
    icon = models.CharField(
        _('图标'),
        max_length=50,
        blank=True,
        help_text=_('分类图标的CSS类名或图标名称')
    )
    
    sort_order = models.PositiveIntegerField(
        _('排序'),
        default=0,
        help_text=_('显示排序，数字越小越靠前')
    )
    
    is_active = models.BooleanField(
        _('是否启用'),
        default=True,
        help_text=_('是否启用此分类')
    )
    
    created_at = models.DateTimeField(
        _('创建时间'),
        auto_now_add=True
    )

    class Meta:
        verbose_name = _('形象分类')
        verbose_name_plural = _('形象分类')
        db_table = 'avatar_categories'
        ordering = ['sort_order', 'name']

    def __str__(self):
        return self.name


class AvatarTag(models.Model):
    """数字人形象标签"""
    
    name = models.CharField(
        _('标签名称'),
        max_length=30,
        unique=True,
        help_text=_('数字人形象标签名称')
    )
    
    color = models.CharField(
        _('标签颜色'),
        max_length=7,
        default='#007bff',
        help_text=_('标签显示颜色，十六进制格式')
    )
    
    usage_count = models.PositiveIntegerField(
        _('使用次数'),
        default=0,
        help_text=_('此标签被使用的次数')
    )
    
    created_at = models.DateTimeField(
        _('创建时间'),
        auto_now_add=True
    )

    class Meta:
        verbose_name = _('形象标签')
        verbose_name_plural = _('形象标签')
        db_table = 'avatar_tags'
        ordering = ['-usage_count', 'name']

    def __str__(self):
        return self.name


# 为DigitalAvatar添加多对多关系
DigitalAvatar.add_to_class(
    'categories',
    models.ManyToManyField(
        AvatarCategory,
        blank=True,
        related_name='avatars',
        verbose_name=_('分类'),
        help_text=_('数字人形象所属的分类')
    )
)

DigitalAvatar.add_to_class(
    'tags',
    models.ManyToManyField(
        AvatarTag,
        blank=True,
        related_name='avatars',
        verbose_name=_('标签'),
        help_text=_('数字人形象的标签')
    )
)


class AvatarUsageLog(models.Model):
    """数字人形象使用日志"""
    
    avatar = models.ForeignKey(
        DigitalAvatar,
        on_delete=models.CASCADE,
        related_name='usage_logs',
        verbose_name=_('数字人形象')
    )
    
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='avatar_usage_logs',
        verbose_name=_('用户')
    )
    
    computing_power_consumed = models.PositiveIntegerField(
        _('消耗算力'),
        help_text=_('本次使用消耗的算力点数')
    )
    
    purpose = models.CharField(
        _('使用目的'),
        max_length=100,
        blank=True,
        help_text=_('使用数字人形象的目的或项目名称')
    )
    
    created_at = models.DateTimeField(
        _('使用时间'),
        auto_now_add=True
    )

    class Meta:
        verbose_name = _('形象使用日志')
        verbose_name_plural = _('形象使用日志')
        db_table = 'avatar_usage_logs'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['avatar', 'created_at']),
            models.Index(fields=['user', 'created_at']),
        ]

    def __str__(self):
        return f'{self.user.display_name} 使用 {self.avatar.name}'
