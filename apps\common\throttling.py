"""
API限流功能
"""
import time
import hashlib
from django.core.cache import cache
from django.conf import settings
from rest_framework.throttling import BaseThrottle
from rest_framework.exceptions import Throttled
from django.utils.translation import gettext_lazy as _
import logging

logger = logging.getLogger(__name__)


class CustomThrottle(BaseThrottle):
    """自定义限流器基类"""
    
    # 限流配置 - 开发环境放宽限制
    THROTTLE_RATES = {
        'anon': '1000/hour',     # 匿名用户每小时1000次
        'user': '10000/hour',    # 认证用户每小时10000次
        'login': '50/min',       # 登录接口每分钟50次
        'register': '30/min',    # 注册接口每分钟30次
        'upload': '100/min',     # 文件上传每分钟100次
        'ai_generate': '200/hour', # AI生成每小时200次
        'burst': '100/min',      # 突发限制每分钟100次
    }
    
    def __init__(self):
        super().__init__()
        self.cache_format = 'throttle_%(scope)s_%(ident)s'
        self.timer = time.time
        self.cache = cache

        # 设置限流速率
        if hasattr(self, 'scope') and self.scope in self.THROTTLE_RATES:
            self.rate = self.THROTTLE_RATES[self.scope]
            self.num_requests, self.duration = self.parse_rate(self.rate)
        else:
            self.rate = None
    
    def allow_request(self, request, view):
        """
        检查是否允许请求
        """
        if self.rate is None:
            return True
        
        self.key = self.get_cache_key(request, view)
        if self.key is None:
            return True
        
        self.history = self.cache.get(self.key, [])
        self.now = self.timer()
        
        # 清理过期的历史记录
        while self.history and self.history[-1] <= self.now - self.duration:
            self.history.pop()
        
        # 检查是否超过限制
        if len(self.history) >= self.num_requests:
            return self.throttle_failure()
        
        return self.throttle_success()
    
    def throttle_success(self):
        """
        限流检查通过
        """
        self.history.insert(0, self.now)
        self.cache.set(self.key, self.history, self.duration)
        return True
    
    def throttle_failure(self):
        """
        限流检查失败
        """
        return False
    
    def get_cache_key(self, request, view):
        """
        生成缓存键
        """
        ident = self.get_ident(request)
        return self.cache_format % {
            'scope': self.scope,
            'ident': ident,
        }
    
    def get_ident(self, request):
        """
        获取请求标识符
        """
        xff = request.META.get('HTTP_X_FORWARDED_FOR')
        remote_addr = request.META.get('REMOTE_ADDR')
        num_proxies = getattr(settings, 'NUM_PROXIES', None)
        
        if num_proxies is not None:
            if num_proxies == 0 or xff is None:
                return remote_addr
            addrs = xff.split(',')
            client_addr = addrs[-min(num_proxies, len(addrs))]
            return client_addr.strip()
        
        return ''.join(xff.split()) if xff else remote_addr
    
    def parse_rate(self, rate):
        """
        解析限流速率
        """
        if rate is None:
            return (None, None)
        
        num, period = rate.split('/')
        num_requests = int(num)
        
        duration = {'s': 1, 'm': 60, 'h': 3600, 'd': 86400}[period[0]]
        return (num_requests, duration)
    
    def wait(self):
        """
        计算等待时间
        """
        if self.history:
            remaining_duration = self.duration - (self.now - self.history[-1])
        else:
            remaining_duration = self.duration
        
        available_requests = self.num_requests - len(self.history) + 1
        if available_requests <= 0:
            return None
        
        return remaining_duration / float(available_requests)


class AnonRateThrottle(CustomThrottle):
    """匿名用户限流"""
    scope = 'anon'
    
    def get_cache_key(self, request, view):
        if request.user.is_authenticated:
            return None  # 只限制匿名用户
        
        ident = self.get_ident(request)
        return self.cache_format % {
            'scope': self.scope,
            'ident': ident,
        }


class UserRateThrottle(CustomThrottle):
    """认证用户限流"""
    scope = 'user'
    
    def get_cache_key(self, request, view):
        if request.user.is_authenticated:
            ident = request.user.pk
        else:
            ident = self.get_ident(request)
        
        return self.cache_format % {
            'scope': self.scope,
            'ident': ident,
        }


class LoginRateThrottle(CustomThrottle):
    """登录接口限流"""
    scope = 'login'
    
    def get_cache_key(self, request, view):
        ident = self.get_ident(request)
        return self.cache_format % {
            'scope': self.scope,
            'ident': ident,
        }


class RegisterRateThrottle(CustomThrottle):
    """注册接口限流"""
    scope = 'register'
    
    def get_cache_key(self, request, view):
        ident = self.get_ident(request)
        return self.cache_format % {
            'scope': self.scope,
            'ident': ident,
        }


class UploadRateThrottle(CustomThrottle):
    """文件上传限流"""
    scope = 'upload'
    
    def get_cache_key(self, request, view):
        if request.user.is_authenticated:
            ident = request.user.pk
        else:
            ident = self.get_ident(request)
        
        return self.cache_format % {
            'scope': self.scope,
            'ident': ident,
        }


class AIGenerateRateThrottle(CustomThrottle):
    """AI生成接口限流"""
    scope = 'ai_generate'
    
    def get_cache_key(self, request, view):
        if request.user.is_authenticated:
            ident = request.user.pk
        else:
            return None  # AI生成需要登录
        
        return self.cache_format % {
            'scope': self.scope,
            'ident': ident,
        }


class BurstRateThrottle(CustomThrottle):
    """突发请求限流"""
    scope = 'burst'
    
    def get_cache_key(self, request, view):
        if request.user.is_authenticated:
            ident = request.user.pk
        else:
            ident = self.get_ident(request)
        
        return self.cache_format % {
            'scope': self.scope,
            'ident': ident,
        }


class ThrottleMiddleware:
    """限流中间件"""
    
    def __init__(self, get_response):
        self.get_response = get_response
        self.throttle = CustomThrottle()
    
    def __call__(self, request):
        # 检查是否需要限流
        if self.should_throttle(request):
            if not self.check_throttle(request):
                from django.http import JsonResponse
                return JsonResponse({
                    'error': '请求过于频繁，请稍后再试',
                    'code': 'THROTTLED'
                }, status=429)
        
        response = self.get_response(request)
        return response
    
    def should_throttle(self, request):
        """判断是否需要限流"""
        # 只对API请求限流
        if not request.path.startswith('/api/'):
            return False
        
        # 管理员不限流
        if hasattr(request, 'user') and request.user.is_superuser:
            return False
        
        return True
    
    def check_throttle(self, request):
        """检查限流"""
        # 这里可以实现简单的限流逻辑
        # 更复杂的限流应该在视图层使用DRF的限流器
        return True


def throttle_decorator(throttle_class):
    """
    限流装饰器
    """
    def decorator(view_func):
        def wrapper(request, *args, **kwargs):
            throttle = throttle_class()
            
            # 设置限流参数
            if hasattr(throttle, 'scope') and throttle.scope in throttle.THROTTLE_RATES:
                rate = throttle.THROTTLE_RATES[throttle.scope]
                throttle.num_requests, throttle.duration = throttle.parse_rate(rate)
                throttle.cache = cache
            
            # 检查限流
            if not throttle.allow_request(request, None):
                wait_time = throttle.wait()
                raise Throttled(
                    detail=_('请求过于频繁，请稍后再试'),
                    wait=wait_time
                )
            
            return view_func(request, *args, **kwargs)
        
        return wrapper
    return decorator


# 常用限流装饰器
login_throttle = throttle_decorator(LoginRateThrottle)
register_throttle = throttle_decorator(RegisterRateThrottle)
upload_throttle = throttle_decorator(UploadRateThrottle)
ai_generate_throttle = throttle_decorator(AIGenerateRateThrottle)
