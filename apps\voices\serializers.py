"""
语音系统相关序列化器
"""
from rest_framework import serializers
from .models import VoiceModel, VoiceCloneRequest, VoiceSynthesisTask


class VoiceModelSerializer(serializers.ModelSerializer):
    """语音模型序列化器"""
    owner_display = serializers.CharField(source='owner.display_name', read_only=True)
    language_display = serializers.CharField(source='get_language_display', read_only=True)
    gender_display = serializers.CharField(source='get_gender_display', read_only=True)
    voice_type_display = serializers.CharField(source='get_voice_type_display', read_only=True)
    display_info = serializers.CharField(read_only=True)
    
    class Meta:
        model = VoiceModel
        fields = (
            'id', 'name', 'description', 'language', 'language_display',
            'gender', 'gender_display', 'voice_type', 'voice_type_display',
            'audio_sample', 'audio_sample_url', 'is_cloned', 'is_public',
            'is_active', 'owner', 'owner_display', 'computing_power_cost',
            'sort_order', 'usage_count', 'default_volume', 'default_pitch',
            'default_speed', 'display_info', 'created_at', 'updated_at'
        )
        read_only_fields = ('id', 'usage_count', 'created_at', 'updated_at')


class VoiceCloneRequestSerializer(serializers.ModelSerializer):
    """语音克隆请求序列化器"""
    user_display = serializers.CharField(source='user.display_name', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    result_voice_name = serializers.CharField(source='result_voice.name', read_only=True)
    
    class Meta:
        model = VoiceCloneRequest
        fields = (
            'id', 'user', 'user_display', 'name', 'description',
            'source_audio', 'audio_duration', 'status', 'status_display',
            'progress', 'error_message', 'result_voice', 'result_voice_name',
            'computing_power_consumed', 'created_at', 'updated_at', 'completed_at'
        )
        read_only_fields = (
            'id', 'user', 'status', 'progress', 'error_message',
            'result_voice', 'computing_power_consumed', 'created_at',
            'updated_at', 'completed_at'
        )


class VoiceSynthesisTaskSerializer(serializers.ModelSerializer):
    """语音合成任务序列化器"""
    user_display = serializers.CharField(source='user.display_name', read_only=True)
    voice_model_name = serializers.CharField(source='voice_model.name', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    
    class Meta:
        model = VoiceSynthesisTask
        fields = (
            'id', 'user', 'user_display', 'voice_model', 'voice_model_name',
            'text_content', 'volume', 'pitch', 'speed', 'status', 'status_display',
            'progress', 'error_message', 'result_audio', 'result_audio_url',
            'audio_duration', 'computing_power_consumed', 'created_at',
            'updated_at', 'completed_at'
        )
        read_only_fields = (
            'id', 'user', 'status', 'progress', 'error_message',
            'result_audio', 'result_audio_url', 'audio_duration',
            'computing_power_consumed', 'created_at', 'updated_at', 'completed_at'
        )
