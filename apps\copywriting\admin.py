"""
智能文案管理后台配置
"""
from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from django.utils.html import format_html
from unfold.admin import ModelAdmin, TabularInline
from unfold.contrib.filters.admin import RangeDateFilter, ChoicesDropdownFilter

from .models import Copywriting, CopywritingTemplate, CopywritingHistory, TextExtraction


class CopywritingHistoryInline(TabularInline):
    """文案历史内联编辑"""
    model = CopywritingHistory
    extra = 0
    readonly_fields = ('created_at',)
    fields = ('version', 'word_count', 'created_at')
    
    def has_add_permission(self, request, obj=None):
        return False


@admin.register(Copywriting)
class CopywritingAdmin(ModelAdmin):
    """智能文案管理"""
    
    list_display = (
        'title', 'user', 'scene_type', 'word_count', 'status',
        'actual_word_count', 'computing_power_consumed', 'created_at'
    )
    
    list_filter = (
        ('scene_type', ChoicesDropdownFilter),
        ('status', ChoicesDropdownFilter),
        ('word_count', ChoicesDropdownFilter),
        ('language', ChoicesDropdownFilter),
        'is_saved_as_draft',
        ('created_at', RangeDateFilter),
        ('completed_at', RangeDateFilter),
    )
    
    search_fields = ('title', 'theme_description', 'generated_content', 'user__username')
    
    readonly_fields = (
        'actual_word_count', 'computing_power_consumed', 'created_at',
        'updated_at', 'completed_at', 'content_preview'
    )
    
    fieldsets = (
        (_('基本信息'), {
            'fields': ('title', 'user', 'scene_type', 'theme_description')
        }),
        (_('生成设置'), {
            'fields': ('word_count', 'language')
        }),
        (_('状态和结果'), {
            'fields': ('status', 'generated_content', 'content_preview', 'error_message')
        }),
        (_('统计信息'), {
            'fields': ('actual_word_count', 'computing_power_consumed', 'is_saved_as_draft')
        }),
        (_('时间信息'), {
            'fields': ('created_at', 'updated_at', 'completed_at'),
            'classes': ('collapse',)
        }),
    )
    
    ordering = ('-created_at',)
    
    inlines = [CopywritingHistoryInline]
    
    actions = ['approve_copywriting', 'reject_copywriting']
    
    def content_preview(self, obj):
        """内容预览"""
        if obj.generated_content:
            preview = obj.generated_content[:300]
            if len(obj.generated_content) > 300:
                preview += '...'
            return format_html('<div style="max-width: 400px; word-wrap: break-word;">{}</div>', preview)
        return _('无生成内容')
    content_preview.short_description = _('生成内容预览')
    
    def approve_copywriting(self, request, queryset):
        """批量通过文案"""
        updated = queryset.filter(status='pending').update(status='generating')
        self.message_user(request, f'成功通过 {updated} 个文案')
    approve_copywriting.short_description = _('通过选中的文案')
    
    def reject_copywriting(self, request, queryset):
        """批量拒绝文案"""
        updated = queryset.filter(status='pending').update(status='failed')
        self.message_user(request, f'成功拒绝 {updated} 个文案')
    reject_copywriting.short_description = _('拒绝选中的文案')


@admin.register(CopywritingTemplate)
class CopywritingTemplateAdmin(ModelAdmin):
    """文案模板管理"""
    
    list_display = (
        'name', 'scene_type', 'is_active', 'usage_count', 'created_by', 'created_at'
    )
    
    list_filter = (
        ('scene_type', ChoicesDropdownFilter),
        'is_active',
        ('created_at', RangeDateFilter),
    )
    
    search_fields = ('name', 'description', 'template_content')
    
    readonly_fields = ('usage_count', 'created_at', 'updated_at', 'template_preview')
    
    fieldsets = (
        (_('基本信息'), {
            'fields': ('name', 'description', 'scene_type', 'created_by')
        }),
        (_('模板内容'), {
            'fields': ('template_content', 'template_preview', 'variables')
        }),
        (_('状态'), {
            'fields': ('is_active',)
        }),
        (_('统计信息'), {
            'fields': ('usage_count', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    ordering = ('-usage_count', 'name')
    
    actions = ['activate_templates', 'deactivate_templates']
    
    def template_preview(self, obj):
        """模板预览"""
        if obj.template_content:
            preview = obj.template_content[:300]
            if len(obj.template_content) > 300:
                preview += '...'
            return format_html('<div style="max-width: 400px; word-wrap: break-word;">{}</div>', preview)
        return _('无模板内容')
    template_preview.short_description = _('模板内容预览')
    
    def activate_templates(self, request, queryset):
        """批量启用模板"""
        updated = queryset.update(is_active=True)
        self.message_user(request, f'成功启用 {updated} 个模板')
    activate_templates.short_description = _('启用选中的模板')
    
    def deactivate_templates(self, request, queryset):
        """批量停用模板"""
        updated = queryset.update(is_active=False)
        self.message_user(request, f'成功停用 {updated} 个模板')
    deactivate_templates.short_description = _('停用选中的模板')


@admin.register(CopywritingHistory)
class CopywritingHistoryAdmin(ModelAdmin):
    """文案历史管理"""
    
    list_display = (
        'copywriting', 'version', 'word_count', 'created_at'
    )
    
    list_filter = (
        ('created_at', RangeDateFilter),
        'copywriting__scene_type',
    )
    
    search_fields = ('copywriting__title', 'content')
    
    readonly_fields = ('created_at', 'content_preview')
    
    fieldsets = (
        (_('基本信息'), {
            'fields': ('copywriting', 'version', 'word_count')
        }),
        (_('内容'), {
            'fields': ('content', 'content_preview')
        }),
        (_('参数'), {
            'fields': ('generation_params',),
            'classes': ('collapse',)
        }),
        (_('时间信息'), {
            'fields': ('created_at',)
        }),
    )
    
    ordering = ('-created_at',)
    
    def content_preview(self, obj):
        """内容预览"""
        if obj.content:
            preview = obj.content[:300]
            if len(obj.content) > 300:
                preview += '...'
            return format_html('<div style="max-width: 400px; word-wrap: break-word;">{}</div>', preview)
        return _('无内容')
    content_preview.short_description = _('内容预览')
    
    def has_add_permission(self, request):
        return False
    
    def has_change_permission(self, request, obj=None):
        return False


@admin.register(TextExtraction)
class TextExtractionAdmin(ModelAdmin):
    """文案提取管理"""
    
    list_display = (
        'user', 'source_type', 'status', 'confidence_score',
        'computing_power_consumed', 'created_at', 'completed_at'
    )
    
    list_filter = (
        ('source_type', ChoicesDropdownFilter),
        ('status', ChoicesDropdownFilter),
        ('created_at', RangeDateFilter),
        ('completed_at', RangeDateFilter),
    )
    
    search_fields = ('user__username', 'extracted_text', 'source_url')
    
    readonly_fields = (
        'computing_power_consumed', 'created_at', 'updated_at',
        'completed_at', 'extracted_text_preview'
    )
    
    fieldsets = (
        (_('基本信息'), {
            'fields': ('user', 'source_type', 'source_file', 'source_url')
        }),
        (_('状态和结果'), {
            'fields': ('status', 'extracted_text', 'extracted_text_preview', 'confidence_score', 'error_message')
        }),
        (_('统计信息'), {
            'fields': ('computing_power_consumed',)
        }),
        (_('时间信息'), {
            'fields': ('created_at', 'updated_at', 'completed_at'),
            'classes': ('collapse',)
        }),
    )
    
    ordering = ('-created_at',)
    
    def extracted_text_preview(self, obj):
        """提取文本预览"""
        if obj.extracted_text:
            preview = obj.extracted_text[:300]
            if len(obj.extracted_text) > 300:
                preview += '...'
            return format_html('<div style="max-width: 400px; word-wrap: break-word;">{}</div>', preview)
        return _('无提取内容')
    extracted_text_preview.short_description = _('提取内容预览')
    
    def has_add_permission(self, request):
        return False
    
    def has_change_permission(self, request, obj=None):
        return False
