"""
视频生成管理后台配置
"""
from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from django.utils.html import format_html
from unfold.admin import ModelAdmin, TabularInline
from unfold.contrib.filters.admin import RangeDateFilter, ChoicesDropdownFilter

from .models import VideoProduction, VideoTemplate, VideoShare, RenderingTask


@admin.register(VideoProduction)
class VideoProductionAdmin(ModelAdmin):
    """视频作品管理"""
    
    list_display = (
        'title', 'user', 'avatar', 'voice_model', 'status', 'drive_mode',
        'video_quality', 'computing_power_consumed', 'view_count', 'created_at'
    )
    
    list_filter = (
        ('status', ChoicesDropdownFilter),
        ('drive_mode', ChoicesDropdownFilter),
        ('video_quality', ChoicesDropdownFilter),
        'is_public', 'is_favorite',
        ('created_at', RangeDateFilter),
        ('completed_at', RangeDateFilter),
    )
    
    search_fields = ('title', 'description', 'user__username', 'text_content')
    
    readonly_fields = (
        'computing_power_consumed', 'view_count', 'download_count',
        'created_at', 'updated_at', 'started_at', 'completed_at',
        'video_preview', 'text_preview'
    )
    
    fieldsets = (
        (_('基本信息'), {
            'fields': ('title', 'description', 'user', 'avatar', 'voice_model')
        }),
        (_('生成设置'), {
            'fields': ('drive_mode', 'text_preview', 'audio_file', 'audio_url')
        }),
        (_('语音参数'), {
            'fields': ('voice_volume', 'voice_pitch', 'voice_speed'),
            'classes': ('collapse',)
        }),
        (_('视频设置'), {
            'fields': ('video_quality', 'background_music', 'background_music_volume'),
            'classes': ('collapse',)
        }),
        (_('状态和结果'), {
            'fields': ('status', 'progress', 'error_message', 'result_video', 'result_video_url', 'video_preview')
        }),
        (_('统计信息'), {
            'fields': ('computing_power_consumed', 'view_count', 'download_count', 'is_public', 'is_favorite')
        }),
        (_('时间信息'), {
            'fields': ('created_at', 'updated_at', 'started_at', 'completed_at'),
            'classes': ('collapse',)
        }),
    )
    
    ordering = ('-created_at',)
    
    actions = ['approve_videos', 'reject_videos', 'make_public', 'make_private']
    
    def text_preview(self, obj):
        """文本预览"""
        if obj.text_content:
            preview = obj.text_content[:200]
            if len(obj.text_content) > 200:
                preview += '...'
            return format_html('<div style="max-width: 400px; word-wrap: break-word;">{}</div>', preview)
        return _('无文本')
    text_preview.short_description = _('文本内容')
    
    def video_preview(self, obj):
        """视频预览"""
        if obj.result_video:
            return format_html(
                '<video controls style="max-width: 300px; max-height: 200px;"><source src="{}" type="video/mp4"></video>',
                obj.result_video.url
            )
        elif obj.result_video_url:
            return format_html(
                '<video controls style="max-width: 300px; max-height: 200px;"><source src="{}" type="video/mp4"></video>',
                obj.result_video_url
            )
        elif obj.thumbnail:
            return format_html(
                '<img src="{}" style="max-width: 300px; max-height: 200px;" />',
                obj.thumbnail.url
            )
        return _('无视频')
    video_preview.short_description = _('视频预览')
    
    def approve_videos(self, request, queryset):
        """批量通过视频"""
        updated = queryset.filter(status='pending').update(status='processing')
        self.message_user(request, f'成功通过 {updated} 个视频')
    approve_videos.short_description = _('通过选中的视频')
    
    def reject_videos(self, request, queryset):
        """批量拒绝视频"""
        updated = queryset.filter(status='pending').update(status='cancelled')
        self.message_user(request, f'成功拒绝 {updated} 个视频')
    reject_videos.short_description = _('拒绝选中的视频')
    
    def make_public(self, request, queryset):
        """设为公开"""
        updated = queryset.update(is_public=True)
        self.message_user(request, f'成功将 {updated} 个视频设为公开')
    make_public.short_description = _('设为公开视频')
    
    def make_private(self, request, queryset):
        """设为私有"""
        updated = queryset.update(is_public=False)
        self.message_user(request, f'成功将 {updated} 个视频设为私有')
    make_private.short_description = _('设为私有视频')


@admin.register(VideoTemplate)
class VideoTemplateAdmin(ModelAdmin):
    """视频模板管理"""
    
    list_display = (
        'name', 'category', 'default_avatar', 'default_voice',
        'is_active', 'usage_count', 'sort_order', 'created_at'
    )
    
    list_filter = (
        ('category', ChoicesDropdownFilter),
        'is_active',
        ('created_at', RangeDateFilter),
    )
    
    search_fields = ('name', 'description', 'template_content')
    
    readonly_fields = ('usage_count', 'created_at', 'updated_at', 'template_preview')
    
    fieldsets = (
        (_('基本信息'), {
            'fields': ('name', 'description', 'category', 'thumbnail')
        }),
        (_('默认设置'), {
            'fields': ('default_avatar', 'default_voice', 'template_content', 'template_preview')
        }),
        (_('高级设置'), {
            'fields': ('template_settings',),
            'classes': ('collapse',)
        }),
        (_('状态和排序'), {
            'fields': ('is_active', 'sort_order', 'created_by')
        }),
        (_('统计信息'), {
            'fields': ('usage_count', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    ordering = ('sort_order', '-usage_count')
    
    actions = ['activate_templates', 'deactivate_templates']
    
    def template_preview(self, obj):
        """模板预览"""
        if obj.template_content:
            preview = obj.template_content[:300]
            if len(obj.template_content) > 300:
                preview += '...'
            return format_html('<div style="max-width: 400px; word-wrap: break-word;">{}</div>', preview)
        return _('无模板内容')
    template_preview.short_description = _('模板内容')
    
    def activate_templates(self, request, queryset):
        """批量启用模板"""
        updated = queryset.update(is_active=True)
        self.message_user(request, f'成功启用 {updated} 个模板')
    activate_templates.short_description = _('启用选中的模板')
    
    def deactivate_templates(self, request, queryset):
        """批量停用模板"""
        updated = queryset.update(is_active=False)
        self.message_user(request, f'成功停用 {updated} 个模板')
    deactivate_templates.short_description = _('停用选中的模板')


@admin.register(VideoShare)
class VideoShareAdmin(ModelAdmin):
    """视频分享管理"""
    
    list_display = (
        'video', 'share_token', 'has_password', 'view_count',
        'is_active', 'expire_at', 'created_at'
    )
    
    list_filter = (
        'is_active',
        ('expire_at', RangeDateFilter),
        ('created_at', RangeDateFilter),
    )
    
    search_fields = ('video__title', 'share_token')
    
    readonly_fields = ('share_token', 'view_count', 'created_at')
    
    fieldsets = (
        (_('基本信息'), {
            'fields': ('video', 'share_token')
        }),
        (_('访问控制'), {
            'fields': ('password', 'expire_at', 'is_active')
        }),
        (_('统计信息'), {
            'fields': ('view_count', 'created_at'),
            'classes': ('collapse',)
        }),
    )
    
    ordering = ('-created_at',)
    
    def has_password(self, obj):
        """是否有密码"""
        return bool(obj.password)
    has_password.boolean = True
    has_password.short_description = _('有密码')
    
    def has_add_permission(self, request):
        return False


@admin.register(RenderingTask)
class RenderingTaskAdmin(ModelAdmin):
    """渲染任务管理"""

    list_display = (
        'video', 'user', 'status', 'priority', 'progress_display',
        'computing_power_consumed', 'duration_display', 'created_at'
    )

    list_filter = (
        ('status', ChoicesDropdownFilter),
        ('priority', ChoicesDropdownFilter),
        ('created_at', RangeDateFilter),
        ('started_at', RangeDateFilter),
        ('completed_at', RangeDateFilter),
    )

    search_fields = ('video__title', 'user__username', 'user__phone')

    readonly_fields = (
        'progress', 'actual_duration', 'computing_power_consumed',
        'started_at', 'completed_at', 'created_at', 'updated_at'
    )

    fieldsets = (
        (_('基本信息'), {
            'fields': ('video', 'user', 'status', 'priority')
        }),
        (_('进度信息'), {
            'fields': ('progress', 'estimated_duration', 'actual_duration')
        }),
        (_('资源消耗'), {
            'fields': ('computing_power_consumed',)
        }),
        (_('错误信息'), {
            'fields': ('error_message',),
            'classes': ('collapse',)
        }),
        (_('时间信息'), {
            'fields': ('started_at', 'completed_at', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    ordering = ('-created_at',)

    actions = ['cancel_tasks', 'retry_failed_tasks']

    def progress_display(self, obj):
        """进度显示"""
        if obj.status == 'completed':
            return format_html(
                '<span style="color: green;">✓ 100%</span>'
            )
        elif obj.status == 'failed':
            return format_html(
                '<span style="color: red;">✗ 失败</span>'
            )
        elif obj.status == 'cancelled':
            return format_html(
                '<span style="color: orange;">⊘ 已取消</span>'
            )
        elif obj.status == 'processing':
            return format_html(
                '<div style="background: #f0f0f0; border-radius: 10px; overflow: hidden;">'
                '<div style="background: #007bff; height: 20px; width: {}%; color: white; '
                'text-align: center; line-height: 20px; font-size: 12px;">{}</div></div>',
                obj.progress, f'{obj.progress}%'
            )
        else:
            return format_html(
                '<span style="color: gray;">⏳ 等待中</span>'
            )
    progress_display.short_description = _('进度')

    def duration_display(self, obj):
        """时长显示"""
        return obj.duration_display
    duration_display.short_description = _('时长')

    def cancel_tasks(self, request, queryset):
        """批量取消任务"""
        cancelled_count = 0
        for task in queryset:
            if task.status in ['pending', 'processing']:
                task.cancel()
                cancelled_count += 1
        self.message_user(request, f'成功取消 {cancelled_count} 个任务')
    cancel_tasks.short_description = _('取消选中的任务')

    def retry_failed_tasks(self, request, queryset):
        """重试失败的任务"""
        retried_count = 0
        for task in queryset.filter(status='failed'):
            task.status = 'pending'
            task.progress = 0
            task.error_message = ''
            task.started_at = None
            task.completed_at = None
            task.save()
            retried_count += 1
        self.message_user(request, f'成功重试 {retried_count} 个失败任务')
    retry_failed_tasks.short_description = _('重试失败的任务')

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        # 只允许修改状态和错误信息
        return True

    def has_delete_permission(self, request, obj=None):
        return obj and obj.status in ['completed', 'failed', 'cancelled'] if obj else False
