"""
缓存工具和装饰器
"""
import hashlib
import json
import logging
from functools import wraps
from django.core.cache import cache
from django.conf import settings
from django.http import JsonResponse
from django.utils.cache import get_cache_key
from rest_framework.response import Response

logger = logging.getLogger(__name__)


class CacheManager:
    """缓存管理器"""
    
    # 缓存键前缀
    PREFIXES = {
        'api': 'api',
        'user': 'user',
        'avatar': 'avatar',
        'voice': 'voice',
        'video': 'video',
        'config': 'config',
        'stats': 'stats',
    }
    
    # 默认缓存时间（秒）
    DEFAULT_TIMEOUT = {
        'api': 300,      # 5分钟
        'user': 1800,    # 30分钟
        'avatar': 3600,  # 1小时
        'voice': 3600,   # 1小时
        'video': 1800,   # 30分钟
        'config': 7200,  # 2小时
        'stats': 600,    # 10分钟
    }
    
    @classmethod
    def make_key(cls, prefix, *args, **kwargs):
        """生成缓存键"""
        key_parts = [cls.PREFIXES.get(prefix, prefix)]
        
        # 添加位置参数
        for arg in args:
            key_parts.append(str(arg))
        
        # 添加关键字参数（排序确保一致性）
        if kwargs:
            sorted_kwargs = sorted(kwargs.items())
            for k, v in sorted_kwargs:
                key_parts.append(f"{k}:{v}")
        
        return ':'.join(key_parts)
    
    @classmethod
    def get(cls, prefix, *args, default=None, **kwargs):
        """获取缓存"""
        key = cls.make_key(prefix, *args, **kwargs)
        return cache.get(key, default)
    
    @classmethod
    def set(cls, prefix, *args, value=None, timeout=None, **kwargs):
        """设置缓存"""
        key = cls.make_key(prefix, *args, **kwargs)
        if timeout is None:
            timeout = cls.DEFAULT_TIMEOUT.get(prefix, 300)
        
        return cache.set(key, value, timeout)
    
    @classmethod
    def delete(cls, prefix, *args, **kwargs):
        """删除缓存"""
        key = cls.make_key(prefix, *args, **kwargs)
        return cache.delete(key)
    
    @classmethod
    def delete_pattern(cls, pattern):
        """删除匹配模式的缓存"""
        try:
            # 如果使用Redis，可以使用模式删除
            if hasattr(cache, 'delete_pattern'):
                return cache.delete_pattern(pattern)
            else:
                # 对于其他缓存后端，需要手动实现
                logger.warning(f"缓存后端不支持模式删除: {pattern}")
                return False
        except Exception as e:
            logger.error(f"删除缓存模式失败: {pattern}, 错误: {str(e)}")
            return False
    
    @classmethod
    def clear_user_cache(cls, user_id):
        """清除用户相关缓存"""
        patterns = [
            f"{cls.PREFIXES['user']}:{user_id}:*",
            f"{cls.PREFIXES['stats']}:user:{user_id}:*",
        ]
        
        for pattern in patterns:
            cls.delete_pattern(pattern)
    
    @classmethod
    def clear_avatar_cache(cls, avatar_id=None):
        """清除数字人相关缓存"""
        if avatar_id:
            patterns = [
                f"{cls.PREFIXES['avatar']}:{avatar_id}:*",
                f"{cls.PREFIXES['avatar']}:*:{avatar_id}:*",
            ]
        else:
            patterns = [
                f"{cls.PREFIXES['avatar']}:*",
            ]
        
        for pattern in patterns:
            cls.delete_pattern(pattern)


def cache_response(prefix, timeout=None, key_func=None, condition=None):
    """
    API响应缓存装饰器
    
    Args:
        prefix: 缓存键前缀
        timeout: 缓存超时时间（秒）
        key_func: 自定义键生成函数
        condition: 缓存条件函数
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 检查是否满足缓存条件
            if condition and not condition(*args, **kwargs):
                return func(*args, **kwargs)
            
            # 生成缓存键
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                # 默认键生成逻辑
                request = args[1] if len(args) > 1 else None
                if request:
                    key_parts = [
                        request.method,
                        request.path,
                        str(sorted(request.GET.items())),
                    ]
                    if hasattr(request, 'user') and request.user.is_authenticated:
                        key_parts.append(f"user:{request.user.id}")
                    
                    cache_key = CacheManager.make_key(prefix, *key_parts)
                else:
                    cache_key = CacheManager.make_key(prefix, func.__name__, str(args), str(kwargs))
            
            # 尝试从缓存获取
            cached_response = cache.get(cache_key)
            if cached_response is not None:
                logger.debug(f"缓存命中: {cache_key}")
                return cached_response
            
            # 执行原函数
            response = func(*args, **kwargs)
            
            # 缓存响应（只缓存成功的响应）
            if isinstance(response, Response) and response.status_code == 200:
                cache_timeout = timeout or CacheManager.DEFAULT_TIMEOUT.get(prefix, 300)
                cache.set(cache_key, response, cache_timeout)
                logger.debug(f"缓存设置: {cache_key}, 超时: {cache_timeout}秒")
            
            return response
        
        return wrapper
    return decorator


def cache_result(prefix, timeout=None, key_func=None):
    """
    函数结果缓存装饰器
    
    Args:
        prefix: 缓存键前缀
        timeout: 缓存超时时间（秒）
        key_func: 自定义键生成函数
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 生成缓存键
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                # 默认键生成逻辑
                key_parts = [func.__name__]
                
                # 处理位置参数
                for arg in args:
                    if hasattr(arg, 'id'):
                        key_parts.append(f"id:{arg.id}")
                    else:
                        key_parts.append(str(arg))
                
                # 处理关键字参数
                for k, v in sorted(kwargs.items()):
                    key_parts.append(f"{k}:{v}")
                
                cache_key = CacheManager.make_key(prefix, *key_parts)
            
            # 尝试从缓存获取
            cached_result = cache.get(cache_key)
            if cached_result is not None:
                logger.debug(f"缓存命中: {cache_key}")
                return cached_result
            
            # 执行原函数
            result = func(*args, **kwargs)
            
            # 缓存结果
            cache_timeout = timeout or CacheManager.DEFAULT_TIMEOUT.get(prefix, 300)
            cache.set(cache_key, result, cache_timeout)
            logger.debug(f"缓存设置: {cache_key}, 超时: {cache_timeout}秒")
            
            return result
        
        return wrapper
    return decorator


def invalidate_cache(prefix, *args, **kwargs):
    """
    缓存失效装饰器
    在函数执行后清除相关缓存
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*wrapper_args, **wrapper_kwargs):
            # 执行原函数
            result = func(*wrapper_args, **wrapper_kwargs)
            
            # 清除缓存
            try:
                if args or kwargs:
                    # 删除指定缓存
                    CacheManager.delete(prefix, *args, **kwargs)
                else:
                    # 删除前缀相关的所有缓存
                    pattern = f"{CacheManager.PREFIXES.get(prefix, prefix)}:*"
                    CacheManager.delete_pattern(pattern)
                
                logger.debug(f"缓存已清除: {prefix}")
            except Exception as e:
                logger.error(f"清除缓存失败: {str(e)}")
            
            return result
        
        return wrapper
    return decorator


class CacheMiddleware:
    """缓存中间件"""
    
    def __init__(self, get_response):
        self.get_response = get_response
    
    def __call__(self, request):
        # 检查是否需要缓存
        if self.should_cache_request(request):
            cache_key = self.get_cache_key(request)
            cached_response = cache.get(cache_key)
            
            if cached_response:
                logger.debug(f"中间件缓存命中: {cache_key}")
                return cached_response
        
        response = self.get_response(request)
        
        # 缓存响应
        if self.should_cache_response(request, response):
            cache_key = self.get_cache_key(request)
            cache.set(cache_key, response, 300)  # 5分钟
            logger.debug(f"中间件缓存设置: {cache_key}")
        
        return response
    
    def should_cache_request(self, request):
        """判断是否应该缓存请求"""
        # 只缓存GET请求
        if request.method != 'GET':
            return False
        
        # 只缓存API请求
        if not request.path.startswith('/api/'):
            return False
        
        # 不缓存需要认证的请求
        if request.user.is_authenticated:
            return False
        
        return True
    
    def should_cache_response(self, request, response):
        """判断是否应该缓存响应"""
        # 只缓存成功的响应
        if response.status_code != 200:
            return False
        
        # 只缓存JSON响应
        content_type = response.get('Content-Type', '')
        if 'application/json' not in content_type:
            return False
        
        return True
    
    def get_cache_key(self, request):
        """生成缓存键"""
        key_parts = [
            'middleware',
            request.method,
            request.path,
            str(sorted(request.GET.items())),
        ]
        
        return ':'.join(key_parts)


# 全局缓存管理器实例
cache_manager = CacheManager()
