# Generated by Django 4.2.7 on 2025-01-19 12:00

from django.db import migrations


def add_baidu_xiling_configs(apps, schema_editor):
    """添加百度曦灵数字人服务配置"""
    SystemConfig = apps.get_model('config', 'SystemConfig')
    
    configs = [
        {
            'key': 'baidu_xiling_api_key',
            'name': '百度曦灵API Key',
            'description': '百度智能云曦灵数字人开放平台的API Key，用于身份验证',
            'config_type': 'ai_service',
            'value': '',
            'value_type': 'string',
            'default_value': '',
            'is_active': True,
            'is_sensitive': True,
            'is_readonly': False,
            'sort_order': 30,
            'validation_rule': '{"required": true, "min_length": 10}'
        },
        {
            'key': 'baidu_xiling_secret_key',
            'name': '百度曦灵Secret Key',
            'description': '百度智能云曦灵数字人开放平台的Secret Key，用于签名验证',
            'config_type': 'ai_service',
            'value': '',
            'value_type': 'string',
            'default_value': '',
            'is_active': True,
            'is_sensitive': True,
            'is_readonly': False,
            'sort_order': 31,
            'validation_rule': '{"required": true, "min_length": 10}'
        },
        {
            'key': 'baidu_xiling_base_url',
            'name': '百度曦灵API基础URL',
            'description': '百度曦灵数字人服务的API基础URL地址',
            'config_type': 'ai_service',
            'value': 'https://xiling.cloud.baidu.com/api/v1',
            'value_type': 'string',
            'default_value': 'https://xiling.cloud.baidu.com/api/v1',
            'is_active': True,
            'is_sensitive': False,
            'is_readonly': False,
            'sort_order': 32,
            'validation_rule': '{"required": true, "pattern": "^https?://"}'
        },
        {
            'key': 'digital_human_provider',
            'name': '数字人服务提供商',
            'description': '选择默认的数字人服务提供商',
            'config_type': 'ai_service',
            'value': 'baidu_xiling',
            'value_type': 'string',
            'default_value': 'baidu_xiling',
            'is_active': True,
            'is_sensitive': False,
            'is_readonly': False,
            'sort_order': 33,
            'validation_rule': '{"choices": ["heygen", "baidu_xiling", "did"]}'
        },
        {
            'key': 'avatar_clone_max_wait_time',
            'name': '形象克隆最大等待时间',
            'description': '形象克隆任务的最大等待时间（秒），超时后任务会被标记为失败',
            'config_type': 'ai_service',
            'value': '600',
            'value_type': 'integer',
            'default_value': '600',
            'is_active': True,
            'is_sensitive': False,
            'is_readonly': False,
            'sort_order': 34,
            'validation_rule': '{"min": 300, "max": 1800}'
        }
    ]
    
    for config_data in configs:
        SystemConfig.objects.get_or_create(
            key=config_data['key'],
            defaults=config_data
        )


def remove_baidu_xiling_configs(apps, schema_editor):
    """移除百度曦灵数字人服务配置"""
    SystemConfig = apps.get_model('config', 'SystemConfig')
    
    config_keys = [
        'baidu_xiling_api_key',
        'baidu_xiling_secret_key',
        'baidu_xiling_base_url',
        'digital_human_provider',
        'avatar_clone_max_wait_time'
    ]
    
    SystemConfig.objects.filter(key__in=config_keys).delete()


class Migration(migrations.Migration):

    dependencies = [
        ('config', '0001_initial'),
    ]

    operations = [
        migrations.RunPython(add_baidu_xiling_configs, remove_baidu_xiling_configs),
    ]
