"""
文件上传序列化器
"""
from rest_framework import serializers
from .models import UploadFile, ChunkedUpload


class UploadFileSerializer(serializers.ModelSerializer):
    """上传文件序列化器"""
    
    file_url = serializers.ReadOnlyField()
    upload_type_display = serializers.CharField(source='get_upload_type_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    file_size_mb = serializers.SerializerMethodField()
    
    class Meta:
        model = UploadFile
        fields = [
            'id', 'upload_type', 'upload_type_display', 'original_filename',
            'file_url', 'file_size', 'file_size_mb', 'file_type', 'file_hash',
            'status', 'status_display', 'progress', 'error_message',
            'metadata', 'created_at', 'completed_at'
        ]
        read_only_fields = [
            'id', 'file_hash', 'created_at', 'completed_at'
        ]
    
    def get_file_size_mb(self, obj):
        """获取文件大小（MB）"""
        if obj.file_size:
            return round(obj.file_size / (1024 * 1024), 2)
        return 0


class ChunkedUploadSerializer(serializers.ModelSerializer):
    """分块上传序列化器"""
    
    progress_percentage = serializers.ReadOnlyField()
    upload_type_display = serializers.CharField(source='get_upload_type_display', read_only=True)
    total_size_mb = serializers.SerializerMethodField()
    uploaded_size_mb = serializers.SerializerMethodField()
    
    class Meta:
        model = ChunkedUpload
        fields = [
            'upload_id', 'filename', 'total_size', 'total_size_mb',
            'chunk_size', 'uploaded_size', 'uploaded_size_mb',
            'upload_type', 'upload_type_display', 'progress_percentage',
            'is_completed', 'created_at', 'expires_at'
        ]
        read_only_fields = [
            'upload_id', 'uploaded_size', 'progress_percentage',
            'is_completed', 'created_at'
        ]
    
    def get_total_size_mb(self, obj):
        """获取总大小（MB）"""
        return round(obj.total_size / (1024 * 1024), 2)
    
    def get_uploaded_size_mb(self, obj):
        """获取已上传大小（MB）"""
        return round(obj.uploaded_size / (1024 * 1024), 2)
