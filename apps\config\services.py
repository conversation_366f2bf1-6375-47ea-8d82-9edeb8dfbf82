"""
第三方API配置服务
"""
from typing import Optional, Dict, Any
from django.core.cache import cache
from .models import SystemConfig


class ThirdPartyAPIConfig:
    """第三方API配置管理器"""
    
    CACHE_PREFIX = 'api_config_'
    CACHE_TIMEOUT = 3600  # 1小时
    
    @classmethod
    def get_config(cls, key: str, default: Any = None) -> Any:
        """获取配置值"""
        cache_key = f"{cls.CACHE_PREFIX}{key}"
        value = cache.get(cache_key)
        
        if value is None:
            try:
                config = SystemConfig.objects.get(key=key, is_active=True)
                value = config.get_value()
                cache.set(cache_key, value, cls.CACHE_TIMEOUT)
            except SystemConfig.DoesNotExist:
                value = default
        
        return value
    
    @classmethod
    def set_config(cls, key: str, value: Any) -> bool:
        """设置配置值"""
        try:
            config = SystemConfig.objects.get(key=key)
            config.set_value(value)
            config.save()
            
            # 清除缓存
            cache_key = f"{cls.CACHE_PREFIX}{key}"
            cache.delete(cache_key)
            
            return True
        except SystemConfig.DoesNotExist:
            return False
    
    @classmethod
    def clear_cache(cls, key: str = None):
        """清除配置缓存"""
        if key:
            cache_key = f"{cls.CACHE_PREFIX}{key}"
            cache.delete(cache_key)
        else:
            # 清除所有配置缓存
            cache.delete_many([
                f"{cls.CACHE_PREFIX}{config.key}" 
                for config in SystemConfig.objects.all()
            ])
    
    # ==================== AI服务配置 ====================
    
    @classmethod
    def get_openai_config(cls) -> Dict[str, Any]:
        """获取OpenAI配置"""
        return {
            'api_key': cls.get_config('openai_api_key', ''),
            'model': cls.get_config('openai_model', 'gpt-4'),
            'max_tokens': cls.get_config('openai_max_tokens', 2000),
            'base_url': cls.get_config('ai_api_base_url', 'https://api.openai.com/v1'),
        }
    
    @classmethod
    def get_azure_speech_config(cls) -> Dict[str, Any]:
        """获取Azure语音服务配置"""
        return {
            'api_key': cls.get_config('azure_speech_key', ''),
            'region': cls.get_config('azure_speech_region', 'eastasia'),
        }
    
    @classmethod
    def get_elevenlabs_config(cls) -> Dict[str, Any]:
        """获取ElevenLabs配置"""
        return {
            'api_key': cls.get_config('elevenlabs_api_key', ''),
        }
    
    @classmethod
    def get_dalle_config(cls) -> Dict[str, Any]:
        """获取DALL-E配置"""
        return {
            'api_key': cls.get_config('dalle_api_key', ''),
        }
    
    @classmethod
    def get_heygen_config(cls) -> Dict[str, Any]:
        """获取HeyGen配置"""
        return {
            'api_key': cls.get_config('heygen_api_key', ''),
        }
    
    @classmethod
    def get_did_config(cls) -> Dict[str, Any]:
        """获取D-ID配置"""
        return {
            'api_key': cls.get_config('did_api_key', ''),
        }
    
    @classmethod
    def get_runwayml_config(cls) -> Dict[str, Any]:
        """获取RunwayML配置"""
        return {
            'api_key': cls.get_config('runwayml_api_key', ''),
        }
    
    @classmethod
    def get_suno_config(cls) -> Dict[str, Any]:
        """获取Suno AI配置"""
        return {
            'api_key': cls.get_config('suno_api_key', ''),
        }
    
    @classmethod
    def get_facepp_config(cls) -> Dict[str, Any]:
        """获取Face++配置"""
        return {
            'api_key': cls.get_config('facepp_api_key', ''),
            'api_secret': cls.get_config('facepp_api_secret', ''),
        }
    
    # ==================== 存储服务配置 ====================
    
    @classmethod
    def get_aliyun_oss_config(cls) -> Dict[str, Any]:
        """获取阿里云OSS配置"""
        return {
            'access_key': cls.get_config('aliyun_oss_access_key', ''),
            'secret_key': cls.get_config('aliyun_oss_secret_key', ''),
            'bucket': cls.get_config('aliyun_oss_bucket', ''),
            'endpoint': cls.get_config('aliyun_oss_endpoint', ''),
        }
    
    # ==================== 支付服务配置 ====================
    
    @classmethod
    def get_alipay_config(cls) -> Dict[str, Any]:
        """获取支付宝配置"""
        return {
            'app_id': cls.get_config('alipay_app_id', ''),
            'private_key': cls.get_config('alipay_private_key', ''),
            'public_key': cls.get_config('alipay_public_key', ''),
        }
    
    @classmethod
    def get_wechat_pay_config(cls) -> Dict[str, Any]:
        """获取微信支付配置"""
        return {
            'mch_id': cls.get_config('wechat_pay_mch_id', ''),
            'api_key': cls.get_config('wechat_pay_api_key', ''),
        }
    
    # ==================== 通知服务配置 ====================
    
    @classmethod
    def get_aliyun_sms_config(cls) -> Dict[str, Any]:
        """获取阿里云短信配置"""
        return {
            'access_key': cls.get_config('aliyun_sms_access_key', ''),
            'secret_key': cls.get_config('aliyun_sms_secret_key', ''),
            'sign_name': cls.get_config('aliyun_sms_sign_name', ''),
        }
    
    @classmethod
    def get_sendgrid_config(cls) -> Dict[str, Any]:
        """获取SendGrid配置"""
        return {
            'api_key': cls.get_config('sendgrid_api_key', ''),
        }
    
    # ==================== 配置验证 ====================
    
    @classmethod
    def validate_openai_config(cls) -> bool:
        """验证OpenAI配置是否完整"""
        config = cls.get_openai_config()
        return bool(config['api_key'])
    
    @classmethod
    def validate_azure_speech_config(cls) -> bool:
        """验证Azure语音配置是否完整"""
        config = cls.get_azure_speech_config()
        return bool(config['api_key'] and config['region'])
    
    @classmethod
    def validate_aliyun_oss_config(cls) -> bool:
        """验证阿里云OSS配置是否完整"""
        config = cls.get_aliyun_oss_config()
        return bool(
            config['access_key'] and 
            config['secret_key'] and 
            config['bucket'] and 
            config['endpoint']
        )
    
    @classmethod
    def get_heygen_config(cls) -> Dict[str, Any]:
        """获取HeyGen配置"""
        return {
            'api_key': cls.get_config('heygen_api_key', ''),
            'base_url': cls.get_config('heygen_base_url', 'https://api.heygen.com/v2')
        }

    @classmethod
    def get_did_config(cls) -> Dict[str, Any]:
        """获取D-ID配置"""
        return {
            'api_key': cls.get_config('did_api_key', ''),
            'base_url': cls.get_config('did_base_url', 'https://api.d-id.com')
        }

    @classmethod
    def get_elevenlabs_config(cls) -> Dict[str, Any]:
        """获取ElevenLabs配置"""
        return {
            'api_key': cls.get_config('elevenlabs_api_key', ''),
            'base_url': cls.get_config('elevenlabs_base_url', 'https://api.elevenlabs.io/v1')
        }

    @classmethod
    def get_aws_s3_config(cls) -> Dict[str, Any]:
        """获取AWS S3配置"""
        return {
            'access_key': cls.get_config('aws_s3_access_key', ''),
            'secret_key': cls.get_config('aws_s3_secret_key', ''),
            'bucket': cls.get_config('aws_s3_bucket', ''),
            'region': cls.get_config('aws_s3_region', 'us-east-1')
        }

    @classmethod
    def get_tencent_sms_config(cls) -> Dict[str, Any]:
        """获取腾讯云短信配置"""
        return {
            'secret_id': cls.get_config('tencent_sms_secret_id', ''),
            'secret_key': cls.get_config('tencent_sms_secret_key', ''),
            'app_id': cls.get_config('tencent_sms_app_id', ''),
            'sign_name': cls.get_config('tencent_sms_sign_name', ''),
            'region': cls.get_config('tencent_sms_region', 'ap-beijing')
        }

    @classmethod
    def get_missing_configs(cls) -> Dict[str, list]:
        """获取缺失的配置项"""
        missing = {
            'ai_services': [],
            'storage': [],
            'payment': [],
            'notification': []
        }

        # 检查AI服务
        if not cls.validate_openai_config():
            missing['ai_services'].append('OpenAI API配置')
        if not cls.validate_azure_speech_config():
            missing['ai_services'].append('Azure语音服务配置')

        heygen_config = cls.get_heygen_config()
        if not heygen_config['api_key']:
            missing['ai_services'].append('HeyGen数字人配置')

        did_config = cls.get_did_config()
        if not did_config['api_key']:
            missing['ai_services'].append('D-ID数字人配置')

        elevenlabs_config = cls.get_elevenlabs_config()
        if not elevenlabs_config['api_key']:
            missing['ai_services'].append('ElevenLabs语音配置')

        # 检查存储服务
        if not cls.validate_aliyun_oss_config():
            missing['storage'].append('阿里云OSS配置')

        aws_config = cls.get_aws_s3_config()
        if not (aws_config['access_key'] and aws_config['secret_key']):
            missing['storage'].append('AWS S3配置')

        # 检查支付服务
        alipay_config = cls.get_alipay_config()
        if not (alipay_config['app_id'] and alipay_config['private_key']):
            missing['payment'].append('支付宝配置')

        wechat_config = cls.get_wechat_pay_config()
        if not (wechat_config['mch_id'] and wechat_config['api_key']):
            missing['payment'].append('微信支付配置')

        # 检查通知服务
        sms_config = cls.get_aliyun_sms_config()
        if not (sms_config['access_key'] and sms_config['secret_key']):
            missing['notification'].append('阿里云短信配置')

        tencent_sms_config = cls.get_tencent_sms_config()
        if not (tencent_sms_config['secret_id'] and tencent_sms_config['secret_key']):
            missing['notification'].append('腾讯云短信配置')

        return missing


# 创建全局实例
api_config = ThirdPartyAPIConfig()
