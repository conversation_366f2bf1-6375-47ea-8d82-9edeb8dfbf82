"""
CSRF豁免中间件
"""
import re
from django.conf import settings
from django.utils.deprecation import MiddlewareMixin


class CSRFExemptMiddleware(MiddlewareMixin):
    """
    CSRF豁免中间件
    对指定的URL模式豁免CSRF检查
    """
    
    def process_request(self, request):
        """处理请求"""
        # 获取豁免URL模式
        exempt_urls = getattr(settings, 'CSRF_EXEMPT_URLS', [])
        
        if exempt_urls:
            path = request.path_info
            
            # 检查是否匹配豁免模式
            for pattern in exempt_urls:
                if re.match(pattern, path):
                    # 设置CSRF豁免标记
                    setattr(request, '_dont_enforce_csrf_checks', True)
                    break
        
        return None
