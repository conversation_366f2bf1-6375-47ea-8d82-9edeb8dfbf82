# Generated by Django 5.2.1 on 2025-07-16 11:24

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("voices", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="VoiceCategory",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(max_length=50, unique=True, verbose_name="分类名称"),
                ),
                ("description", models.TextField(blank=True, verbose_name="分类描述")),
                (
                    "icon",
                    models.Char<PERSON>ield(
                        blank=True,
                        help_text="Material Icons图标名称",
                        max_length=50,
                        verbose_name="图标",
                    ),
                ),
                ("sort_order", models.IntegerField(default=0, verbose_name="排序")),
                ("is_active", models.BooleanField(default=True, verbose_name="是否启用")),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
            ],
            options={
                "verbose_name": "语音分类",
                "verbose_name_plural": "语音分类",
                "ordering": ["sort_order", "name"],
            },
        ),
        migrations.CreateModel(
            name="VoiceTag",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(max_length=30, unique=True, verbose_name="标签名称"),
                ),
                (
                    "color",
                    models.CharField(
                        default="#007bff",
                        help_text="十六进制颜色值",
                        max_length=7,
                        verbose_name="标签颜色",
                    ),
                ),
                ("usage_count", models.IntegerField(default=0, verbose_name="使用次数")),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
            ],
            options={
                "verbose_name": "语音标签",
                "verbose_name_plural": "语音标签",
                "ordering": ["-usage_count", "name"],
            },
        ),
        migrations.AddField(
            model_name="voicemodel",
            name="categories",
            field=models.ManyToManyField(
                blank=True,
                help_text="语音模型所属的分类",
                related_name="voices",
                to="voices.voicecategory",
                verbose_name="分类",
            ),
        ),
        migrations.AddField(
            model_name="voicemodel",
            name="tags",
            field=models.ManyToManyField(
                blank=True,
                help_text="语音模型的标签",
                related_name="voices",
                to="voices.voicetag",
                verbose_name="标签",
            ),
        ),
    ]
