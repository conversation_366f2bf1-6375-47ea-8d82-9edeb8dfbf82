"""
语音系统异步任务
"""
from celery import shared_task
from django.utils import timezone
from .models import VoiceCloneRequest, VoiceSynthesisTask, VoiceModel
import logging

logger = logging.getLogger(__name__)


@shared_task(bind=True, max_retries=3)
def process_voice_clone(self, clone_request_id):
    """处理语音克隆请求"""
    try:
        clone_request = VoiceCloneRequest.objects.get(id=clone_request_id)
        
        # 更新状态为处理中
        clone_request.status = 'processing'
        clone_request.progress = 10
        clone_request.save(update_fields=['status', 'progress'])
        
        # 模拟处理过程
        import time
        for progress in [25, 50, 75, 90]:
            time.sleep(2)  # 模拟处理时间
            clone_request.progress = progress
            clone_request.save(update_fields=['progress'])
        
        # 创建克隆后的语音模型
        cloned_voice = VoiceModel.objects.create(
            name=f"{clone_request.name}（克隆）",
            description=f"基于用户上传音频克隆的语音：{clone_request.description}",
            language='zh-cn',  # 默认中文
            gender='female',   # 默认女声，实际应该从音频分析
            voice_type='standard',
            is_cloned=True,
            is_public=False,
            owner=clone_request.user,
            computing_power_cost=8,
            # 这里应该设置实际的音频文件
        )
        
        # 更新克隆请求状态
        clone_request.status = 'completed'
        clone_request.progress = 100
        clone_request.result_voice = cloned_voice
        clone_request.completed_at = timezone.now()
        clone_request.save(update_fields=[
            'status', 'progress', 'result_voice', 'completed_at'
        ])
        
        logger.info(f"语音克隆完成: {clone_request.name}")
        return f"语音克隆完成: {cloned_voice.name}"
        
    except VoiceCloneRequest.DoesNotExist:
        logger.error(f"语音克隆请求不存在: {clone_request_id}")
        return f"语音克隆请求不存在: {clone_request_id}"
    
    except Exception as exc:
        logger.error(f"语音克隆失败: {exc}")
        
        # 更新失败状态
        try:
            clone_request = VoiceCloneRequest.objects.get(id=clone_request_id)
            clone_request.status = 'failed'
            clone_request.error_message = str(exc)
            clone_request.save(update_fields=['status', 'error_message'])
        except:
            pass
        
        # 重试任务
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=60, exc=exc)
        
        return f"语音克隆失败: {exc}"


@shared_task(bind=True, max_retries=3)
def synthesize_voice(self, synthesis_task_id):
    """处理语音合成任务"""
    try:
        synthesis_task = VoiceSynthesisTask.objects.get(id=synthesis_task_id)
        
        # 更新状态为处理中
        synthesis_task.status = 'processing'
        synthesis_task.progress = 10
        synthesis_task.save(update_fields=['status', 'progress'])
        
        # 模拟合成过程
        import time
        text_length = len(synthesis_task.text_content)
        
        for progress in [25, 50, 75, 90]:
            time.sleep(1)  # 模拟处理时间
            synthesis_task.progress = progress
            synthesis_task.save(update_fields=['progress'])
        
        # 估算音频时长（按每分钟180字计算）
        estimated_duration = max(text_length / 3, 5)  # 最少5秒
        
        # 模拟生成音频文件URL
        result_audio_url = f"https://example.com/synthesized_audio_{synthesis_task_id}.mp3"
        
        # 更新合成任务状态
        synthesis_task.status = 'completed'
        synthesis_task.progress = 100
        synthesis_task.result_audio_url = result_audio_url
        synthesis_task.audio_duration = int(estimated_duration)
        synthesis_task.completed_at = timezone.now()
        synthesis_task.save(update_fields=[
            'status', 'progress', 'result_audio_url', 
            'audio_duration', 'completed_at'
        ])
        
        # 增加语音模型使用次数
        synthesis_task.voice_model.increment_usage()
        
        logger.info(f"语音合成完成: {synthesis_task.voice_model.name}")
        return f"语音合成完成: {result_audio_url}"
        
    except VoiceSynthesisTask.DoesNotExist:
        logger.error(f"语音合成任务不存在: {synthesis_task_id}")
        return f"语音合成任务不存在: {synthesis_task_id}"
    
    except Exception as exc:
        logger.error(f"语音合成失败: {exc}")
        
        # 更新失败状态
        try:
            synthesis_task = VoiceSynthesisTask.objects.get(id=synthesis_task_id)
            synthesis_task.status = 'failed'
            synthesis_task.error_message = str(exc)
            synthesis_task.save(update_fields=['status', 'error_message'])
        except:
            pass
        
        # 重试任务
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=30, exc=exc)
        
        return f"语音合成失败: {exc}"


@shared_task
def cleanup_expired_synthesis_tasks():
    """清理过期的合成任务"""
    from datetime import timedelta
    
    # 删除7天前的已完成任务
    expired_date = timezone.now() - timedelta(days=7)
    expired_tasks = VoiceSynthesisTask.objects.filter(
        status='completed',
        completed_at__lt=expired_date
    )
    
    count = expired_tasks.count()
    expired_tasks.delete()
    
    logger.info(f"清理了 {count} 个过期的语音合成任务")
    return f"清理了 {count} 个过期的语音合成任务"


@shared_task
def update_voice_model_usage_stats():
    """更新语音模型使用统计"""
    from django.db.models import Count
    
    # 统计每个语音模型的使用次数
    voice_stats = VoiceSynthesisTask.objects.filter(
        status='completed'
    ).values('voice_model').annotate(
        usage_count=Count('id')
    )
    
    updated_count = 0
    for stat in voice_stats:
        VoiceModel.objects.filter(
            id=stat['voice_model']
        ).update(
            usage_count=stat['usage_count']
        )
        updated_count += 1
    
    logger.info(f"更新了 {updated_count} 个语音模型的使用统计")
    return f"更新了 {updated_count} 个语音模型的使用统计"
