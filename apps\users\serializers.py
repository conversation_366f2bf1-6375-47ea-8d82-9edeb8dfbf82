"""
用户相关序列化器
"""
from rest_framework import serializers
from django.contrib.auth import authenticate
from django.utils.translation import gettext_lazy as _
from .models import User, ComputingPowerLog, UserPackage


class UserSerializer(serializers.ModelSerializer):
    """用户序列化器"""
    
    class Meta:
        model = User
        fields = (
            'id', 'username', 'email', 'phone', 'first_name', 'last_name',
            'avatar', 'computing_power', 'total_computing_power', 'package_type',
            'package_expire_date', 'is_verified', 'date_joined'
        )
        read_only_fields = ('id', 'total_computing_power', 'date_joined')


class UserProfileSerializer(serializers.ModelSerializer):
    """用户资料序列化器"""
    
    class Meta:
        model = User
        fields = (
            'id', 'username', 'email', 'phone', 'first_name', 'last_name',
            'avatar', 'computing_power', 'package_type', 'package_expire_date'
        )
        read_only_fields = ('id', 'computing_power', 'package_type', 'package_expire_date')


class LoginSerializer(serializers.Serializer):
    """登录序列化器"""
    username = serializers.CharField()
    password = serializers.CharField(write_only=True)
    
    def validate(self, attrs):
        username = attrs.get('username')
        password = attrs.get('password')
        
        if username and password:
            # 支持用户名、邮箱、手机号登录
            user = None
            if '@' in username:
                # 邮箱登录
                try:
                    user_obj = User.objects.get(email=username)
                    user = authenticate(username=user_obj.username, password=password)
                except User.DoesNotExist:
                    pass
            elif username.isdigit():
                # 手机号登录
                try:
                    user_obj = User.objects.get(phone=username)
                    user = authenticate(username=user_obj.username, password=password)
                except User.DoesNotExist:
                    pass
            else:
                # 用户名登录
                user = authenticate(username=username, password=password)
            
            if not user:
                raise serializers.ValidationError(_('用户名或密码错误'))
            
            if not user.is_active:
                raise serializers.ValidationError(_('用户账户已被禁用'))
            
            attrs['user'] = user
            return attrs
        else:
            raise serializers.ValidationError(_('必须提供用户名和密码'))


class RegisterSerializer(serializers.ModelSerializer):
    """注册序列化器"""
    password = serializers.CharField(write_only=True, min_length=8)
    password_confirm = serializers.CharField(write_only=True)
    
    class Meta:
        model = User
        fields = ('username', 'email', 'phone', 'password', 'password_confirm')
    
    def validate(self, attrs):
        if attrs['password'] != attrs['password_confirm']:
            raise serializers.ValidationError(_('两次输入的密码不一致'))
        return attrs
    
    def validate_username(self, value):
        if User.objects.filter(username=value).exists():
            raise serializers.ValidationError(_('用户名已存在'))
        return value
    
    def validate_email(self, value):
        if value and User.objects.filter(email=value).exists():
            raise serializers.ValidationError(_('邮箱已被注册'))
        return value
    
    def validate_phone(self, value):
        if value and User.objects.filter(phone=value).exists():
            raise serializers.ValidationError(_('手机号已被注册'))
        return value
    
    def create(self, validated_data):
        validated_data.pop('password_confirm')
        password = validated_data.pop('password')
        user = User.objects.create_user(**validated_data)
        user.set_password(password)
        user.save()
        
        # 新用户赠送初始算力
        user.add_computing_power(100, '新用户注册赠送')
        
        return user


class ComputingPowerLogSerializer(serializers.ModelSerializer):
    """算力日志序列化器"""
    user_display = serializers.CharField(source='user.display_name', read_only=True)
    
    class Meta:
        model = ComputingPowerLog
        fields = (
            'id', 'user', 'user_display', 'amount', 'balance_after',
            'operation_type', 'reason', 'created_at'
        )
        read_only_fields = ('id', 'created_at')


class UserPackageSerializer(serializers.ModelSerializer):
    """用户套餐序列化器"""
    user_display = serializers.CharField(source='user.display_name', read_only=True)
    package_type_display = serializers.CharField(source='get_package_type_display', read_only=True)
    
    class Meta:
        model = UserPackage
        fields = (
            'id', 'user', 'user_display', 'package_type', 'package_type_display',
            'duration_days', 'computing_power_included', 'price',
            'start_date', 'end_date', 'is_active', 'created_at'
        )
        read_only_fields = ('id', 'created_at')
