"""
自定义中间件
"""
import time
import json
import logging
from django.utils.deprecation import MiddlewareMixin
from django.http import JsonResponse
from django.core.cache import cache
from django.conf import settings

logger = logging.getLogger('apps')


class APILoggingMiddleware(MiddlewareMixin):
    """API请求日志中间件"""
    
    def process_request(self, request):
        """处理请求"""
        # 记录请求开始时间
        request._start_time = time.time()
        
        # 记录请求信息
        if request.path.startswith('/api/'):
            logger.info(
                f"API请求开始 - {request.method} {request.path} - "
                f"用户: {getattr(request.user, 'username', 'Anonymous')} - "
                f"IP: {self.get_client_ip(request)}"
            )
    
    def process_response(self, request, response):
        """处理响应"""
        if hasattr(request, '_start_time') and request.path.startswith('/api/'):
            # 计算请求耗时
            duration = time.time() - request._start_time
            
            # 记录响应信息
            logger.info(
                f"API请求完成 - {request.method} {request.path} - "
                f"状态码: {response.status_code} - "
                f"耗时: {duration:.3f}s - "
                f"用户: {getattr(request.user, 'username', 'Anonymous')}"
            )
        
        return response
    
    def get_client_ip(self, request):
        """获取客户端IP"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class RateLimitMiddleware(MiddlewareMixin):
    """API频率限制中间件"""
    
    def process_request(self, request):
        """处理请求"""
        if not request.path.startswith('/api/'):
            return None
        
        # 获取客户端标识
        client_id = self.get_client_id(request)
        
        # 检查频率限制
        if self.is_rate_limited(client_id, request):
            return JsonResponse({
                'error': True,
                'message': '请求过于频繁，请稍后重试',
                'status_code': 429
            }, status=429)
        
        return None
    
    def get_client_id(self, request):
        """获取客户端标识"""
        if request.user.is_authenticated:
            return f"user_{request.user.id}"
        else:
            # 使用IP作为匿名用户标识
            return f"ip_{self.get_client_ip(request)}"
    
    def get_client_ip(self, request):
        """获取客户端IP"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
    
    def is_rate_limited(self, client_id, request):
        """检查是否超过频率限制"""
        # 不同类型的请求有不同的限制
        limits = self.get_rate_limits(request)
        
        for period, max_requests in limits.items():
            cache_key = f"rate_limit_{client_id}_{period}"
            current_requests = cache.get(cache_key, 0)
            
            if current_requests >= max_requests:
                return True
            
            # 增加计数
            cache.set(cache_key, current_requests + 1, period)
        
        return False
    
    def get_rate_limits(self, request):
        """获取频率限制配置"""
        # 默认限制
        default_limits = {
            60: 100,    # 每分钟100次
            3600: 1000, # 每小时1000次
        }
        
        # 特殊路径的限制
        if '/auth/' in request.path:
            return {
                60: 10,   # 认证接口每分钟10次
                3600: 50, # 每小时50次
            }
        elif '/upload/' in request.path:
            return {
                60: 20,   # 上传接口每分钟20次
                3600: 200, # 每小时200次
            }
        
        return default_limits


class SecurityHeadersMiddleware(MiddlewareMixin):
    """安全头中间件"""
    
    def process_response(self, request, response):
        """添加安全头"""
        # 防止点击劫持
        response['X-Frame-Options'] = 'DENY'
        
        # 防止MIME类型嗅探
        response['X-Content-Type-Options'] = 'nosniff'
        
        # XSS保护
        response['X-XSS-Protection'] = '1; mode=block'
        
        # 引用策略
        response['Referrer-Policy'] = 'strict-origin-when-cross-origin'
        
        # 内容安全策略（仅对HTML响应）
        if response.get('Content-Type', '').startswith('text/html'):
            response['Content-Security-Policy'] = (
                "default-src 'self'; "
                "script-src 'self' 'unsafe-inline' 'unsafe-eval'; "
                "style-src 'self' 'unsafe-inline'; "
                "img-src 'self' data: https:; "
                "font-src 'self' https:; "
                "connect-src 'self' https:; "
                "media-src 'self' https:;"
            )
        
        return response


class RequestIDMiddleware(MiddlewareMixin):
    """请求ID中间件"""
    
    def process_request(self, request):
        """为每个请求生成唯一ID"""
        import uuid
        request.request_id = str(uuid.uuid4())[:8]
        
        # 添加到响应头
        def add_request_id(response):
            response['X-Request-ID'] = request.request_id
            return response
        
        request._add_request_id = add_request_id
    
    def process_response(self, request, response):
        """添加请求ID到响应头"""
        if hasattr(request, '_add_request_id'):
            response = request._add_request_id(response)
        return response


class HealthCheckMiddleware(MiddlewareMixin):
    """健康检查中间件"""
    
    def process_request(self, request):
        """处理健康检查请求"""
        if request.path == '/health/':
            return JsonResponse({
                'status': 'healthy',
                'timestamp': time.time(),
                'version': getattr(settings, 'VERSION', '1.0.0')
            })
        
        return None


class CORSMiddleware(MiddlewareMixin):
    """自定义CORS中间件"""
    
    def process_response(self, request, response):
        """添加CORS头"""
        if request.path.startswith('/api/'):
            response['Access-Control-Allow-Origin'] = '*'
            response['Access-Control-Allow-Methods'] = 'GET, POST, PUT, PATCH, DELETE, OPTIONS'
            response['Access-Control-Allow-Headers'] = (
                'Accept, Accept-Language, Content-Language, Content-Type, '
                'Authorization, X-Requested-With, X-CSRFToken'
            )
            response['Access-Control-Max-Age'] = '86400'
        
        return response
    
    def process_request(self, request):
        """处理预检请求"""
        if request.method == 'OPTIONS' and request.path.startswith('/api/'):
            response = JsonResponse({})
            response['Access-Control-Allow-Origin'] = '*'
            response['Access-Control-Allow-Methods'] = 'GET, POST, PUT, PATCH, DELETE, OPTIONS'
            response['Access-Control-Allow-Headers'] = (
                'Accept, Accept-Language, Content-Language, Content-Type, '
                'Authorization, X-Requested-With, X-CSRFToken'
            )
            response['Access-Control-Max-Age'] = '86400'
            return response
        
        return None
