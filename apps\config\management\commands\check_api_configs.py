"""
检查第三方API配置状态
"""
from django.core.management.base import BaseCommand
from apps.config.services import ThirdPartyAPIConfig


class Command(BaseCommand):
    help = '检查第三方API配置状态'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--detailed',
            action='store_true',
            help='显示详细的配置信息',
        )
    
    def handle(self, *args, **options):
        self.stdout.write('=' * 60)
        self.stdout.write(self.style.SUCCESS('第三方API配置状态检查'))
        self.stdout.write('=' * 60)
        
        # 获取缺失的配置
        missing_configs = ThirdPartyAPIConfig.get_missing_configs()
        
        # 统计配置状态
        total_missing = sum(len(configs) for configs in missing_configs.values())
        
        if total_missing == 0:
            self.stdout.write(
                self.style.SUCCESS('\n✅ 所有必要的第三方API配置都已完成！')
            )
        else:
            self.stdout.write(
                self.style.WARNING(f'\n⚠️  发现 {total_missing} 个缺失的配置项')
            )
        
        # 显示各类别的配置状态
        self._show_category_status('AI服务', missing_configs['ai_services'])
        self._show_category_status('存储服务', missing_configs['storage'])
        self._show_category_status('支付服务', missing_configs['payment'])
        self._show_category_status('通知服务', missing_configs['notification'])
        
        # 显示详细配置信息
        if options['detailed']:
            self._show_detailed_configs()
        
        # 显示配置建议
        self._show_configuration_guide()
    
    def _show_category_status(self, category_name, missing_items):
        """显示分类配置状态"""
        self.stdout.write(f'\n📋 {category_name}:')
        
        if not missing_items:
            self.stdout.write(f'  ✅ 配置完整')
        else:
            self.stdout.write(f'  ❌ 缺失配置:')
            for item in missing_items:
                self.stdout.write(f'    - {item}')
    
    def _show_detailed_configs(self):
        """显示详细配置信息"""
        self.stdout.write('\n' + '=' * 60)
        self.stdout.write('详细配置信息')
        self.stdout.write('=' * 60)
        
        # OpenAI配置
        openai_config = ThirdPartyAPIConfig.get_openai_config()
        self.stdout.write('\n🤖 OpenAI配置:')
        self.stdout.write(f'  API密钥: {"已配置" if openai_config["api_key"] else "未配置"}')
        self.stdout.write(f'  模型: {openai_config["model"]}')
        self.stdout.write(f'  最大Token: {openai_config["max_tokens"]}')
        
        # Azure语音配置
        azure_config = ThirdPartyAPIConfig.get_azure_speech_config()
        self.stdout.write('\n🎵 Azure语音配置:')
        self.stdout.write(f'  API密钥: {"已配置" if azure_config["api_key"] else "未配置"}')
        self.stdout.write(f'  区域: {azure_config["region"]}')
        
        # 阿里云OSS配置
        oss_config = ThirdPartyAPIConfig.get_aliyun_oss_config()
        self.stdout.write('\n☁️ 阿里云OSS配置:')
        self.stdout.write(f'  AccessKey: {"已配置" if oss_config["access_key"] else "未配置"}')
        self.stdout.write(f'  SecretKey: {"已配置" if oss_config["secret_key"] else "未配置"}')
        self.stdout.write(f'  存储桶: {oss_config["bucket"] or "未配置"}')
        self.stdout.write(f'  端点: {oss_config["endpoint"] or "未配置"}')
        
        # 支付宝配置
        alipay_config = ThirdPartyAPIConfig.get_alipay_config()
        self.stdout.write('\n💰 支付宝配置:')
        self.stdout.write(f'  应用ID: {"已配置" if alipay_config["app_id"] else "未配置"}')
        self.stdout.write(f'  私钥: {"已配置" if alipay_config["private_key"] else "未配置"}')
        self.stdout.write(f'  公钥: {"已配置" if alipay_config["public_key"] else "未配置"}')
    
    def _show_configuration_guide(self):
        """显示配置指南"""
        self.stdout.write('\n' + '=' * 60)
        self.stdout.write('配置指南')
        self.stdout.write('=' * 60)
        
        self.stdout.write('\n📝 如何配置第三方API:')
        self.stdout.write('1. 访问Django Admin后台: http://localhost:8000/admin/')
        self.stdout.write('2. 进入 "配置管理" -> "系统配置"')
        self.stdout.write('3. 找到相应的API配置项并填入密钥')
        self.stdout.write('4. 确保配置项状态为"激活"')
        
        self.stdout.write('\n🔑 主要API服务申请地址:')
        self.stdout.write('• OpenAI: https://platform.openai.com/api-keys')
        self.stdout.write('• Azure语音: https://portal.azure.com/')
        self.stdout.write('• ElevenLabs: https://elevenlabs.io/app/settings/api-keys')
        self.stdout.write('• HeyGen: https://app.heygen.com/settings/api')
        self.stdout.write('• 阿里云OSS: https://ram.console.aliyun.com/manage/ak')
        self.stdout.write('• 支付宝开放平台: https://open.alipay.com/')
        self.stdout.write('• 微信支付: https://pay.weixin.qq.com/')
        
        self.stdout.write('\n💡 配置优先级建议:')
        self.stdout.write('第一阶段 (基础功能):')
        self.stdout.write('  ✓ OpenAI API (文案生成)')
        self.stdout.write('  ✓ 阿里云OSS (文件存储)')
        self.stdout.write('  ✓ 支付宝/微信支付 (支付功能)')
        
        self.stdout.write('\n第二阶段 (核心AI功能):')
        self.stdout.write('  ✓ Azure语音服务 (语音合成)')
        self.stdout.write('  ✓ HeyGen (数字人视频)')
        self.stdout.write('  ✓ ElevenLabs (语音克隆)')
        
        self.stdout.write('\n第三阶段 (完整功能):')
        self.stdout.write('  ✓ DALL-E (AI绘画)')
        self.stdout.write('  ✓ RunwayML (AI视频)')
        self.stdout.write('  ✓ Suno AI (AI音乐)')
        self.stdout.write('  ✓ Face++ (人脸处理)')
        
        self.stdout.write('\n⚠️  注意事项:')
        self.stdout.write('• 敏感配置项(API密钥)会在界面中隐藏显示')
        self.stdout.write('• 配置修改后会自动清除缓存')
        self.stdout.write('• 建议定期检查API配额和使用情况')
        self.stdout.write('• 生产环境请使用环境变量管理敏感信息')
        
        self.stdout.write('\n🔄 重新检查配置:')
        self.stdout.write('python manage.py check_api_configs --detailed')
        
        self.stdout.write('\n' + '=' * 60)
