"""
智能文案相关视图
"""
from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import SearchFilter, OrderingFilter

from .models import Copywriting, CopywritingTemplate, CopywritingHistory, TextExtraction
from .serializers import (
    CopywritingSerializer, CopywritingTemplateSerializer, 
    CopywritingHistorySerializer, TextExtractionSerializer
)


class CopywritingViewSet(viewsets.ModelViewSet):
    """智能文案视图集"""
    queryset = Copywriting.objects.all()
    serializer_class = CopywritingSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['scene_type', 'status', 'word_count', 'language']
    search_fields = ['title', 'theme_description', 'generated_content']
    ordering_fields = ['created_at', 'updated_at']
    ordering = ['-created_at']
    
    def get_queryset(self):
        """普通用户只能查看自己的文案"""
        if self.request.user.is_staff:
            return self.queryset
        return self.queryset.filter(user=self.request.user)
    
    def perform_create(self, serializer):
        """创建文案时设置用户"""
        serializer.save(user=self.request.user)
    
    @action(detail=True, methods=['post'])
    def generate(self, request, pk=None):
        """生成文案"""
        copywriting = self.get_object()
        user = request.user
        
        if copywriting.status != 'pending':
            return Response(
                {'error': '只有待生成状态的文案才能开始生成'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # 计算算力消耗（按字数计算）
        word_count = copywriting.word_count
        computing_power_needed = max(word_count // 100, 1)  # 每100字消耗1算力
        
        # 检查算力是否足够
        if user.computing_power < computing_power_needed:
            return Response(
                {'error': f'算力不足，需要 {computing_power_needed} 算力'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # 消耗算力
        try:
            user.consume_computing_power(
                computing_power_needed,
                f'生成文案: {copywriting.title or copywriting.theme_description[:20]}'
            )
            
            # 更新文案状态
            copywriting.status = 'generating'
            copywriting.computing_power_consumed = computing_power_needed
            copywriting.save(update_fields=['status', 'computing_power_consumed'])
            
            # 这里应该调用AI服务生成文案
            # 暂时使用模拟内容
            generated_content = self._generate_mock_content(copywriting)
            
            # 更新生成结果
            copywriting.status = 'completed'
            copywriting.generated_content = generated_content
            copywriting.actual_word_count = len(generated_content)
            from django.utils import timezone
            copywriting.completed_at = timezone.now()
            copywriting.save(update_fields=[
                'status', 'generated_content', 'actual_word_count', 'completed_at'
            ])
            
            # 保存历史版本
            CopywritingHistory.objects.create(
                copywriting=copywriting,
                version=1,
                content=generated_content,
                word_count=len(generated_content)
            )
            
            return Response({
                'message': '文案生成完成',
                'generated_content': generated_content,
                'word_count': len(generated_content),
                'computing_power_consumed': computing_power_needed,
                'remaining_power': user.computing_power
            })
            
        except ValueError as e:
            copywriting.status = 'pending'
            copywriting.save(update_fields=['status'])
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
    
    def _generate_mock_content(self, copywriting):
        """生成模拟文案内容"""
        scene_templates = {
            'general': '这是一篇关于{theme}的通用文案。{theme}具有重要的意义和价值。',
            'knowledge': '今天我们来了解{theme}。{theme}是一个值得深入探讨的知识点。',
            'emotion': '亲爱的朋友，关于{theme}，我想和你分享一些感悟。',
            'ecommerce': '推荐一款优质的{theme}产品，性价比超高，值得购买！',
            'blessing': '在这个特殊的时刻，祝愿大家{theme}，幸福安康！',
            'health': '关于{theme}的健康知识，让我们一起关注身体健康。',
        }
        
        template = scene_templates.get(copywriting.scene_type, scene_templates['general'])
        theme = copywriting.theme_description[:20] if copywriting.theme_description else '主题'
        
        # 根据字数要求扩展内容
        base_content = template.format(theme=theme)
        target_length = copywriting.word_count
        
        while len(base_content) < target_length:
            base_content += f"这是为了达到{target_length}字要求而添加的内容。"
        
        return base_content[:target_length]
    
    @action(detail=True, methods=['post'])
    def regenerate(self, request, pk=None):
        """重新生成文案"""
        copywriting = self.get_object()
        
        # 重置状态
        copywriting.status = 'pending'
        copywriting.save(update_fields=['status'])
        
        # 调用生成方法
        return self.generate(request, pk)

    @action(detail=False, methods=['get'])
    def my_copywriting(self, request):
        """获取我的文案"""
        copywriting = self.get_queryset()
        page = self.paginate_queryset(copywriting)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(copywriting, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['post'])
    def ai_generate(self, request):
        """AI文案生成"""
        theme = request.data.get('theme', '')
        scene_type = request.data.get('scene_type', 'general')
        word_count = request.data.get('word_count', 100)

        if not theme:
            return Response(
                {'error': '请提供文案主题'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # 创建文案记录
        copywriting = Copywriting.objects.create(
            user=request.user,
            theme_description=theme,
            scene_type=scene_type,
            word_count=word_count,
            status='pending'
        )

        # 直接生成文案
        return self.generate(request, copywriting.id)

    @action(detail=True, methods=['post'])
    def optimize(self, request, pk=None):
        """文案优化"""
        copywriting = self.get_object()
        optimization_type = request.data.get('type', 'general')

        if not copywriting.generated_content:
            return Response(
                {'error': '没有可优化的文案内容'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # 这里应该调用AI优化服务
        # 暂时返回模拟优化结果
        optimized_content = f"[优化版本] {copywriting.generated_content}"

        return Response({
            'message': '文案优化完成',
            'original_content': copywriting.generated_content,
            'optimized_content': optimized_content,
            'optimization_type': optimization_type
        })

    @action(detail=False, methods=['post'])
    def batch_delete(self, request):
        """批量删除文案"""
        ids = request.data.get('ids', [])

        if not ids:
            return Response(
                {'error': '请提供要删除的文案ID列表'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # 只能删除自己的文案
        deleted_count = self.get_queryset().filter(id__in=ids).delete()[0]

        return Response({
            'message': f'成功删除 {deleted_count} 条文案',
            'deleted_count': deleted_count
        })
    
    @action(detail=True, methods=['post'])
    def save_as_draft(self, request, pk=None):
        """保存为草稿"""
        copywriting = self.get_object()
        copywriting.is_saved_as_draft = True
        copywriting.save(update_fields=['is_saved_as_draft'])
        
        return Response({'message': '已保存为草稿'})


class CopywritingTemplateViewSet(viewsets.ReadOnlyModelViewSet):
    """文案模板视图集"""
    queryset = CopywritingTemplate.objects.filter(is_active=True)
    serializer_class = CopywritingTemplateSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['scene_type']
    search_fields = ['name', 'description']
    ordering_fields = ['usage_count', 'name']
    ordering = ['-usage_count', 'name']
    
    @action(detail=True, methods=['post'])
    def use_template(self, request, pk=None):
        """使用模板创建文案"""
        template = self.get_object()
        user = request.user
        
        # 基于模板创建文案
        copywriting = Copywriting.objects.create(
            user=user,
            title=request.data.get('title', f'基于{template.name}的文案'),
            scene_type=template.scene_type,
            theme_description=request.data.get('theme_description', ''),
            word_count=request.data.get('word_count', 300),
            language=request.data.get('language', 'zh-cn')
        )
        
        # 增加模板使用次数
        template.increment_usage()
        
        serializer = CopywritingSerializer(copywriting)
        return Response({
            'message': f'成功使用模板 {template.name} 创建文案',
            'copywriting': serializer.data
        })


class CopywritingHistoryViewSet(viewsets.ReadOnlyModelViewSet):
    """文案历史视图集"""
    queryset = CopywritingHistory.objects.all()
    serializer_class = CopywritingHistorySerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, OrderingFilter]
    filterset_fields = ['copywriting']
    ordering_fields = ['version', 'created_at']
    ordering = ['-version']
    
    def get_queryset(self):
        """普通用户只能查看自己文案的历史"""
        if self.request.user.is_staff:
            return self.queryset
        return self.queryset.filter(copywriting__user=self.request.user)


class TextExtractionViewSet(viewsets.ModelViewSet):
    """文案提取视图集"""
    queryset = TextExtraction.objects.all()
    serializer_class = TextExtractionSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['source_type', 'status']
    search_fields = ['extracted_text']
    ordering_fields = ['created_at', 'updated_at']
    ordering = ['-created_at']
    
    def get_queryset(self):
        """普通用户只能查看自己的提取记录"""
        if self.request.user.is_staff:
            return self.queryset
        return self.queryset.filter(user=self.request.user)
    
    def perform_create(self, serializer):
        """创建提取任务时设置用户"""
        serializer.save(user=self.request.user)
    
    @action(detail=True, methods=['post'])
    def start_extraction(self, request, pk=None):
        """开始文案提取"""
        extraction = self.get_object()
        user = request.user
        
        if extraction.status != 'pending':
            return Response(
                {'error': '只有待处理状态的任务才能开始提取'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # 计算算力消耗
        computing_power_needed = 5  # 固定消耗5算力
        
        # 检查算力是否足够
        if user.computing_power < computing_power_needed:
            return Response(
                {'error': f'算力不足，需要 {computing_power_needed} 算力'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # 消耗算力
        try:
            user.consume_computing_power(
                computing_power_needed,
                f'文案提取: {extraction.get_source_type_display()}'
            )
            
            # 更新提取状态
            extraction.status = 'processing'
            extraction.computing_power_consumed = computing_power_needed
            extraction.save(update_fields=['status', 'computing_power_consumed'])
            
            # 这里应该调用实际的提取服务
            # 暂时使用模拟结果
            extracted_text = "这是从文件中提取的示例文本内容。"
            
            # 更新提取结果
            extraction.status = 'completed'
            extraction.extracted_text = extracted_text
            extraction.confidence_score = 0.95
            from django.utils import timezone
            extraction.completed_at = timezone.now()
            extraction.save(update_fields=[
                'status', 'extracted_text', 'confidence_score', 'completed_at'
            ])
            
            return Response({
                'message': '文案提取完成',
                'extracted_text': extracted_text,
                'confidence_score': 0.95,
                'computing_power_consumed': computing_power_needed,
                'remaining_power': user.computing_power
            })
            
        except ValueError as e:
            extraction.status = 'pending'
            extraction.save(update_fields=['status'])
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
