from django.db import models
from django.conf import settings


class DigitalWork(models.Model):
    """数字人作品"""
    STATUS_CHOICES = [
        ('processing', '生成中'),
        ('completed', '已完成'),
        ('failed', '失败'),
        ('pending', '等待中'),
    ]

    name = models.CharField(max_length=200, verbose_name='作品名称')
    description = models.TextField(blank=True, verbose_name='作品描述')
    thumbnail = models.URLField(blank=True, verbose_name='缩略图')
    video_url = models.URLField(blank=True, verbose_name='视频地址')
    duration = models.CharField(max_length=20, blank=True, verbose_name='视频时长')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending', verbose_name='状态')

    # 关联信息
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, verbose_name='创建用户')
    avatar_name = models.CharField(max_length=100, blank=True, verbose_name='使用的数字人形象')
    voice_name = models.CharField(max_length=100, blank=True, verbose_name='使用的声音')
    script_content = models.TextField(blank=True, verbose_name='文案内容')

    # 生成参数
    computing_power_cost = models.IntegerField(default=0, verbose_name='消耗算力')
    generation_time = models.FloatField(default=0.0, verbose_name='生成耗时(秒)')

    # 文件信息
    file_size = models.BigIntegerField(default=0, verbose_name='文件大小(字节)')
    resolution = models.CharField(max_length=20, blank=True, verbose_name='分辨率')
    frame_rate = models.CharField(max_length=10, blank=True, verbose_name='帧率')

    # 时间信息
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    completed_at = models.DateTimeField(null=True, blank=True, verbose_name='完成时间')

    class Meta:
        db_table = 'digital_works'
        verbose_name = '数字人作品'
        verbose_name_plural = '数字人作品'
        ordering = ['-created_at']

    def __str__(self):
        return f'{self.name} - {self.user.username}'

    @property
    def is_completed(self):
        return self.status == 'completed'

    @property
    def is_processing(self):
        return self.status == 'processing'

    @property
    def can_download(self):
        return self.status == 'completed' and self.video_url


class WorkTemplate(models.Model):
    """作品模板"""
    name = models.CharField(max_length=200, verbose_name='模板名称')
    description = models.TextField(blank=True, verbose_name='模板描述')
    thumbnail = models.URLField(blank=True, verbose_name='模板缩略图')
    preview_video = models.URLField(blank=True, verbose_name='预览视频')

    # 模板配置
    default_avatar = models.CharField(max_length=100, blank=True, verbose_name='默认数字人')
    default_voice = models.CharField(max_length=100, blank=True, verbose_name='默认声音')
    default_script = models.TextField(blank=True, verbose_name='默认文案')

    # 分类和标签
    category = models.CharField(max_length=50, blank=True, verbose_name='分类')
    tags = models.JSONField(default=list, verbose_name='标签')

    # 状态
    is_active = models.BooleanField(default=True, verbose_name='是否启用')
    is_premium = models.BooleanField(default=False, verbose_name='是否付费模板')
    sort_order = models.IntegerField(default=0, verbose_name='排序')

    # 统计
    usage_count = models.IntegerField(default=0, verbose_name='使用次数')

    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        db_table = 'work_templates'
        verbose_name = '作品模板'
        verbose_name_plural = '作品模板'
        ordering = ['sort_order', '-created_at']

    def __str__(self):
        return self.name
