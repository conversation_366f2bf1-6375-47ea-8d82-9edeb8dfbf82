"""
用户数据初始化命令
基于原项目 https://hm.umi6.com/ 的真实数据
"""

from django.core.management.base import BaseCommand
from django.db import transaction
from django.contrib.auth import get_user_model
from apps.users.models import ComputingPowerLog

User = get_user_model()


class Command(BaseCommand):
    help = '初始化用户数据'

    def add_arguments(self, parser):
        parser.add_argument(
            '--clear',
            action='store_true',
            help='清除现有数据后重新初始化',
        )

    def handle(self, *args, **options):
        if options['clear']:
            self.stdout.write('清除现有用户数据...')
            # 保留超级用户，只删除普通用户
            User.objects.filter(is_superuser=False).delete()
            ComputingPowerLog.objects.all().delete()

        with transaction.atomic():
            self.create_demo_users()

        self.stdout.write(
            self.style.SUCCESS('用户数据初始化完成！')
        )

    def create_demo_users(self):
        """创建演示用户数据 - 基于原项目的真实数据"""
        users_data = [
            {
                'username': '13542802363',
                'phone': '13542802363',
                'email': '<EMAIL>',
                'computing_power': 120,
                'total_computing_power': 500,
                'package_type': 'basic',
                'is_active': True,
                'password': 'demo123456'
            },
            {
                'username': 'testuser1',
                'phone': '13800138001',
                'email': '<EMAIL>',
                'computing_power': 50,
                'total_computing_power': 200,
                'package_type': 'free',
                'is_active': True,
                'password': 'test123456'
            },
            {
                'username': 'testuser2',
                'phone': '13800138002',
                'email': '<EMAIL>',
                'computing_power': 200,
                'total_computing_power': 1000,
                'package_type': 'pro',
                'is_active': True,
                'password': 'test123456'
            },
            {
                'username': 'vipuser',
                'phone': '13800138888',
                'email': '<EMAIL>',
                'computing_power': 1000,
                'total_computing_power': 5000,
                'package_type': 'enterprise',
                'is_active': True,
                'password': 'vip123456'
            }
        ]

        for user_data in users_data:
            password = user_data.pop('password')
            
            # 检查用户是否已存在
            if User.objects.filter(username=user_data['username']).exists():
                self.stdout.write(f'用户已存在: {user_data["username"]}')
                continue
            
            # 创建用户
            user = User.objects.create_user(
                password=password,
                **user_data
            )
            
            self.stdout.write(f'创建用户: {user.username}')
            
            # 创建算力日志
            if user.computing_power > 0:
                ComputingPowerLog.objects.create(
                    user=user,
                    operation_type='add',
                    amount=user.computing_power,
                    balance_after=user.computing_power,
                    reason='初始化赠送算力'
                )
                
        # 创建管理员用户（如果不存在）
        if not User.objects.filter(username='admin').exists():
            admin_user = User.objects.create_superuser(
                username='admin',
                email='<EMAIL>',
                password='admin123',
                phone='13800000000',
                computing_power=10000,
                total_computing_power=10000,
                package_type='enterprise'
            )
            self.stdout.write(f'创建管理员用户: {admin_user.username}')
            
            # 创建管理员算力日志
            ComputingPowerLog.objects.create(
                user=admin_user,
                operation_type='add',
                amount=10000,
                balance_after=10000,
                reason='管理员初始算力'
            )
