from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON>ult<PERSON>out<PERSON>
from .views import ToolCategoryViewSet, AIToolViewSet, ToolUsageLogViewSet
from apps.ai_tools.views import (
    AIChatView, AIPaintingView, AIMusicView, AIVideoView,
    PhotoDigitalHumanView, TextExtractionView
)

router = DefaultRouter()
router.register(r'categories', ToolCategoryViewSet)
router.register(r'tools', AIToolViewSet)
router.register(r'usage-logs', ToolUsageLogViewSet, basename='toolusagelog')

urlpatterns = [
    path('', include(router.urls)),
    # AI工具具体功能端点
    path('chat/', AIChatView.as_view(), name='ai-chat'),
    path('painting/', AIPaintingView.as_view(), name='ai-painting'),
    path('copywriting/', AIChatView.as_view(), name='ai-copywriting'),  # 复用聊天接口
    path('music/', AIMusicView.as_view(), name='ai-music'),
    path('video/', AIVideoView.as_view(), name='ai-video'),
    path('photo-digital-human/', PhotoDigitalHumanView.as_view(), name='photo-digital-human'),
    path('text-extraction/', TextExtractionView.as_view(), name='text-extraction'),
]
