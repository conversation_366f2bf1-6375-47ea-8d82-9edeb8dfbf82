"""
文件上传数据模型
"""
import os
import uuid
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.contrib.auth import get_user_model
from django.core.validators import FileExtensionValidator

User = get_user_model()


def upload_to_path(instance, filename):
    """生成上传路径"""
    ext = filename.split('.')[-1]
    filename = f"{uuid.uuid4().hex}.{ext}"
    return os.path.join(instance.upload_type, filename)


class UploadFile(models.Model):
    """上传文件模型"""
    
    UPLOAD_TYPE_CHOICES = [
        ('avatar', _('数字人形象')),
        ('voice', _('语音文件')),
        ('video', _('视频文件')),
        ('image', _('图片文件')),
        ('document', _('文档文件')),
        ('other', _('其他文件')),
    ]
    
    STATUS_CHOICES = [
        ('uploading', _('上传中')),
        ('completed', _('上传完成')),
        ('failed', _('上传失败')),
        ('processing', _('处理中')),
        ('processed', _('处理完成')),
    ]
    
    # 基本信息
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name=_('用户'))
    upload_type = models.CharField(_('上传类型'), max_length=20, choices=UPLOAD_TYPE_CHOICES)
    original_filename = models.CharField(_('原始文件名'), max_length=255)
    file = models.FileField(_('文件'), upload_to=upload_to_path)
    
    # 文件信息
    file_size = models.BigIntegerField(_('文件大小'), default=0)
    file_type = models.CharField(_('文件类型'), max_length=100, blank=True)
    file_hash = models.CharField(_('文件哈希'), max_length=64, blank=True)
    
    # 状态信息
    status = models.CharField(_('状态'), max_length=20, choices=STATUS_CHOICES, default='uploading')
    progress = models.IntegerField(_('上传进度'), default=0)
    error_message = models.TextField(_('错误信息'), blank=True)
    
    # 元数据
    metadata = models.JSONField(_('元数据'), default=dict, blank=True)
    
    # 时间信息
    created_at = models.DateTimeField(_('创建时间'), auto_now_add=True)
    updated_at = models.DateTimeField(_('更新时间'), auto_now=True)
    completed_at = models.DateTimeField(_('完成时间'), null=True, blank=True)
    
    class Meta:
        verbose_name = _('上传文件')
        verbose_name_plural = _('上传文件')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'status']),
            models.Index(fields=['upload_type', 'created_at']),
        ]
    
    def __str__(self):
        return f'{self.user.username}: {self.original_filename}'
    
    @property
    def file_url(self):
        """获取文件URL"""
        if self.file:
            return self.file.url
        return None
    
    def mark_completed(self):
        """标记为完成"""
        from django.utils import timezone
        self.status = 'completed'
        self.progress = 100
        self.completed_at = timezone.now()
        self.save(update_fields=['status', 'progress', 'completed_at'])
    
    def mark_failed(self, error_message=''):
        """标记为失败"""
        self.status = 'failed'
        self.error_message = error_message
        self.save(update_fields=['status', 'error_message'])


class ChunkedUpload(models.Model):
    """分块上传模型"""
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name=_('用户'))
    upload_id = models.UUIDField(_('上传ID'), default=uuid.uuid4, unique=True)
    filename = models.CharField(_('文件名'), max_length=255)
    total_size = models.BigIntegerField(_('总大小'))
    chunk_size = models.IntegerField(_('块大小'), default=1024*1024)  # 1MB
    uploaded_size = models.BigIntegerField(_('已上传大小'), default=0)
    
    # 状态
    is_completed = models.BooleanField(_('是否完成'), default=False)
    
    # 文件信息
    file_type = models.CharField(_('文件类型'), max_length=100, blank=True)
    upload_type = models.CharField(_('上传类型'), max_length=20, choices=UploadFile.UPLOAD_TYPE_CHOICES)
    
    # 时间信息
    created_at = models.DateTimeField(_('创建时间'), auto_now_add=True)
    updated_at = models.DateTimeField(_('更新时间'), auto_now=True)
    expires_at = models.DateTimeField(_('过期时间'))
    
    class Meta:
        verbose_name = _('分块上传')
        verbose_name_plural = _('分块上传')
        ordering = ['-created_at']
    
    def __str__(self):
        return f'{self.user.username}: {self.filename} ({self.upload_id})'
    
    @property
    def progress_percentage(self):
        """上传进度百分比"""
        if self.total_size == 0:
            return 0
        return int((self.uploaded_size / self.total_size) * 100)
    
    def is_expired(self):
        """检查是否过期"""
        from django.utils import timezone
        return timezone.now() > self.expires_at


class UploadChunk(models.Model):
    """上传块模型"""
    
    chunked_upload = models.ForeignKey(
        ChunkedUpload, 
        on_delete=models.CASCADE, 
        related_name='chunks',
        verbose_name=_('分块上传')
    )
    chunk_number = models.IntegerField(_('块编号'))
    chunk_data = models.BinaryField(_('块数据'))
    chunk_size = models.IntegerField(_('块大小'))
    checksum = models.CharField(_('校验和'), max_length=64, blank=True)
    
    created_at = models.DateTimeField(_('创建时间'), auto_now_add=True)
    
    class Meta:
        verbose_name = _('上传块')
        verbose_name_plural = _('上传块')
        unique_together = ['chunked_upload', 'chunk_number']
        ordering = ['chunk_number']
    
    def __str__(self):
        return f'{self.chunked_upload.filename} - 块 {self.chunk_number}'
