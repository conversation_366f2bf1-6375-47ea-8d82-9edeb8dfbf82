"""
配置百度曦灵数字人服务的管理命令
"""
from django.core.management.base import BaseCommand, CommandError
from apps.config.models import SystemConfig
import json
import os
import requests


class Command(BaseCommand):
    help = '配置百度曦灵数字人服务'

    def add_arguments(self, parser):
        parser.add_argument('--api-key', type=str, help='百度曦灵API Key')
        parser.add_argument('--secret-key', type=str, help='百度曦灵Secret Key')
        parser.add_argument('--config-file', type=str, help='配置文件路径', default='baidu_xiling_config.json')
        parser.add_argument('--test', action='store_true', help='测试API连接')

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('开始配置百度曦灵数字人服务...'))
        
        api_key = options.get('api_key')
        secret_key = options.get('secret_key')
        config_file = options.get('config_file')
        test_connection = options.get('test')
        
        # 从配置文件加载
        if config_file and os.path.exists(config_file):
            self.stdout.write(f'从配置文件加载: {config_file}')
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    baidu_config = config.get('baidu_xiling', {})
                    api_key = api_key or baidu_config.get('api_key')
                    secret_key = secret_key or baidu_config.get('secret_key')
            except Exception as e:
                raise CommandError(f'读取配置文件失败: {e}')
        
        # 验证参数
        if not api_key or not secret_key:
            raise CommandError('请提供API Key和Secret Key')
        
        if api_key == 'your_api_key_here' or secret_key == 'your_secret_key_here':
            raise CommandError('请使用真实的API密钥，不要使用占位符')
        
        # 更新系统配置
        self.update_system_config(api_key, secret_key)
        
        # 测试连接
        if test_connection:
            self.test_api_connection()
        
        self.stdout.write(self.style.SUCCESS('百度曦灵配置完成！'))

    def update_system_config(self, api_key, secret_key):
        """更新系统配置"""
        configs = [
            {
                'key': 'baidu_xiling_api_key',
                'name': '百度曦灵API Key',
                'value': api_key,
                'config_type': 'ai_service',
                'value_type': 'string',
                'is_sensitive': True,
                'description': '百度智能云曦灵数字人开放平台的API Key'
            },
            {
                'key': 'baidu_xiling_secret_key',
                'name': '百度曦灵Secret Key',
                'value': secret_key,
                'config_type': 'ai_service',
                'value_type': 'string',
                'is_sensitive': True,
                'description': '百度智能云曦灵数字人开放平台的Secret Key'
            },
            {
                'key': 'baidu_xiling_base_url',
                'name': '百度曦灵API基础URL',
                'value': 'https://xiling.cloud.baidu.com/api/v1',
                'config_type': 'ai_service',
                'value_type': 'string',
                'is_sensitive': False,
                'description': '百度曦灵数字人服务的API基础URL地址'
            },
            {
                'key': 'digital_human_provider',
                'name': '数字人服务提供商',
                'value': 'baidu_xiling',
                'config_type': 'ai_service',
                'value_type': 'string',
                'is_sensitive': False,
                'description': '默认的数字人服务提供商'
            },
            {
                'key': 'avatar_clone_max_wait_time',
                'name': '形象克隆最大等待时间',
                'value': '600',
                'config_type': 'ai_service',
                'value_type': 'integer',
                'is_sensitive': False,
                'description': '形象克隆任务的最大等待时间（秒）'
            }
        ]
        
        for config_data in configs:
            config_obj, created = SystemConfig.objects.get_or_create(
                key=config_data['key'],
                defaults=config_data
            )
            
            if not created:
                # 更新现有配置
                config_obj.value = config_data['value']
                config_obj.name = config_data['name']
                config_obj.description = config_data['description']
                config_obj.save()
            
            action = '创建' if created else '更新'
            self.stdout.write(f'  {action}配置: {config_data["key"]}')

    def test_api_connection(self):
        """测试API连接"""
        self.stdout.write('测试百度曦灵API连接...')
        
        try:
            from apps.videos.services.digital_human_service import digital_human_service
            
            xiling_service = digital_human_service.baidu_xiling
            
            if not xiling_service.is_available():
                raise Exception('百度曦灵服务不可用')
            
            self.stdout.write(self.style.SUCCESS('✅ 百度曦灵服务连接正常'))
            
            # 测试获取音色列表
            voices_result = xiling_service.get_voices()
            if voices_result.get('success'):
                voices = voices_result.get('voices', [])
                self.stdout.write(self.style.SUCCESS(f'✅ 成功获取音色列表，共 {len(voices)} 个音色'))
                
                # 显示前3个音色
                for i, voice in enumerate(voices[:3]):
                    name = voice.get('name', 'Unknown')
                    per_id = voice.get('perId', 'No ID')
                    self.stdout.write(f'  音色 {i+1}: {name} ({per_id})')
            else:
                self.stdout.write(self.style.WARNING(f'⚠️  获取音色列表失败: {voices_result.get("error")}'))
            
            # 测试获取人像列表
            try:
                from apps.avatars.models import DigitalAvatar
                avatar_count = DigitalAvatar.objects.count()
                self.stdout.write(self.style.SUCCESS(f'✅ 数据库连接正常，当前有 {avatar_count} 个数字人形象'))
            except Exception as e:
                self.stdout.write(self.style.WARNING(f'⚠️  数据库连接测试失败: {e}'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'❌ API连接测试失败: {e}'))
            raise CommandError('API连接测试失败')

    def create_sample_config(self):
        """创建示例配置文件"""
        config_file = 'baidu_xiling_config.json'
        
        if os.path.exists(config_file):
            self.stdout.write(f'配置文件已存在: {config_file}')
            return
        
        sample_config = {
            "baidu_xiling": {
                "api_key": "your_api_key_here",
                "secret_key": "your_secret_key_here",
                "base_url": "https://xiling.cloud.baidu.com/api/v1",
                "description": "请访问 https://xiling.cloud.baidu.com/open/overview 获取API密钥"
            },
            "settings": {
                "avatar_clone_max_wait_time": 600,
                "digital_human_provider": "baidu_xiling",
                "enable_progress_tracking": True
            },
            "instructions": {
                "step1": "访问 https://xiling.cloud.baidu.com/open/overview",
                "step2": "注册百度智能云账号",
                "step3": "点击左下角领取试用额度",
                "step4": "进入应用管理创建应用",
                "step5": "获取AppID作为api_key，AppKey作为secret_key",
                "step6": "替换上面的your_api_key_here和your_secret_key_here"
            }
        }
        
        try:
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(sample_config, f, indent=2, ensure_ascii=False)
            
            self.stdout.write(self.style.SUCCESS(f'✅ 创建示例配置文件: {config_file}'))
            self.stdout.write('请编辑此文件，填入真实的API密钥')
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'❌ 创建配置文件失败: {e}'))

    def show_usage_guide(self):
        """显示使用指南"""
        self.stdout.write(self.style.SUCCESS('\n📋 使用指南:'))
        self.stdout.write('')
        self.stdout.write('1. 获取API密钥:')
        self.stdout.write('   访问: https://xiling.cloud.baidu.com/open/overview')
        self.stdout.write('   注册并领取试用额度')
        self.stdout.write('   创建应用获取AppID和AppKey')
        self.stdout.write('')
        self.stdout.write('2. 配置系统:')
        self.stdout.write('   python manage.py setup_baidu_xiling --api-key YOUR_API_KEY --secret-key YOUR_SECRET_KEY')
        self.stdout.write('   或者:')
        self.stdout.write('   python manage.py setup_baidu_xiling --config-file baidu_xiling_config.json')
        self.stdout.write('')
        self.stdout.write('3. 测试连接:')
        self.stdout.write('   python manage.py setup_baidu_xiling --test')
        self.stdout.write('')
        self.stdout.write('4. 开始使用:')
        self.stdout.write('   python manage.py runserver')
        self.stdout.write('   访问前端页面测试形象克隆和视频生成功能')
        self.stdout.write('')
        self.stdout.write('💰 成本优势:')
        self.stdout.write('   相比HeyGen节省75%成本')
        self.stdout.write('   有免费试用额度')
        self.stdout.write('   价格透明，按量计费')
