"""
异步任务数据模型
"""
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.contrib.auth import get_user_model
from django.contrib.contenttypes.models import ContentType
from django.contrib.contenttypes.fields import GenericForeignKey
import json
import uuid

User = get_user_model()


class TaskQueue(models.Model):
    """任务队列"""
    
    PRIORITY_CHOICES = [
        ('low', _('低')),
        ('normal', _('普通')),
        ('high', _('高')),
        ('urgent', _('紧急')),
    ]
    
    STATUS_CHOICES = [
        ('pending', _('等待中')),
        ('running', _('执行中')),
        ('completed', _('已完成')),
        ('failed', _('失败')),
        ('cancelled', _('已取消')),
        ('retry', _('重试中')),
    ]
    
    # 任务标识
    task_id = models.UUIDField(_('任务ID'), default=uuid.uuid4, unique=True)
    name = models.CharField(_('任务名称'), max_length=200)
    description = models.TextField(_('任务描述'), blank=True)
    
    # 任务类型和函数
    task_type = models.CharField(_('任务类型'), max_length=100)
    task_function = models.CharField(_('任务函数'), max_length=200)
    
    # 任务参数
    args = models.JSONField(_('位置参数'), default=list, blank=True)
    kwargs = models.JSONField(_('关键字参数'), default=dict, blank=True)
    
    # 任务状态
    status = models.CharField(_('状态'), max_length=20, choices=STATUS_CHOICES, default='pending')
    priority = models.CharField(_('优先级'), max_length=10, choices=PRIORITY_CHOICES, default='normal')
    
    # 执行信息
    worker_id = models.CharField(_('工作进程ID'), max_length=100, blank=True)
    started_at = models.DateTimeField(_('开始时间'), null=True, blank=True)
    completed_at = models.DateTimeField(_('完成时间'), null=True, blank=True)
    
    # 结果和错误
    result = models.JSONField(_('执行结果'), null=True, blank=True)
    error_message = models.TextField(_('错误信息'), blank=True)
    traceback = models.TextField(_('错误堆栈'), blank=True)
    
    # 重试配置
    max_retries = models.IntegerField(_('最大重试次数'), default=3)
    retry_count = models.IntegerField(_('已重试次数'), default=0)
    retry_delay = models.IntegerField(_('重试延迟(秒)'), default=60)
    
    # 进度信息
    progress = models.IntegerField(_('进度百分比'), default=0)
    progress_message = models.CharField(_('进度消息'), max_length=500, blank=True)
    
    # 关联对象
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE, null=True, blank=True)
    object_id = models.PositiveIntegerField(null=True, blank=True)
    content_object = GenericForeignKey('content_type', 'object_id')
    
    # 创建者
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name=_('创建者'))
    
    # 时间
    created_at = models.DateTimeField(_('创建时间'), auto_now_add=True)
    updated_at = models.DateTimeField(_('更新时间'), auto_now=True)
    
    # 过期时间
    expires_at = models.DateTimeField(_('过期时间'), null=True, blank=True)
    
    class Meta:
        verbose_name = _('任务队列')
        verbose_name_plural = _('任务队列')
        ordering = ['-priority', '-created_at']
        indexes = [
            models.Index(fields=['status', 'priority']),
            models.Index(fields=['task_type', 'status']),
            models.Index(fields=['created_by', 'status']),
        ]
    
    def __str__(self):
        return f'{self.name} ({self.get_status_display()})'
    
    def is_finished(self):
        """检查任务是否已完成"""
        return self.status in ['completed', 'failed', 'cancelled']
    
    def can_retry(self):
        """检查是否可以重试"""
        return (
            self.status == 'failed' and
            self.retry_count < self.max_retries
        )
    
    def update_progress(self, progress, message=''):
        """更新任务进度"""
        self.progress = max(0, min(100, progress))
        self.progress_message = message
        self.save(update_fields=['progress', 'progress_message', 'updated_at'])
    
    def mark_started(self, worker_id):
        """标记任务开始"""
        from django.utils import timezone
        self.status = 'running'
        self.worker_id = worker_id
        self.started_at = timezone.now()
        self.save(update_fields=['status', 'worker_id', 'started_at', 'updated_at'])
    
    def mark_completed(self, result=None):
        """标记任务完成"""
        from django.utils import timezone
        self.status = 'completed'
        self.result = result
        self.progress = 100
        self.completed_at = timezone.now()
        self.save(update_fields=['status', 'result', 'progress', 'completed_at', 'updated_at'])
    
    def mark_failed(self, error_message, traceback=''):
        """标记任务失败"""
        from django.utils import timezone
        self.status = 'failed'
        self.error_message = error_message
        self.traceback = traceback
        self.completed_at = timezone.now()
        self.save(update_fields=['status', 'error_message', 'traceback', 'completed_at', 'updated_at'])


class TaskSchedule(models.Model):
    """定时任务"""
    
    SCHEDULE_TYPE_CHOICES = [
        ('once', _('一次性')),
        ('interval', _('间隔执行')),
        ('cron', _('Cron表达式')),
        ('daily', _('每日')),
        ('weekly', _('每周')),
        ('monthly', _('每月')),
    ]
    
    # 任务信息
    name = models.CharField(_('任务名称'), max_length=200)
    description = models.TextField(_('任务描述'), blank=True)
    task_function = models.CharField(_('任务函数'), max_length=200)
    
    # 调度配置
    schedule_type = models.CharField(_('调度类型'), max_length=20, choices=SCHEDULE_TYPE_CHOICES)
    schedule_config = models.JSONField(_('调度配置'), default=dict)
    
    # 任务参数
    args = models.JSONField(_('位置参数'), default=list, blank=True)
    kwargs = models.JSONField(_('关键字参数'), default=dict, blank=True)
    
    # 状态
    is_active = models.BooleanField(_('启用'), default=True)
    
    # 执行统计
    total_runs = models.IntegerField(_('总执行次数'), default=0)
    success_runs = models.IntegerField(_('成功次数'), default=0)
    failed_runs = models.IntegerField(_('失败次数'), default=0)
    
    # 时间
    last_run_at = models.DateTimeField(_('上次执行时间'), null=True, blank=True)
    next_run_at = models.DateTimeField(_('下次执行时间'), null=True, blank=True)
    created_at = models.DateTimeField(_('创建时间'), auto_now_add=True)
    updated_at = models.DateTimeField(_('更新时间'), auto_now=True)
    
    class Meta:
        verbose_name = _('定时任务')
        verbose_name_plural = _('定时任务')
        ordering = ['-created_at']
    
    def __str__(self):
        return self.name
    
    def calculate_next_run(self):
        """计算下次执行时间"""
        from django.utils import timezone
        from datetime import timedelta
        import croniter
        
        now = timezone.now()
        
        if self.schedule_type == 'once':
            # 一次性任务
            if not self.last_run_at:
                self.next_run_at = now
            else:
                self.next_run_at = None
        
        elif self.schedule_type == 'interval':
            # 间隔执行
            interval_seconds = self.schedule_config.get('interval_seconds', 3600)
            if self.last_run_at:
                self.next_run_at = self.last_run_at + timedelta(seconds=interval_seconds)
            else:
                self.next_run_at = now
        
        elif self.schedule_type == 'cron':
            # Cron表达式
            cron_expression = self.schedule_config.get('cron_expression', '0 0 * * *')
            try:
                cron = croniter.croniter(cron_expression, now)
                self.next_run_at = cron.get_next(timezone.datetime)
            except:
                self.next_run_at = None
        
        elif self.schedule_type == 'daily':
            # 每日执行
            hour = self.schedule_config.get('hour', 0)
            minute = self.schedule_config.get('minute', 0)
            next_run = now.replace(hour=hour, minute=minute, second=0, microsecond=0)
            if next_run <= now:
                next_run += timedelta(days=1)
            self.next_run_at = next_run
        
        elif self.schedule_type == 'weekly':
            # 每周执行
            weekday = self.schedule_config.get('weekday', 0)  # 0=Monday
            hour = self.schedule_config.get('hour', 0)
            minute = self.schedule_config.get('minute', 0)
            
            days_ahead = weekday - now.weekday()
            if days_ahead <= 0:
                days_ahead += 7
            
            next_run = now + timedelta(days=days_ahead)
            next_run = next_run.replace(hour=hour, minute=minute, second=0, microsecond=0)
            self.next_run_at = next_run
        
        elif self.schedule_type == 'monthly':
            # 每月执行
            day = self.schedule_config.get('day', 1)
            hour = self.schedule_config.get('hour', 0)
            minute = self.schedule_config.get('minute', 0)
            
            try:
                next_run = now.replace(day=day, hour=hour, minute=minute, second=0, microsecond=0)
                if next_run <= now:
                    if now.month == 12:
                        next_run = next_run.replace(year=now.year + 1, month=1)
                    else:
                        next_run = next_run.replace(month=now.month + 1)
                self.next_run_at = next_run
            except ValueError:
                # 处理月份天数不足的情况
                self.next_run_at = None
        
        self.save(update_fields=['next_run_at'])


class TaskResult(models.Model):
    """任务执行结果"""
    
    task = models.ForeignKey(
        TaskQueue,
        on_delete=models.CASCADE,
        related_name='execution_results',
        verbose_name=_('任务')
    )
    
    # 执行信息
    worker_id = models.CharField(_('工作进程ID'), max_length=100)
    started_at = models.DateTimeField(_('开始时间'))
    completed_at = models.DateTimeField(_('完成时间'))
    duration = models.FloatField(_('执行时长(秒)'))
    
    # 结果
    success = models.BooleanField(_('是否成功'))
    result = models.JSONField(_('执行结果'), null=True, blank=True)
    error_message = models.TextField(_('错误信息'), blank=True)
    traceback = models.TextField(_('错误堆栈'), blank=True)
    
    # 资源使用
    memory_usage = models.FloatField(_('内存使用(MB)'), null=True, blank=True)
    cpu_usage = models.FloatField(_('CPU使用率'), null=True, blank=True)
    
    # 时间
    created_at = models.DateTimeField(_('创建时间'), auto_now_add=True)
    
    class Meta:
        verbose_name = _('任务执行结果')
        verbose_name_plural = _('任务执行结果')
        ordering = ['-created_at']
    
    def __str__(self):
        return f'{self.task.name} - {self.started_at}'
