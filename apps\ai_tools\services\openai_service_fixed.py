"""
OpenAI API集成服务 - 修复版本
解决httpx 0.28.0版本中proxies参数被移除的问题
"""
import openai
import httpx
import logging
import time
from typing import Dict, Any, Optional, List
from django.conf import settings
from apps.config.services import api_config

logger = logging.getLogger(__name__)


class CompatibleHTTPClient(httpx.Client):
    """兼容的HTTP客户端，处理proxies参数问题"""

    def __init__(self, *args, **kwargs):
        # 移除可能导致问题的proxies参数
        kwargs.pop("proxies", None)
        super().__init__(*args, **kwargs)


class OpenAIService:
    """OpenAI API服务 - 修复版本"""

    def __init__(self):
        self.client = None
        self._initialize_client()

    def _initialize_client(self):
        """初始化OpenAI客户端 - 修复版本"""
        try:
            config = api_config.get_openai_config()

            if not config['api_key']:
                logger.warning("OpenAI API密钥未配置")
                return

            # 创建兼容的HTTP客户端
            try:
                http_client = CompatibleHTTPClient()

                # 使用兼容的HTTP客户端初始化OpenAI客户端
                base_url = config.get('base_url', 'https://api.openai.com/v1')

                init_kwargs = {
                    'api_key': config['api_key'],
                    'http_client': http_client
                }

                # 只有在非默认URL时才添加base_url
                if base_url and base_url.strip() != 'https://api.openai.com/v1':
                    init_kwargs['base_url'] = base_url

                self.client = openai.OpenAI(**init_kwargs)
                logger.info("OpenAI客户端初始化成功 (使用兼容HTTP客户端)")

            except Exception as e:
                logger.warning(f"使用兼容HTTP客户端初始化失败，尝试基本初始化: {e}")

                # 如果兼容客户端失败，尝试最基本的初始化
                try:
                    self.client = openai.OpenAI(api_key=config['api_key'])
                    logger.info("OpenAI客户端基本初始化成功")
                except Exception as e2:
                    logger.error(f"OpenAI客户端基本初始化也失败: {e2}")
                    self.client = None

        except Exception as e:
            logger.error(f"OpenAI服务初始化过程失败: {e}")
            self.client = None

    def is_available(self) -> bool:
        """检查服务是否可用"""
        return self.client is not None and api_config.validate_openai_config()
