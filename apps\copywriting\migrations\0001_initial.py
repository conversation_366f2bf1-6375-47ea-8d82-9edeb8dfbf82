# Generated by Django 5.2.4 on 2025-07-15 02:41

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Copywriting",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "title",
                    models.CharField(
                        blank=True,
                        help_text="文案的标题或主题",
                        max_length=200,
                        verbose_name="文案标题",
                    ),
                ),
                (
                    "scene_type",
                    models.CharField(
                        choices=[
                            ("general", "通用"),
                            ("knowledge", "知识科普"),
                            ("emotion", "情感专家"),
                            ("ecommerce", "电商营销"),
                            ("blessing", "祝福问候"),
                            ("health", "健康大师"),
                        ],
                        default="general",
                        help_text="文案生成的场景类型",
                        max_length=20,
                        verbose_name="场景类型",
                    ),
                ),
                (
                    "theme_description",
                    models.TextField(help_text="文案生成的主题描述和要求", verbose_name="主题描述"),
                ),
                (
                    "word_count",
                    models.PositiveIntegerField(
                        choices=[
                            (100, "100字 ~ 60秒"),
                            (300, "300字 ~ 120秒"),
                            (500, "500字 ~ 300秒"),
                            (1000, "1000字 ~ 600秒"),
                        ],
                        default=300,
                        help_text="期望生成的文案字数",
                        verbose_name="字数要求",
                    ),
                ),
                (
                    "language",
                    models.CharField(
                        choices=[
                            ("zh-cn", "中文（简体）"),
                            ("zh-tw", "中文（繁体）"),
                            ("en-us", "英语"),
                        ],
                        default="zh-cn",
                        help_text="文案生成的语言",
                        max_length=20,
                        verbose_name="语言",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "待生成"),
                            ("generating", "生成中"),
                            ("completed", "已完成"),
                            ("failed", "生成失败"),
                        ],
                        default="pending",
                        help_text="文案生成的状态",
                        max_length=20,
                        verbose_name="生成状态",
                    ),
                ),
                (
                    "generated_content",
                    models.TextField(
                        blank=True, help_text="AI生成的文案内容", verbose_name="生成内容"
                    ),
                ),
                (
                    "actual_word_count",
                    models.PositiveIntegerField(
                        blank=True,
                        help_text="生成文案的实际字数",
                        null=True,
                        verbose_name="实际字数",
                    ),
                ),
                (
                    "error_message",
                    models.TextField(
                        blank=True, help_text="生成失败时的错误信息", verbose_name="错误信息"
                    ),
                ),
                (
                    "computing_power_consumed",
                    models.PositiveIntegerField(
                        default=0, help_text="本次生成消耗的算力点数", verbose_name="消耗算力"
                    ),
                ),
                (
                    "is_saved_as_draft",
                    models.BooleanField(
                        default=False, help_text="是否保存为草稿", verbose_name="保存为草稿"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "completed_at",
                    models.DateTimeField(
                        blank=True,
                        help_text="文案生成完成的时间",
                        null=True,
                        verbose_name="完成时间",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="copywritings",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="用户",
                    ),
                ),
            ],
            options={
                "verbose_name": "智能文案",
                "verbose_name_plural": "智能文案",
                "db_table": "copywritings",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="CopywritingHistory",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "version",
                    models.PositiveIntegerField(
                        default=1, help_text="文案的版本号", verbose_name="版本号"
                    ),
                ),
                (
                    "content",
                    models.TextField(help_text="此版本的文案内容", verbose_name="文案内容"),
                ),
                (
                    "word_count",
                    models.PositiveIntegerField(
                        help_text="此版本文案的字数", verbose_name="字数"
                    ),
                ),
                (
                    "generation_params",
                    models.JSONField(
                        blank=True,
                        default=dict,
                        help_text="生成此版本时使用的参数",
                        verbose_name="生成参数",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "copywriting",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="history",
                        to="copywriting.copywriting",
                        verbose_name="文案",
                    ),
                ),
            ],
            options={
                "verbose_name": "文案历史",
                "verbose_name_plural": "文案历史",
                "db_table": "copywriting_history",
                "ordering": ["-version"],
            },
        ),
        migrations.CreateModel(
            name="CopywritingTemplate",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        help_text="文案模板的名称", max_length=100, verbose_name="模板名称"
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        blank=True, help_text="模板的详细描述", verbose_name="模板描述"
                    ),
                ),
                (
                    "scene_type",
                    models.CharField(
                        choices=[
                            ("general", "通用"),
                            ("knowledge", "知识科普"),
                            ("emotion", "情感专家"),
                            ("ecommerce", "电商营销"),
                            ("blessing", "祝福问候"),
                            ("health", "健康大师"),
                        ],
                        help_text="模板适用的场景类型",
                        max_length=20,
                        verbose_name="适用场景",
                    ),
                ),
                (
                    "template_content",
                    models.TextField(help_text="文案模板的内容，支持变量替换", verbose_name="模板内容"),
                ),
                (
                    "variables",
                    models.JSONField(
                        blank=True,
                        default=dict,
                        help_text="模板中使用的变量定义",
                        verbose_name="模板变量",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True, help_text="是否启用此模板", verbose_name="是否启用"
                    ),
                ),
                (
                    "usage_count",
                    models.PositiveIntegerField(
                        default=0, help_text="模板被使用的次数", verbose_name="使用次数"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="created_templates",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="创建者",
                    ),
                ),
            ],
            options={
                "verbose_name": "文案模板",
                "verbose_name_plural": "文案模板",
                "db_table": "copywriting_templates",
                "ordering": ["-usage_count", "name"],
            },
        ),
        migrations.CreateModel(
            name="TextExtraction",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "source_type",
                    models.CharField(
                        choices=[
                            ("image", "图片"),
                            ("video", "视频"),
                            ("audio", "音频"),
                            ("url", "网页链接"),
                            ("file", "文档文件"),
                        ],
                        help_text="文案提取的来源类型",
                        max_length=20,
                        verbose_name="来源类型",
                    ),
                ),
                (
                    "source_file",
                    models.FileField(
                        blank=True,
                        help_text="上传的来源文件",
                        null=True,
                        upload_to="extractions/sources/",
                        verbose_name="来源文件",
                    ),
                ),
                (
                    "source_url",
                    models.URLField(
                        blank=True, help_text="来源的网页链接", null=True, verbose_name="来源链接"
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "待处理"),
                            ("processing", "处理中"),
                            ("completed", "已完成"),
                            ("failed", "处理失败"),
                        ],
                        default="pending",
                        help_text="文案提取的处理状态",
                        max_length=20,
                        verbose_name="处理状态",
                    ),
                ),
                (
                    "extracted_text",
                    models.TextField(
                        blank=True, help_text="提取出的文本内容", verbose_name="提取文本"
                    ),
                ),
                (
                    "confidence_score",
                    models.FloatField(
                        blank=True,
                        help_text="文本提取的置信度分数（0-1）",
                        null=True,
                        verbose_name="置信度",
                    ),
                ),
                (
                    "error_message",
                    models.TextField(
                        blank=True, help_text="处理失败时的错误信息", verbose_name="错误信息"
                    ),
                ),
                (
                    "computing_power_consumed",
                    models.PositiveIntegerField(
                        default=0, help_text="本次提取消耗的算力点数", verbose_name="消耗算力"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "completed_at",
                    models.DateTimeField(
                        blank=True, help_text="提取完成的时间", null=True, verbose_name="完成时间"
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="text_extractions",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="用户",
                    ),
                ),
            ],
            options={
                "verbose_name": "文案提取",
                "verbose_name_plural": "文案提取",
                "db_table": "text_extractions",
                "ordering": ["-created_at"],
            },
        ),
        migrations.AddIndex(
            model_name="copywriting",
            index=models.Index(
                fields=["user", "status"], name="copywriting_user_id_eafa1f_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="copywriting",
            index=models.Index(
                fields=["scene_type", "created_at"],
                name="copywriting_scene_t_808a57_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="copywriting",
            index=models.Index(
                fields=["status", "created_at"], name="copywriting_status_2e8053_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="copywritinghistory",
            unique_together={("copywriting", "version")},
        ),
        migrations.AddIndex(
            model_name="textextraction",
            index=models.Index(
                fields=["user", "status"], name="text_extrac_user_id_ee5378_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="textextraction",
            index=models.Index(
                fields=["source_type", "created_at"],
                name="text_extrac_source__6773ef_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="textextraction",
            index=models.Index(
                fields=["status", "created_at"], name="text_extrac_status_47dd0f_idx"
            ),
        ),
    ]
