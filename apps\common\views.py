"""
公共视图
"""
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status, permissions
from django.conf import settings
from django.utils import timezone
from django.contrib.admin.views.decorators import staff_member_required
from django.shortcuts import render
from django.utils.decorators import method_decorator
from django.views.generic import TemplateView
from django.db.models import Count, Sum
from datetime import timedelta


class HealthCheckView(APIView):
    """健康检查接口"""
    permission_classes = [permissions.AllowAny]
    
    def get(self, request):
        """健康检查"""
        return Response({
            'status': 'healthy',
            'timestamp': timezone.now(),
            'version': '1.0.0',
            'service': 'AI数字人SaaS平台'
        })


class SystemInfoView(APIView):
    """系统信息接口"""
    permission_classes = [permissions.IsAdminUser]
    
    def get(self, request):
        """获取系统信息"""
        from django.db import connection
        from apps.users.models import User
        from apps.avatars.models import DigitalAvatar
        from apps.voices.models import VoiceModel
        from apps.videos.models import VideoProduction
        from apps.copywriting.models import Copywriting
        
        # 统计数据
        stats = {
            'users': {
                'total': User.objects.count(),
                'active': User.objects.filter(is_active=True).count(),
                'verified': User.objects.filter(is_verified=True).count(),
            },
            'avatars': {
                'total': DigitalAvatar.objects.count(),
                'public': DigitalAvatar.objects.filter(is_public=True).count(),
                'active': DigitalAvatar.objects.filter(is_active=True).count(),
            },
            'voices': {
                'total': VoiceModel.objects.count(),
                'public': VoiceModel.objects.filter(is_public=True).count(),
                'cloned': VoiceModel.objects.filter(is_cloned=True).count(),
            },
            'videos': {
                'total': VideoProduction.objects.count(),
                'completed': VideoProduction.objects.filter(status='completed').count(),
                'public': VideoProduction.objects.filter(is_public=True).count(),
            },
            'copywriting': {
                'total': Copywriting.objects.count(),
                'completed': Copywriting.objects.filter(status='completed').count(),
            }
        }
        
        # 数据库信息
        with connection.cursor() as cursor:
            cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
            table_count = cursor.fetchone()[0]
        
        return Response({
            'system': {
                'debug': settings.DEBUG,
                'timezone': str(settings.TIME_ZONE),
                'language': settings.LANGUAGE_CODE,
                'database_tables': table_count,
            },
            'statistics': stats,
            'timestamp': timezone.now()
        })


class APIDocumentationView(APIView):
    """API文档接口"""
    permission_classes = [permissions.AllowAny]
    
    def get(self, request):
        """获取API文档"""
        api_docs = {
            'title': 'AI数字人SaaS平台 API文档',
            'version': '1.0.0',
            'description': '基于Django REST Framework的AI数字人SaaS平台API',
            'base_url': request.build_absolute_uri('/api/v1/'),
            'endpoints': {
                'authentication': {
                    'login': 'POST /api/v1/auth/login/',
                    'logout': 'POST /api/v1/auth/logout/',
                    'register': 'POST /api/v1/auth/register/',
                    'profile': 'GET/PUT /api/v1/auth/profile/',
                },
                'users': {
                    'list': 'GET /api/v1/users/',
                    'detail': 'GET /api/v1/users/{id}/',
                    'add_computing_power': 'POST /api/v1/users/{id}/add_computing_power/',
                    'consume_computing_power': 'POST /api/v1/users/{id}/consume_computing_power/',
                },
                'avatars': {
                    'list': 'GET /api/v1/avatars/',
                    'detail': 'GET /api/v1/avatars/{id}/',
                    'public_avatars': 'GET /api/v1/avatars/public_avatars/',
                    'my_avatars': 'GET /api/v1/avatars/my_avatars/',
                    'use_avatar': 'POST /api/v1/avatars/{id}/use_avatar/',
                    'categories': 'GET /api/v1/avatar-categories/',
                    'tags': 'GET /api/v1/avatar-tags/',
                },
                'voices': {
                    'list': 'GET /api/v1/voice-models/',
                    'detail': 'GET /api/v1/voice-models/{id}/',
                    'public_voices': 'GET /api/v1/voice-models/public_voices/',
                    'my_voices': 'GET /api/v1/voice-models/my_voices/',
                    'synthesize': 'POST /api/v1/voice-models/{id}/synthesize/',
                    'clone_requests': 'GET/POST /api/v1/voice-clone-requests/',
                    'synthesis_tasks': 'GET /api/v1/voice-synthesis-tasks/',
                },
                'videos': {
                    'list': 'GET /api/v1/video-productions/',
                    'detail': 'GET /api/v1/video-productions/{id}/',
                    'create': 'POST /api/v1/video-productions/',
                    'start_generation': 'POST /api/v1/video-productions/{id}/start_generation/',
                    'toggle_favorite': 'POST /api/v1/video-productions/{id}/toggle_favorite/',
                    'my_favorites': 'GET /api/v1/video-productions/my_favorites/',
                    'public_videos': 'GET /api/v1/video-productions/public_videos/',
                },
                'copywriting': {
                    'list': 'GET /api/v1/copywriting/',
                    'detail': 'GET /api/v1/copywriting/{id}/',
                    'create': 'POST /api/v1/copywriting/',
                    'generate': 'POST /api/v1/copywriting/{id}/generate/',
                    'regenerate': 'POST /api/v1/copywriting/{id}/regenerate/',
                    'templates': 'GET /api/v1/copywriting-templates/',
                    'text_extraction': 'GET/POST /api/v1/text-extractions/',
                },
                'ai_tools': {
                    'ai_chat': 'POST /api/v1/ai-chat/',
                    'ai_painting': 'POST /api/v1/ai-painting/',
                    'ai_music': 'POST /api/v1/ai-music/',
                    'ai_video': 'POST /api/v1/ai-video/',
                    'face_fusion': 'POST /api/v1/face-fusion/',
                    'photo_digital_human': 'POST /api/v1/photo-digital-human/',
                    'text_extraction': 'POST /api/v1/text-extraction/',
                },
                'system': {
                    'health': 'GET /api/v1/health/',
                    'system_info': 'GET /api/v1/system-info/',
                    'api_docs': 'GET /api/v1/api-docs/',
                }
            },
            'authentication': {
                'type': 'Session Authentication / JWT Token',
                'header': 'Authorization: Bearer <token>',
                'login_required': True
            },
            'response_format': {
                'success': {
                    'status': 'success',
                    'data': '...',
                    'message': 'Optional message'
                },
                'error': {
                    'status': 'error',
                    'error': 'Error message',
                    'details': 'Optional error details'
                }
            },
            'pagination': {
                'page_size': 20,
                'page_param': 'page',
                'response_format': {
                    'count': 'Total count',
                    'next': 'Next page URL',
                    'previous': 'Previous page URL',
                    'results': 'Array of results'
                }
            }
        }
        
        return Response(api_docs)


@method_decorator(staff_member_required, name='dispatch')
class DashboardView(TemplateView):
    """仪表盘视图"""
    template_name = 'admin/dashboard.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # 导入模型
        from apps.users.models import User, ComputingPowerLog
        from apps.avatars.models import DigitalAvatar, AvatarUsageLog
        from apps.voices.models import VoiceModel, VoiceSynthesisTask
        from apps.videos.models import VideoProduction
        from apps.copywriting.models import Copywriting

        # 基础统计
        context.update({
            'total_users': User.objects.count(),
            'active_users': User.objects.filter(is_active=True).count(),
            'verified_users': User.objects.filter(is_verified=True).count(),
            'total_avatars': DigitalAvatar.objects.count(),
            'public_avatars': DigitalAvatar.objects.filter(is_public=True).count(),
            'total_voices': VoiceModel.objects.count(),
            'total_videos': VideoProduction.objects.count(),
            'total_copywriting': Copywriting.objects.count(),
        })

        # 今日统计
        today = timezone.now().date()
        context.update({
            'today_new_users': User.objects.filter(date_joined__date=today).count(),
            'today_avatar_usage': AvatarUsageLog.objects.filter(created_at__date=today).count(),
            'today_video_created': VideoProduction.objects.filter(created_at__date=today).count(),
            'today_copywriting_created': Copywriting.objects.filter(created_at__date=today).count(),
        })

        # 算力统计
        total_computing_power = User.objects.aggregate(
            total=Sum('computing_power')
        )['total'] or 0

        total_consumed_power = ComputingPowerLog.objects.filter(
            operation_type='consume'
        ).aggregate(total=Sum('amount'))['total'] or 0

        context.update({
            'total_computing_power': total_computing_power,
            'total_consumed_power': total_consumed_power,
        })

        # 最近7天的用户注册趋势
        seven_days_ago = timezone.now() - timedelta(days=7)
        daily_registrations = []
        for i in range(7):
            date = (seven_days_ago + timedelta(days=i)).date()
            count = User.objects.filter(date_joined__date=date).count()
            daily_registrations.append({
                'date': date.strftime('%m-%d'),
                'count': count
            })
        context['daily_registrations'] = daily_registrations

        # 热门数字人形象
        popular_avatars = DigitalAvatar.objects.filter(
            is_active=True
        ).order_by('-usage_count')[:5]
        context['popular_avatars'] = popular_avatars

        # 最近活动
        recent_activities = []

        # 最近的用户注册
        recent_users = User.objects.order_by('-date_joined')[:5]
        for user in recent_users:
            recent_activities.append({
                'type': 'user_register',
                'message': f'新用户 {user.username} 注册',
                'time': user.date_joined
            })

        # 最近的视频创建
        recent_videos = VideoProduction.objects.order_by('-created_at')[:5]
        for video in recent_videos:
            recent_activities.append({
                'type': 'video_create',
                'message': f'用户 {video.user.username} 创建视频 "{video.title}"',
                'time': video.created_at
            })

        # 按时间排序
        recent_activities.sort(key=lambda x: x['time'], reverse=True)
        context['recent_activities'] = recent_activities[:10]

        return context
