"""
用户相关API路由
"""
from django.urls import path, include
from rest_framework.routers import Default<PERSON>out<PERSON>
from rest_framework_simplejwt.views import TokenRefreshView
from . import views

router = DefaultRouter()
router.register(r'users', views.UserViewSet)
router.register(r'computing-power-logs', views.ComputingPowerLogViewSet)
router.register(r'user-packages', views.UserPackageViewSet)

urlpatterns = [
    path('', include(router.urls)),
    # 认证相关
    path('auth/login/', views.LoginView.as_view(), name='login'),
    path('auth/logout/', views.LogoutView.as_view(), name='logout'),
    path('auth/register/', views.RegisterView.as_view(), name='register'),
    path('auth/token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    path('auth/profile/', views.ProfileView.as_view(), name='profile'),
    path('auth/change-password/', views.ChangePasswordView.as_view(), name='change-password'),
    path('auth/upload-avatar/', views.UploadAvatarView.as_view(), name='upload-avatar'),

    # 算力管理
    path('computing-power/', views.ComputingPowerView.as_view(), name='computing-power'),
    path('consume-computing-power/', views.ConsumeComputingPowerView.as_view(), name='consume-computing-power'),
    path('add-computing-power/', views.AddComputingPowerView.as_view(), name='add-computing-power'),
    path('computing-power-logs/', views.ComputingPowerLogViewSet.as_view({'get': 'list'}), name='computing-power-logs'),

    # 套餐管理
    path('package-info/', views.UserPackageViewSet.as_view({'get': 'list'}), name='package-info'),
    path('packages/', views.UserPackageViewSet.as_view({'get': 'list'}), name='packages'),
    path('purchase-package/', views.UserPackageViewSet.as_view({'post': 'create'}), name='purchase-package'),

    # 用户统计和历史
    path('stats/', views.UserStatsView.as_view(), name='user-stats'),
    path('usage-history/', views.UserUsageHistoryView.as_view(), name='user-usage-history'),
]
