"""
文件上传视图
"""
import os
import hashlib
import uuid
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.core.files.storage import default_storage
from django.core.files.base import ContentFile
from django.utils import timezone
from datetime import timed<PERSON>ta
from rest_framework import status, permissions
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.parsers import MultiPartParser, FormParser
from apps.common.throttling import UploadRateThrottle

from .models import UploadFile, ChunkedUpload, UploadChunk
from .serializers import UploadFileSerializer, ChunkedUploadSerializer


class SimpleUploadView(APIView):
    """简单文件上传视图"""
    permission_classes = [permissions.IsAuthenticated]
    parser_classes = [MultiPartParser, FormParser]
    throttle_classes = [UploadRateThrottle]
    
    def post(self, request):
        """上传文件"""
        file_obj = request.FILES.get('file')
        upload_type = request.data.get('upload_type', 'other')
        
        if not file_obj:
            return Response(
                {'error': '请选择要上传的文件'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # 验证文件大小（根据文件类型设置不同限制）
        if upload_type == 'video':
            max_size = 500 * 1024 * 1024  # 视频文件500MB
        elif upload_type == 'audio':
            max_size = 100 * 1024 * 1024  # 音频文件100MB
        elif upload_type == 'image':
            max_size = 20 * 1024 * 1024   # 图片文件20MB
        else:
            max_size = 100 * 1024 * 1024  # 其他文件100MB
        if file_obj.size > max_size:
            return Response(
                {'error': f'文件大小不能超过{max_size // (1024*1024)}MB'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # 验证文件类型
        allowed_types = self.get_allowed_types(upload_type)
        if allowed_types and file_obj.content_type not in allowed_types:
            return Response(
                {'error': f'不支持的文件类型: {file_obj.content_type}'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # 计算文件哈希
        file_hash = self.calculate_file_hash(file_obj)
        
        # 创建上传记录
        upload_file = UploadFile.objects.create(
            user=request.user,
            upload_type=upload_type,
            original_filename=file_obj.name,
            file=file_obj,
            file_size=file_obj.size,
            file_type=file_obj.content_type,
            file_hash=file_hash,
            status='completed',
            progress=100
        )
        upload_file.mark_completed()
        
        serializer = UploadFileSerializer(upload_file)
        return Response(serializer.data, status=status.HTTP_201_CREATED)
    
    def get_allowed_types(self, upload_type):
        """获取允许的文件类型"""
        type_mapping = {
            'image': ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
            'video': ['video/mp4', 'video/avi', 'video/mov', 'video/wmv'],
            'voice': ['audio/mp3', 'audio/wav', 'audio/mpeg', 'audio/ogg'],
            'document': ['application/pdf', 'text/plain', 'application/msword'],
        }
        return type_mapping.get(upload_type)
    
    def calculate_file_hash(self, file_obj):
        """计算文件哈希值"""
        hash_md5 = hashlib.md5()
        for chunk in file_obj.chunks():
            hash_md5.update(chunk)
        file_obj.seek(0)  # 重置文件指针
        return hash_md5.hexdigest()


class ChunkedUploadInitView(APIView):
    """分块上传初始化视图"""
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request):
        """初始化分块上传"""
        filename = request.data.get('filename')
        total_size = request.data.get('total_size')
        upload_type = request.data.get('upload_type', 'other')
        chunk_size = request.data.get('chunk_size', 1024*1024)  # 默认1MB
        
        if not filename or not total_size:
            return Response(
                {'error': '请提供文件名和文件大小'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # 验证文件大小（根据文件类型设置不同限制）
        if upload_type == 'video':
            max_size = 2 * 1024 * 1024 * 1024  # 视频文件2GB
        elif upload_type == 'audio':
            max_size = 500 * 1024 * 1024       # 音频文件500MB
        elif upload_type == 'image':
            max_size = 50 * 1024 * 1024        # 图片文件50MB
        else:
            max_size = 1024 * 1024 * 1024      # 其他文件1GB
        if int(total_size) > max_size:
            return Response(
                {'error': f'文件大小不能超过{max_size // (1024*1024)}MB'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # 创建分块上传记录
        expires_at = timezone.now() + timedelta(hours=24)  # 24小时过期
        
        chunked_upload = ChunkedUpload.objects.create(
            user=request.user,
            filename=filename,
            total_size=int(total_size),
            chunk_size=int(chunk_size),
            upload_type=upload_type,
            expires_at=expires_at
        )
        
        serializer = ChunkedUploadSerializer(chunked_upload)
        return Response(serializer.data, status=status.HTTP_201_CREATED)


class ChunkedUploadView(APIView):
    """分块上传视图"""
    permission_classes = [permissions.IsAuthenticated]
    parser_classes = [MultiPartParser, FormParser]
    throttle_classes = [UploadRateThrottle]

    def post(self, request, upload_id):
        """上传文件块"""
        try:
            chunked_upload = ChunkedUpload.objects.get(
                upload_id=upload_id,
                user=request.user
            )
        except ChunkedUpload.DoesNotExist:
            return Response(
                {'error': '上传会话不存在'},
                status=status.HTTP_404_NOT_FOUND
            )

        # 检查是否过期
        if chunked_upload.is_expired():
            return Response(
                {'error': '上传会话已过期'},
                status=status.HTTP_400_BAD_REQUEST
            )

        chunk_number = request.data.get('chunk_number')
        chunk_data = request.FILES.get('chunk')

        if chunk_number is None or not chunk_data:
            return Response(
                {'error': '请提供块编号和块数据'},
                status=status.HTTP_400_BAD_REQUEST
            )

        chunk_number = int(chunk_number)

        # 检查块是否已存在
        if UploadChunk.objects.filter(
            chunked_upload=chunked_upload,
            chunk_number=chunk_number
        ).exists():
            return Response(
                {'message': '块已存在', 'chunk_number': chunk_number},
                status=status.HTTP_200_OK
            )

        # 保存块数据
        chunk_data_bytes = chunk_data.read()
        checksum = hashlib.md5(chunk_data_bytes).hexdigest()

        UploadChunk.objects.create(
            chunked_upload=chunked_upload,
            chunk_number=chunk_number,
            chunk_data=chunk_data_bytes,
            chunk_size=len(chunk_data_bytes),
            checksum=checksum
        )

        # 更新上传进度
        chunked_upload.uploaded_size += len(chunk_data_bytes)
        chunked_upload.save(update_fields=['uploaded_size'])

        # 检查是否所有块都已上传
        total_chunks = (chunked_upload.total_size + chunked_upload.chunk_size - 1) // chunked_upload.chunk_size
        uploaded_chunks = chunked_upload.chunks.count()

        if uploaded_chunks >= total_chunks:
            # 合并文件
            self.merge_chunks(chunked_upload)

        return Response({
            'message': '块上传成功',
            'chunk_number': chunk_number,
            'progress': chunked_upload.progress_percentage,
            'is_completed': chunked_upload.is_completed
        })

    def merge_chunks(self, chunked_upload):
        """合并文件块"""
        try:
            # 按顺序获取所有块
            chunks = chunked_upload.chunks.order_by('chunk_number')

            # 创建临时文件
            temp_filename = f"temp_{chunked_upload.upload_id}.tmp"
            temp_path = os.path.join('uploads', 'temp', temp_filename)

            # 确保目录存在
            os.makedirs(os.path.dirname(temp_path), exist_ok=True)

            # 合并块数据
            with open(temp_path, 'wb') as temp_file:
                for chunk in chunks:
                    temp_file.write(chunk.chunk_data)

            # 创建最终的上传文件记录
            with open(temp_path, 'rb') as temp_file:
                file_content = ContentFile(temp_file.read())
                file_content.name = chunked_upload.filename

                upload_file = UploadFile.objects.create(
                    user=chunked_upload.user,
                    upload_type=chunked_upload.upload_type,
                    original_filename=chunked_upload.filename,
                    file=file_content,
                    file_size=chunked_upload.total_size,
                    file_type=chunked_upload.file_type,
                    status='completed',
                    progress=100
                )
                upload_file.mark_completed()

            # 标记分块上传为完成
            chunked_upload.is_completed = True
            chunked_upload.save(update_fields=['is_completed'])

            # 清理临时文件和块数据
            if os.path.exists(temp_path):
                os.remove(temp_path)
            chunked_upload.chunks.all().delete()

        except Exception as e:
            chunked_upload.mark_failed(str(e))


class UploadProgressView(APIView):
    """上传进度查询视图"""
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request, upload_id):
        """获取上传进度"""
        try:
            chunked_upload = ChunkedUpload.objects.get(
                upload_id=upload_id,
                user=request.user
            )

            serializer = ChunkedUploadSerializer(chunked_upload)
            return Response(serializer.data)

        except ChunkedUpload.DoesNotExist:
            return Response(
                {'error': '上传会话不存在'},
                status=status.HTTP_404_NOT_FOUND
            )


class UploadListView(APIView):
    """用户上传文件列表"""
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        """获取用户上传的文件列表"""
        upload_type = request.query_params.get('upload_type')

        queryset = UploadFile.objects.filter(user=request.user)

        if upload_type:
            queryset = queryset.filter(upload_type=upload_type)

        files = queryset.order_by('-created_at')[:50]  # 最近50个文件
        serializer = UploadFileSerializer(files, many=True)

        return Response(serializer.data)
