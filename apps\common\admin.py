"""
通用模块管理后台配置
"""
from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from django.utils.html import format_html
from unfold.admin import ModelAdmin
from unfold.contrib.filters.admin import RangeDateFilter, ChoicesDropdownFilter
from .models import SystemLog, UserActionLog, SecurityLog


@admin.register(SystemLog)
class SystemLogAdmin(ModelAdmin):
    """系统日志管理"""
    
    list_display = (
        'level', 'logger', 'message_short', 'user', 'ip_address',
        'timestamp'
    )
    
    list_filter = (
        ('level', ChoicesDropdownFilter),
        ('logger', ChoicesDropdownFilter),
        ('timestamp', RangeDateFilter),
        'user',
    )
    
    search_fields = ('message', 'logger', 'user__username', 'ip_address')
    
    readonly_fields = (
        'level', 'logger', 'message', 'module', 'function', 'line_number',
        'request_id', 'user', 'ip_address', 'user_agent', 'url', 'method',
        'status_code', 'exception_type', 'exception_message', 'traceback',
        'extra_data', 'timestamp'
    )
    
    fieldsets = (
        (_('基本信息'), {
            'fields': ('level', 'logger', 'message', 'timestamp')
        }),
        (_('代码位置'), {
            'fields': ('module', 'function', 'line_number'),
            'classes': ('collapse',)
        }),
        (_('请求信息'), {
            'fields': ('request_id', 'user', 'ip_address', 'user_agent', 'url', 'method', 'status_code'),
            'classes': ('collapse',)
        }),
        (_('异常信息'), {
            'fields': ('exception_type', 'exception_message', 'traceback'),
            'classes': ('collapse',)
        }),
        (_('额外数据'), {
            'fields': ('extra_data',),
            'classes': ('collapse',)
        }),
    )
    
    def message_short(self, obj):
        """显示简短消息"""
        return obj.message[:100] + '...' if len(obj.message) > 100 else obj.message
    message_short.short_description = _('消息')
    
    def has_add_permission(self, request):
        return False
    
    def has_change_permission(self, request, obj=None):
        return False


@admin.register(UserActionLog)
class UserActionLogAdmin(ModelAdmin):
    """用户操作日志管理"""
    
    list_display = (
        'user', 'action', 'description_short', 'object_type',
        'ip_address', 'timestamp'
    )
    
    list_filter = (
        ('action', ChoicesDropdownFilter),
        ('object_type', ChoicesDropdownFilter),
        ('timestamp', RangeDateFilter),
        'user',
    )
    
    search_fields = ('user__username', 'description', 'object_id', 'ip_address')
    
    readonly_fields = (
        'user', 'action', 'description', 'ip_address', 'user_agent',
        'object_type', 'object_id', 'extra_data', 'timestamp'
    )
    
    fieldsets = (
        (_('基本信息'), {
            'fields': ('user', 'action', 'description', 'timestamp')
        }),
        (_('请求信息'), {
            'fields': ('ip_address', 'user_agent'),
            'classes': ('collapse',)
        }),
        (_('操作对象'), {
            'fields': ('object_type', 'object_id'),
            'classes': ('collapse',)
        }),
        (_('额外数据'), {
            'fields': ('extra_data',),
            'classes': ('collapse',)
        }),
    )
    
    def description_short(self, obj):
        """显示简短描述"""
        return obj.description[:50] + '...' if len(obj.description) > 50 else obj.description
    description_short.short_description = _('描述')
    
    def has_add_permission(self, request):
        return False
    
    def has_change_permission(self, request, obj=None):
        return False


@admin.register(SecurityLog)
class SecurityLogAdmin(ModelAdmin):
    """安全日志管理"""
    
    list_display = (
        'event_type', 'severity', 'description_short', 'user', 'username',
        'ip_address', 'is_resolved', 'timestamp'
    )
    
    list_filter = (
        ('event_type', ChoicesDropdownFilter),
        ('severity', ChoicesDropdownFilter),
        'is_resolved',
        ('timestamp', RangeDateFilter),
        'user',
    )
    
    search_fields = ('description', 'username', 'ip_address', 'url')
    
    readonly_fields = (
        'event_type', 'severity', 'description', 'user', 'username',
        'ip_address', 'user_agent', 'url', 'method', 'extra_data', 'timestamp'
    )
    
    fieldsets = (
        (_('基本信息'), {
            'fields': ('event_type', 'severity', 'description', 'timestamp')
        }),
        (_('用户信息'), {
            'fields': ('user', 'username'),
            'classes': ('collapse',)
        }),
        (_('请求信息'), {
            'fields': ('ip_address', 'user_agent', 'url', 'method'),
            'classes': ('collapse',)
        }),
        (_('处理状态'), {
            'fields': ('is_resolved', 'resolved_by', 'resolved_at', 'resolution_notes')
        }),
        (_('额外数据'), {
            'fields': ('extra_data',),
            'classes': ('collapse',)
        }),
    )
    
    actions = ['mark_resolved', 'mark_unresolved']
    
    def description_short(self, obj):
        """显示简短描述"""
        return obj.description[:50] + '...' if len(obj.description) > 50 else obj.description
    description_short.short_description = _('描述')
    
    def mark_resolved(self, request, queryset):
        """标记为已处理"""
        from django.utils import timezone
        
        updated = 0
        for log in queryset.filter(is_resolved=False):
            log.is_resolved = True
            log.resolved_by = request.user
            log.resolved_at = timezone.now()
            log.save()
            updated += 1
        
        self.message_user(request, f'成功标记 {updated} 条安全日志为已处理')
    mark_resolved.short_description = _('标记为已处理')
    
    def mark_unresolved(self, request, queryset):
        """标记为未处理"""
        updated = queryset.filter(is_resolved=True).update(
            is_resolved=False,
            resolved_by=None,
            resolved_at=None,
            resolution_notes=''
        )
        
        self.message_user(request, f'成功标记 {updated} 条安全日志为未处理')
    mark_unresolved.short_description = _('标记为未处理')
    
    def has_add_permission(self, request):
        return False
