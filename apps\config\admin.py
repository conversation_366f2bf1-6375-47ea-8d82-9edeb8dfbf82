"""
系统配置管理后台配置
"""
from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from django.utils.html import format_html
from django.forms import widgets
from unfold.admin import ModelAdmin
from unfold.contrib.filters.admin import RangeDateFilter, ChoicesDropdownFilter
from .models import SystemConfig, ConfigGroup, ConfigHistory


class ConfigHistoryInline(admin.TabularInline):
    """配置历史内联"""
    model = ConfigHistory
    extra = 0
    readonly_fields = ('old_value', 'new_value', 'change_reason', 'changed_by', 'created_at')
    
    def has_add_permission(self, request, obj=None):
        return False


@admin.register(SystemConfig)
class SystemConfigAdmin(ModelAdmin):
    """系统配置管理"""
    
    list_display = (
        'name', 'key', 'config_type', 'value_display', 'value_type',
        'is_active', 'is_sensitive', 'updated_at'
    )
    
    list_filter = (
        ('config_type', ChoicesDropdownFilter),
        ('value_type', ChoicesDropdownFilter),
        'is_active', 'is_sensitive', 'is_readonly',
        ('updated_at', RangeDateFilter),
    )
    
    search_fields = ('name', 'key', 'description')
    
    readonly_fields = ('created_at', 'updated_at')
    
    fieldsets = (
        (_('基本信息'), {
            'fields': ('key', 'name', 'description', 'config_type')
        }),
        (_('配置值'), {
            'fields': ('value', 'value_type', 'default_value')
        }),
        (_('验证和约束'), {
            'fields': ('validation_rule',),
            'classes': ('collapse',)
        }),
        (_('设置'), {
            'fields': ('is_active', 'is_sensitive', 'is_readonly', 'sort_order')
        }),
        (_('时间信息'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    inlines = [ConfigHistoryInline]
    
    ordering = ('config_type', 'sort_order', 'key')
    
    actions = ['activate_configs', 'deactivate_configs', 'refresh_cache']
    
    def value_display(self, obj):
        """显示配置值"""
        if obj.is_sensitive:
            return '***敏感信息***'
        
        value = obj.value
        if len(value) > 50:
            return value[:50] + '...'
        return value
    value_display.short_description = _('配置值')
    
    def get_form(self, request, obj=None, **kwargs):
        """自定义表单"""
        form = super().get_form(request, obj, **kwargs)
        
        # 根据值类型调整表单控件
        if obj:
            if obj.value_type == 'text':
                form.base_fields['value'].widget = widgets.Textarea(attrs={'rows': 4})
            elif obj.value_type == 'json':
                form.base_fields['value'].widget = widgets.Textarea(attrs={'rows': 6})
            elif obj.value_type == 'boolean':
                form.base_fields['value'].widget = widgets.Select(choices=[
                    ('true', 'True'),
                    ('false', 'False')
                ])
        
        return form
    
    def activate_configs(self, request, queryset):
        """批量启用配置"""
        updated = queryset.update(is_active=True)
        self.message_user(request, f'成功启用 {updated} 个配置项')
    activate_configs.short_description = _('批量启用配置')
    
    def deactivate_configs(self, request, queryset):
        """批量停用配置"""
        updated = queryset.update(is_active=False)
        self.message_user(request, f'成功停用 {updated} 个配置项')
    deactivate_configs.short_description = _('批量停用配置')
    
    def refresh_cache(self, request, queryset):
        """刷新缓存"""
        from .models import config_manager
        config_manager.refresh_cache()
        self.message_user(request, '配置缓存已刷新')
    refresh_cache.short_description = _('刷新配置缓存')
    
    def save_model(self, request, obj, form, change):
        """保存模型时记录历史"""
        if change:
            # 记录变更历史
            old_obj = SystemConfig.objects.get(pk=obj.pk)
            if old_obj.value != obj.value:
                ConfigHistory.objects.create(
                    config=obj,
                    old_value=old_obj.value,
                    new_value=obj.value,
                    change_reason='管理员修改',
                    changed_by=request.user
                )
        
        super().save_model(request, obj, form, change)


@admin.register(ConfigGroup)
class ConfigGroupAdmin(ModelAdmin):
    """配置分组管理"""
    
    list_display = ('name', 'icon', 'config_count', 'is_active', 'sort_order', 'updated_at')
    
    list_filter = ('is_active', ('updated_at', RangeDateFilter))
    
    search_fields = ('name', 'description')
    
    readonly_fields = ('created_at', 'updated_at', 'config_count')
    
    fieldsets = (
        (_('基本信息'), {
            'fields': ('name', 'description', 'icon')
        }),
        (_('配置项'), {
            'fields': ('configs',)
        }),
        (_('设置'), {
            'fields': ('is_active', 'sort_order')
        }),
        (_('统计信息'), {
            'fields': ('config_count', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    filter_horizontal = ('configs',)
    
    ordering = ('sort_order', 'name')
    
    def config_count(self, obj):
        """配置项数量"""
        return obj.configs.count()
    config_count.short_description = _('配置项数量')


@admin.register(ConfigHistory)
class ConfigHistoryAdmin(ModelAdmin):
    """配置变更历史管理"""
    
    list_display = (
        'config', 'old_value_display', 'new_value_display',
        'change_reason', 'changed_by', 'created_at'
    )
    
    list_filter = (
        'config__config_type',
        ('created_at', RangeDateFilter),
    )
    
    search_fields = ('config__name', 'config__key', 'change_reason')
    
    readonly_fields = ('config', 'old_value', 'new_value', 'change_reason', 'changed_by', 'created_at')
    
    ordering = ('-created_at',)
    
    def old_value_display(self, obj):
        """显示原值"""
        if obj.config.is_sensitive:
            return '***敏感信息***'
        
        value = obj.old_value
        if len(value) > 30:
            return value[:30] + '...'
        return value
    old_value_display.short_description = _('原值')
    
    def new_value_display(self, obj):
        """显示新值"""
        if obj.config.is_sensitive:
            return '***敏感信息***'
        
        value = obj.new_value
        if len(value) > 30:
            return value[:30] + '...'
        return value
    new_value_display.short_description = _('新值')
    
    def has_add_permission(self, request):
        return False
    
    def has_change_permission(self, request, obj=None):
        return False
