from rest_framework import serializers
from .models import DigitalWork, WorkTemplate


class DigitalWorkSerializer(serializers.ModelSerializer):
    """数字人作品序列化器"""
    user_name = serializers.CharField(source='user.username', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    
    class Meta:
        model = DigitalWork
        fields = [
            'id', 'name', 'description', 'thumbnail', 'video_url', 'duration',
            'status', 'status_display', 'user', 'user_name', 'avatar_name', 
            'voice_name', 'script_content', 'computing_power_cost', 
            'generation_time', 'file_size', 'resolution', 'frame_rate',
            'created_at', 'updated_at', 'completed_at', 'is_completed',
            'is_processing', 'can_download'
        ]
        read_only_fields = ['user', 'created_at', 'updated_at']


class DigitalWorkCreateSerializer(serializers.ModelSerializer):
    """创建数字人作品序列化器"""
    
    class Meta:
        model = DigitalWork
        fields = [
            'name', 'description', 'avatar_name', 'voice_name', 
            'script_content', 'resolution', 'frame_rate'
        ]
    
    def create(self, validated_data):
        validated_data['user'] = self.context['request'].user
        validated_data['status'] = 'pending'
        return super().create(validated_data)


class WorkTemplateSerializer(serializers.ModelSerializer):
    """作品模板序列化器"""
    
    class Meta:
        model = WorkTemplate
        fields = [
            'id', 'name', 'description', 'thumbnail', 'preview_video',
            'default_avatar', 'default_voice', 'default_script',
            'category', 'tags', 'is_active', 'is_premium', 'sort_order',
            'usage_count', 'created_at', 'updated_at'
        ]


class WorkStatsSerializer(serializers.Serializer):
    """作品统计序列化器"""
    total_works = serializers.IntegerField()
    completed_works = serializers.IntegerField()
    processing_works = serializers.IntegerField()
    failed_works = serializers.IntegerField()
    total_duration = serializers.CharField()
    total_computing_power = serializers.IntegerField()
    avg_generation_time = serializers.FloatField()
