"""
通知系统数据模型
"""
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.contrib.auth import get_user_model
import json

User = get_user_model()


class Notification(models.Model):
    """通知模型"""
    
    TYPE_CHOICES = [
        ('system', _('系统通知')),
        ('video_progress', _('视频生成进度')),
        ('video_complete', _('视频生成完成')),
        ('voice_complete', _('语音合成完成')),
        ('copywriting_complete', _('文案生成完成')),
        ('package_expire', _('套餐即将过期')),
        ('computing_low', _('算力不足')),
        ('error', _('错误通知')),
    ]
    
    PRIORITY_CHOICES = [
        ('low', _('低')),
        ('normal', _('普通')),
        ('high', _('高')),
        ('urgent', _('紧急')),
    ]
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name=_('用户'))
    type = models.CharField(_('通知类型'), max_length=20, choices=TYPE_CHOICES)
    title = models.CharField(_('标题'), max_length=200)
    message = models.TextField(_('消息内容'))
    priority = models.CharField(_('优先级'), max_length=10, choices=PRIORITY_CHOICES, default='normal')
    
    # 额外数据（JSON格式）
    extra_data = models.TextField(_('额外数据'), blank=True, help_text='JSON格式的额外数据')
    
    # 状态
    is_read = models.BooleanField(_('已读'), default=False)
    is_sent = models.BooleanField(_('已发送'), default=False)
    
    # 时间
    created_at = models.DateTimeField(_('创建时间'), auto_now_add=True)
    read_at = models.DateTimeField(_('阅读时间'), null=True, blank=True)
    
    class Meta:
        verbose_name = _('通知')
        verbose_name_plural = _('通知')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'is_read']),
            models.Index(fields=['type', 'created_at']),
        ]
    
    def __str__(self):
        return f'{self.user.username}: {self.title}'
    
    def get_extra_data_dict(self):
        """获取额外数据的字典格式"""
        if self.extra_data:
            try:
                return json.loads(self.extra_data)
            except json.JSONDecodeError:
                return {}
        return {}
    
    def set_extra_data_dict(self, data):
        """设置额外数据"""
        self.extra_data = json.dumps(data, ensure_ascii=False)
    
    def mark_as_read(self):
        """标记为已读"""
        from django.utils import timezone
        self.is_read = True
        self.read_at = timezone.now()
        self.save(update_fields=['is_read', 'read_at'])


class NotificationTemplate(models.Model):
    """通知模板"""
    
    type = models.CharField(_('通知类型'), max_length=20, unique=True)
    title_template = models.CharField(_('标题模板'), max_length=200)
    message_template = models.TextField(_('消息模板'))
    is_active = models.BooleanField(_('启用'), default=True)
    
    created_at = models.DateTimeField(_('创建时间'), auto_now_add=True)
    updated_at = models.DateTimeField(_('更新时间'), auto_now=True)
    
    class Meta:
        verbose_name = _('通知模板')
        verbose_name_plural = _('通知模板')
    
    def __str__(self):
        return f'{self.type}: {self.title_template}'
    
    def render(self, context=None):
        """渲染模板"""
        if context is None:
            context = {}
        
        title = self.title_template.format(**context)
        message = self.message_template.format(**context)
        
        return title, message


class UserNotificationSettings(models.Model):
    """用户通知设置"""
    
    user = models.OneToOneField(User, on_delete=models.CASCADE, verbose_name=_('用户'))
    
    # 通知开关
    email_notifications = models.BooleanField(_('邮件通知'), default=True)
    browser_notifications = models.BooleanField(_('浏览器通知'), default=True)
    
    # 具体通知类型开关
    video_progress_notifications = models.BooleanField(_('视频进度通知'), default=True)
    system_notifications = models.BooleanField(_('系统通知'), default=True)
    package_notifications = models.BooleanField(_('套餐通知'), default=True)
    
    created_at = models.DateTimeField(_('创建时间'), auto_now_add=True)
    updated_at = models.DateTimeField(_('更新时间'), auto_now=True)
    
    class Meta:
        verbose_name = _('用户通知设置')
        verbose_name_plural = _('用户通知设置')
    
    def __str__(self):
        return f'{self.user.username} 的通知设置'
