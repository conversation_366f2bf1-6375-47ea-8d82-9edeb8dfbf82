"""
数字人形象相关视图
"""
from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import SearchFilter, OrderingFilter
from apps.common.cache import cache_response, cache_result, invalidate_cache, cache_manager
from django.db import models
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator

from .models import DigitalAvatar, AvatarCategory, AvatarTag, AvatarUsageLog
from .serializers import (
    DigitalAvatarSerializer, AvatarCategorySerializer,
    AvatarTagSerializer, AvatarUsageLogSerializer
)
from apps.tasks.models import TaskQueue


@method_decorator(csrf_exempt, name='dispatch')
class DigitalAvatarViewSet(viewsets.ModelViewSet):
    """数字人形象视图集"""
    queryset = DigitalAvatar.objects.all()
    serializer_class = DigitalAvatarSerializer
    permission_classes = [permissions.AllowAny]
    authentication_classes = []  # 完全禁用认证
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['model_version', 'scene_type', 'is_public', 'is_active']
    search_fields = ['name', 'description']
    ordering_fields = ['sort_order', 'usage_count', 'created_at']
    ordering = ['sort_order', '-created_at']
    
    def get_queryset(self):
        """根据用户权限过滤数字人形象"""
        queryset = self.queryset.filter(is_active=True)

        # 检查用户是否已认证
        if not self.request.user.is_authenticated:
            # 匿名用户只能看到公共形象
            queryset = queryset.filter(is_public=True)
        elif not self.request.user.is_staff:
            # 普通用户只能看到公共形象和自己的私有形象
            queryset = queryset.filter(
                models.Q(is_public=True) |
                models.Q(owner=self.request.user)
            )

        return queryset
    
    @action(detail=True, methods=['post'])
    def use_avatar(self, request, pk=None):
        """使用数字人形象"""
        avatar = self.get_object()
        user = request.user
        
        # 检查算力是否足够
        if user.computing_power < avatar.computing_power_cost:
            return Response(
                {'error': '算力不足，请先充值'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # 消耗算力
        try:
            user.consume_computing_power(
                avatar.computing_power_cost,
                f'使用数字人形象: {avatar.name}'
            )
            
            # 记录使用日志
            AvatarUsageLog.objects.create(
                avatar=avatar,
                user=user,
                computing_power_consumed=avatar.computing_power_cost,
                purpose=request.data.get('purpose', '')
            )
            
            # 增加使用次数
            avatar.increment_usage()
            
            return Response({
                'message': f'成功使用数字人形象 {avatar.name}',
                'remaining_power': user.computing_power
            })
            
        except ValueError as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
    
    @action(detail=False, methods=['get'], permission_classes=[permissions.AllowAny])
    def public_avatars(self, request):
        """获取公共数字人形象"""
        try:
            # 直接查询所有公共形象，不使用get_queryset()
            avatars = DigitalAvatar.objects.filter(is_public=True, is_active=True)
            page = self.paginate_queryset(avatars)
            if page is not None:
                serializer = self.get_serializer(page, many=True)
                return self.get_paginated_response(serializer.data)

            serializer = self.get_serializer(avatars, many=True)
            return Response(serializer.data)
        except Exception as e:
            return Response(
                {'error': f'获取公共数字人形象失败: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=False, methods=['get'])
    def my_avatars(self, request):
        """获取我的数字人形象"""
        avatars = self.get_queryset().filter(owner=request.user)
        page = self.paginate_queryset(avatars)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(avatars, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def toggle_favorite(self, request, pk=None):
        """收藏/取消收藏数字人形象"""
        avatar = self.get_object()
        user = request.user

        # 这里需要实现收藏功能，暂时返回模拟结果
        # 实际应该有一个UserFavoriteAvatar模型
        return Response({
            'message': '收藏状态已切换',
            'is_favorited': True  # 模拟结果
        })

    @action(detail=False, methods=['get'])
    def favorites(self, request):
        """获取收藏的数字人形象"""
        # 这里需要实现收藏功能，暂时返回空列表
        # 实际应该查询UserFavoriteAvatar模型
        return Response([])

    @action(detail=False, methods=['get'])
    def search(self, request):
        """搜索数字人形象"""
        query = request.query_params.get('q', '')
        if not query:
            return Response([])

        avatars = self.get_queryset().filter(
            models.Q(name__icontains=query) |
            models.Q(description__icontains=query)
        )

        page = self.paginate_queryset(avatars)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(avatars, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'], permission_classes=[permissions.AllowAny])
    @cache_response('avatar', timeout=1800)  # 缓存30分钟
    def recommended(self, request):
        """获取推荐数字人形象"""
        try:
            # 获取推荐的数字人形象（按使用次数和排序权重）
            avatars = DigitalAvatar.objects.filter(
                is_public=True,
                is_active=True
            ).order_by('-usage_count', '-sort_order')[:12]

            page = self.paginate_queryset(avatars)
            if page is not None:
                serializer = self.get_serializer(page, many=True)
                return self.get_paginated_response(serializer.data)

            serializer = self.get_serializer(avatars, many=True)
            return Response(serializer.data)
        except Exception as e:
            return Response(
                {'error': f'获取推荐数字人形象失败: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['get'], permission_classes=[permissions.AllowAny])
    @cache_response('avatar', timeout=1800)  # 缓存30分钟
    def popular(self, request):
        """获取热门数字人形象"""
        try:
            # 获取热门数字人形象（按使用次数排序）
            avatars = DigitalAvatar.objects.filter(
                is_public=True,
                is_active=True,
                usage_count__gt=0
            ).order_by('-usage_count')[:20]

            page = self.paginate_queryset(avatars)
            if page is not None:
                serializer = self.get_serializer(page, many=True)
                return self.get_paginated_response(serializer.data)

            serializer = self.get_serializer(avatars, many=True)
            return Response(serializer.data)
        except Exception as e:
            return Response(
                {'error': f'获取热门数字人形象失败: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['post'])
    def upload(self, request):
        """上传自定义数字人形象"""
        # 这里需要实现文件上传逻辑
        return Response({
            'message': '上传功能开发中',
            'status': 'pending'
        })

    @action(detail=False, methods=['post'])
    def clone(self, request):
        """提交形象克隆任务"""
        try:
            name = request.data.get('name')
            video_file_id = request.data.get('video_file_id')
            description = request.data.get('description', '')

            if not name:
                return Response({
                    'success': False,
                    'message': '请输入形象名称'
                }, status=status.HTTP_400_BAD_REQUEST)

            if not video_file_id:
                return Response({
                    'success': False,
                    'message': '请上传视频文件'
                }, status=status.HTTP_400_BAD_REQUEST)

            # 创建任务记录
            task = TaskQueue.objects.create(
                name=f'形象克隆: {name}',
                task_type='avatar_clone',
                task_function='clone_avatar',
                created_by=request.user,
                kwargs={
                    'name': name,
                    'video_file_id': video_file_id,
                    'description': description,
                    'user_id': request.user.id
                }
            )

            # 启动异步任务
            from apps.tasks.tasks import clone_avatar_task
            clone_avatar_task.delay(
                task_id=task.id,
                name=name,
                video_file_id=video_file_id,
                description=description,
                user_id=request.user.id
            )

            return Response({
                'success': True,
                'message': '形象克隆任务已提交，预计需要5-10分钟完成',
                'task_id': task.id,
                'estimated_time': '5-10分钟'
            })

        except Exception as e:
            return Response({
                'success': False,
                'message': f'提交任务失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['get'], url_path='clone/(?P<task_id>[^/.]+)/status')
    def clone_status(self, request, task_id=None):
        """获取克隆任务状态"""
        try:
            task = TaskQueue.objects.get(id=task_id, created_by=request.user)

            return Response({
                'success': True,
                'task_id': task.id,
                'status': task.status,
                'progress': task.progress,
                'progress_message': task.progress_message,
                'created_at': task.created_at,
                'updated_at': task.updated_at,
                'result': task.result,
                'error_message': task.error_message
            })

        except TaskQueue.DoesNotExist:
            return Response({
                'success': False,
                'message': '任务不存在'
            }, status=status.HTTP_404_NOT_FOUND)

    @action(detail=False, methods=['get'], url_path='clone/my_tasks')
    def my_clone_tasks(self, request):
        """获取我的克隆任务列表"""
        tasks = TaskQueue.objects.filter(
            created_by=request.user,
            task_type='avatar_clone'
        ).order_by('-created_at')

        page = self.paginate_queryset(tasks)
        if page is not None:
            task_data = []
            for task in page:
                task_data.append({
                    'id': task.id,
                    'name': task.name,
                    'status': task.status,
                    'progress': task.progress,
                    'progress_message': task.progress_message,
                    'created_at': task.created_at,
                    'updated_at': task.updated_at,
                    'result': task.result,
                    'error_message': task.error_message
                })
            return self.get_paginated_response(task_data)

        task_data = []
        for task in tasks:
            task_data.append({
                'id': task.id,
                'name': task.name,
                'status': task.status,
                'progress': task.progress,
                'progress_message': task.progress_message,
                'created_at': task.created_at,
                'updated_at': task.updated_at,
                'result': task.result,
                'error_message': task.error_message
            })

        return Response({
            'success': True,
            'tasks': task_data
        })

    @action(detail=False, methods=['post'], url_path='clone/(?P<task_id>[^/.]+)/cancel')
    def cancel_clone_task(self, request, task_id=None):
        """取消克隆任务"""
        try:
            task = TaskQueue.objects.get(id=task_id, created_by=request.user)

            if task.status in ['completed', 'failed', 'cancelled']:
                return Response({
                    'success': False,
                    'message': '任务已完成，无法取消'
                }, status=status.HTTP_400_BAD_REQUEST)

            task.status = 'cancelled'
            task.save()

            return Response({
                'success': True,
                'message': '任务已取消'
            })

        except TaskQueue.DoesNotExist:
            return Response({
                'success': False,
                'message': '任务不存在'
            }, status=status.HTTP_404_NOT_FOUND)


class AvatarCategoryViewSet(viewsets.ReadOnlyModelViewSet):
    """数字人形象分类视图集"""
    queryset = AvatarCategory.objects.filter(is_active=True)
    serializer_class = AvatarCategorySerializer
    permission_classes = [permissions.AllowAny]
    ordering = ['sort_order', 'name']


class AvatarTagViewSet(viewsets.ReadOnlyModelViewSet):
    """数字人形象标签视图集"""
    queryset = AvatarTag.objects.all()
    serializer_class = AvatarTagSerializer
    permission_classes = [permissions.AllowAny]
    ordering = ['-usage_count', 'name']


class AvatarUsageLogViewSet(viewsets.ReadOnlyModelViewSet):
    """数字人形象使用日志视图集"""
    queryset = AvatarUsageLog.objects.all()
    serializer_class = AvatarUsageLogSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['avatar', 'user']
    search_fields = ['avatar__name', 'user__username', 'purpose']
    ordering_fields = ['created_at']
    ordering = ['-created_at']
    
    def get_queryset(self):
        """普通用户只能查看自己的使用日志"""
        if self.request.user.is_staff:
            return self.queryset
        return self.queryset.filter(user=self.request.user)
