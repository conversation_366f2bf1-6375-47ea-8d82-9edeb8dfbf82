"""
视频生成模型
基于对https://hm.umi6.com/平台的逆向工程分析
"""
from django.db import models
from django.conf import settings
from django.utils.translation import gettext_lazy as _
from django.utils import timezone


class VideoProduction(models.Model):
    """视频作品"""
    
    DRIVE_MODE_CHOICES = [
        ('text', '文本驱动'),
        ('audio', '语音驱动'),
    ]
    
    STATUS_CHOICES = [
        ('draft', '草稿'),
        ('pending', '待生成'),
        ('processing', '生成中'),
        ('completed', '已完成'),
        ('failed', '生成失败'),
        ('cancelled', '已取消'),
    ]
    
    QUALITY_CHOICES = [
        ('720p', '720P'),
        ('1080p', '1080P'),
        ('4k', '4K'),
    ]
    
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='video_productions',
        verbose_name=_('用户')
    )
    
    title = models.CharField(
        _('视频标题'),
        max_length=200,
        help_text=_('视频作品的标题')
    )
    
    description = models.TextField(
        _('视频描述'),
        blank=True,
        help_text=_('视频作品的描述')
    )
    
    avatar = models.ForeignKey(
        'avatars.DigitalAvatar',
        on_delete=models.CASCADE,
        related_name='video_productions',
        verbose_name=_('数字人形象'),
        help_text=_('使用的数字人形象')
    )
    
    voice_model = models.ForeignKey(
        'voices.VoiceModel',
        on_delete=models.CASCADE,
        related_name='video_productions',
        verbose_name=_('语音模型'),
        help_text=_('使用的语音模型')
    )
    
    drive_mode = models.CharField(
        _('驱动模式'),
        max_length=10,
        choices=DRIVE_MODE_CHOICES,
        default='text',
        help_text=_('视频生成的驱动模式')
    )
    
    # 文本驱动相关字段
    text_content = models.TextField(
        _('文本内容'),
        blank=True,
        help_text=_('文本驱动模式下的文本内容')
    )
    
    # 语音驱动相关字段
    audio_file = models.FileField(
        _('音频文件'),
        upload_to='videos/audio_sources/',
        null=True,
        blank=True,
        help_text=_('语音驱动模式下的音频文件')
    )
    
    audio_url = models.URLField(
        _('音频链接'),
        null=True,
        blank=True,
        help_text=_('语音驱动模式下的音频链接')
    )
    
    # 语音参数（文本驱动模式使用）
    voice_volume = models.FloatField(
        _('音量'),
        default=50.0,
        help_text=_('语音音量（0-100）')
    )
    
    voice_pitch = models.FloatField(
        _('音调'),
        default=1.0,
        help_text=_('语音音调（0-2）')
    )
    
    voice_speed = models.FloatField(
        _('语速'),
        default=1.0,
        help_text=_('语音语速（0-2）')
    )
    
    # 视频设置
    video_quality = models.CharField(
        _('视频质量'),
        max_length=10,
        choices=QUALITY_CHOICES,
        default='1080p',
        help_text=_('视频输出质量')
    )
    
    background_music = models.FileField(
        _('背景音乐'),
        upload_to='videos/background_music/',
        null=True,
        blank=True,
        help_text=_('视频背景音乐文件')
    )
    
    background_music_volume = models.FloatField(
        _('背景音乐音量'),
        default=20.0,
        help_text=_('背景音乐音量（0-100）')
    )
    
    # 生成状态和结果
    status = models.CharField(
        _('生成状态'),
        max_length=20,
        choices=STATUS_CHOICES,
        default='draft',
        help_text=_('视频生成状态')
    )
    
    progress = models.PositiveIntegerField(
        _('生成进度'),
        default=0,
        help_text=_('视频生成进度百分比（0-100）')
    )
    
    error_message = models.TextField(
        _('错误信息'),
        blank=True,
        help_text=_('生成失败时的错误信息')
    )
    
    # 生成结果
    result_video = models.FileField(
        _('生成视频'),
        upload_to='videos/results/',
        null=True,
        blank=True,
        help_text=_('生成的视频文件')
    )
    
    result_video_url = models.URLField(
        _('视频链接'),
        null=True,
        blank=True,
        help_text=_('生成视频的在线链接')
    )
    
    video_duration = models.PositiveIntegerField(
        _('视频时长'),
        null=True,
        blank=True,
        help_text=_('生成视频的时长（秒）')
    )
    
    video_size = models.PositiveIntegerField(
        _('视频大小'),
        null=True,
        blank=True,
        help_text=_('生成视频的文件大小（字节）')
    )
    
    thumbnail = models.ImageField(
        _('视频缩略图'),
        upload_to='videos/thumbnails/',
        null=True,
        blank=True,
        help_text=_('视频的缩略图')
    )
    
    # 算力消耗
    computing_power_consumed = models.PositiveIntegerField(
        _('消耗算力'),
        default=0,
        help_text=_('本次生成消耗的算力点数')
    )
    
    # 统计信息
    view_count = models.PositiveIntegerField(
        _('观看次数'),
        default=0,
        help_text=_('视频被观看的次数')
    )
    
    download_count = models.PositiveIntegerField(
        _('下载次数'),
        default=0,
        help_text=_('视频被下载的次数')
    )
    
    is_public = models.BooleanField(
        _('是否公开'),
        default=False,
        help_text=_('是否公开展示此视频')
    )
    
    is_favorite = models.BooleanField(
        _('是否收藏'),
        default=False,
        help_text=_('用户是否收藏此视频')
    )
    
    # 时间字段
    created_at = models.DateTimeField(
        _('创建时间'),
        auto_now_add=True
    )
    
    updated_at = models.DateTimeField(
        _('更新时间'),
        auto_now=True
    )
    
    started_at = models.DateTimeField(
        _('开始生成时间'),
        null=True,
        blank=True,
        help_text=_('开始生成视频的时间')
    )
    
    completed_at = models.DateTimeField(
        _('完成时间'),
        null=True,
        blank=True,
        help_text=_('视频生成完成的时间')
    )

    class Meta:
        verbose_name = _('视频作品')
        verbose_name_plural = _('视频作品')
        db_table = 'video_productions'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'status']),
            models.Index(fields=['avatar', 'created_at']),
            models.Index(fields=['voice_model', 'created_at']),
            models.Index(fields=['status', 'created_at']),
            models.Index(fields=['is_public', 'created_at']),
        ]

    def __str__(self):
        return f"{self.user.display_name} - {self.title}"

    def increment_view(self):
        """增加观看次数"""
        self.view_count += 1
        self.save(update_fields=['view_count'])

    def increment_download(self):
        """增加下载次数"""
        self.download_count += 1
        self.save(update_fields=['download_count'])

    @property
    def estimated_duration(self):
        """估算视频时长"""
        if self.drive_mode == 'text' and self.text_content:
            # 按照平均语速估算：每分钟约150-200字
            char_count = len(self.text_content)
            return max(char_count / 3, 10)  # 最少10秒
        return None

    @property
    def video_thumbnail_url(self):
        """获取视频缩略图URL"""
        if self.thumbnail:
            return self.thumbnail.url
        elif self.result_video_url and 'aliyuncs.com' in self.result_video_url:
            # 阿里云OSS视频缩略图处理
            return f"{self.result_video_url}?x-oss-process=video/snapshot,t_0,m_fast,ar_auto"
        return None


class VideoTemplate(models.Model):
    """视频模板"""
    
    name = models.CharField(
        _('模板名称'),
        max_length=100,
        help_text=_('视频模板的名称')
    )
    
    description = models.TextField(
        _('模板描述'),
        blank=True,
        help_text=_('模板的详细描述')
    )
    
    category = models.CharField(
        _('模板分类'),
        max_length=50,
        choices=[
            ('business', '商务'),
            ('education', '教育'),
            ('entertainment', '娱乐'),
            ('news', '新闻'),
            ('marketing', '营销'),
            ('personal', '个人'),
        ],
        help_text=_('模板的分类')
    )
    
    default_avatar = models.ForeignKey(
        'avatars.DigitalAvatar',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='default_templates',
        verbose_name=_('默认数字人'),
        help_text=_('模板的默认数字人形象')
    )
    
    default_voice = models.ForeignKey(
        'voices.VoiceModel',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='default_templates',
        verbose_name=_('默认语音'),
        help_text=_('模板的默认语音模型')
    )
    
    template_content = models.TextField(
        _('模板内容'),
        blank=True,
        help_text=_('模板的默认文本内容')
    )
    
    template_settings = models.JSONField(
        _('模板设置'),
        default=dict,
        blank=True,
        help_text=_('模板的默认设置参数')
    )
    
    thumbnail = models.ImageField(
        _('模板缩略图'),
        upload_to='videos/template_thumbnails/',
        null=True,
        blank=True,
        help_text=_('模板的预览图')
    )
    
    is_active = models.BooleanField(
        _('是否启用'),
        default=True,
        help_text=_('是否启用此模板')
    )
    
    usage_count = models.PositiveIntegerField(
        _('使用次数'),
        default=0,
        help_text=_('模板被使用的次数')
    )
    
    sort_order = models.PositiveIntegerField(
        _('排序'),
        default=0,
        help_text=_('显示排序，数字越小越靠前')
    )
    
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='created_video_templates',
        verbose_name=_('创建者')
    )
    
    created_at = models.DateTimeField(
        _('创建时间'),
        auto_now_add=True
    )
    
    updated_at = models.DateTimeField(
        _('更新时间'),
        auto_now=True
    )

    class Meta:
        verbose_name = _('视频模板')
        verbose_name_plural = _('视频模板')
        db_table = 'video_templates'
        ordering = ['sort_order', '-usage_count']

    def __str__(self):
        return f"{self.name} ({self.get_category_display()})"

    def increment_usage(self):
        """增加使用次数"""
        self.usage_count += 1
        self.save(update_fields=['usage_count'])


class VideoShare(models.Model):
    """视频分享"""
    
    video = models.ForeignKey(
        VideoProduction,
        on_delete=models.CASCADE,
        related_name='shares',
        verbose_name=_('视频')
    )
    
    share_token = models.CharField(
        _('分享令牌'),
        max_length=64,
        unique=True,
        help_text=_('用于分享的唯一令牌')
    )
    
    password = models.CharField(
        _('访问密码'),
        max_length=20,
        blank=True,
        help_text=_('访问分享链接的密码')
    )
    
    expire_at = models.DateTimeField(
        _('过期时间'),
        null=True,
        blank=True,
        help_text=_('分享链接的过期时间')
    )
    
    view_count = models.PositiveIntegerField(
        _('访问次数'),
        default=0,
        help_text=_('分享链接被访问的次数')
    )
    
    is_active = models.BooleanField(
        _('是否有效'),
        default=True,
        help_text=_('分享链接是否有效')
    )
    
    created_at = models.DateTimeField(
        _('创建时间'),
        auto_now_add=True
    )

    class Meta:
        verbose_name = _('视频分享')
        verbose_name_plural = _('视频分享')
        db_table = 'video_shares'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.video.title} - 分享"

    def increment_view(self):
        """增加访问次数"""
        self.view_count += 1
        self.save(update_fields=['view_count'])


class RenderingTask(models.Model):
    """视频渲染任务"""

    STATUS_CHOICES = [
        ('pending', '待处理'),
        ('processing', '处理中'),
        ('completed', '已完成'),
        ('failed', '失败'),
        ('cancelled', '已取消'),
    ]

    PRIORITY_CHOICES = [
        ('low', '低'),
        ('normal', '普通'),
        ('high', '高'),
        ('urgent', '紧急'),
    ]

    video = models.ForeignKey(
        VideoProduction,
        on_delete=models.CASCADE,
        related_name='rendering_tasks',
        verbose_name=_('关联视频'),
        help_text=_('需要渲染的视频')
    )

    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='rendering_tasks',
        verbose_name=_('用户'),
        help_text=_('提交渲染任务的用户')
    )

    status = models.CharField(
        _('状态'),
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending',
        help_text=_('渲染任务的当前状态')
    )

    priority = models.CharField(
        _('优先级'),
        max_length=10,
        choices=PRIORITY_CHOICES,
        default='normal',
        help_text=_('渲染任务的优先级')
    )

    progress = models.PositiveIntegerField(
        _('进度'),
        default=0,
        help_text=_('渲染进度百分比（0-100）')
    )

    estimated_duration = models.PositiveIntegerField(
        _('预计时长'),
        null=True,
        blank=True,
        help_text=_('预计渲染时长（秒）')
    )

    actual_duration = models.PositiveIntegerField(
        _('实际时长'),
        null=True,
        blank=True,
        help_text=_('实际渲染时长（秒）')
    )

    error_message = models.TextField(
        _('错误信息'),
        blank=True,
        help_text=_('渲染失败时的错误信息')
    )

    computing_power_consumed = models.PositiveIntegerField(
        _('消耗算力'),
        default=0,
        help_text=_('此次渲染消耗的算力点数')
    )

    started_at = models.DateTimeField(
        _('开始时间'),
        null=True,
        blank=True,
        help_text=_('渲染开始时间')
    )

    completed_at = models.DateTimeField(
        _('完成时间'),
        null=True,
        blank=True,
        help_text=_('渲染完成时间')
    )

    created_at = models.DateTimeField(
        _('创建时间'),
        auto_now_add=True
    )

    updated_at = models.DateTimeField(
        _('更新时间'),
        auto_now=True
    )

    class Meta:
        verbose_name = _('渲染任务')
        verbose_name_plural = _('渲染任务')
        db_table = 'video_rendering_tasks'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['status', 'priority']),
            models.Index(fields=['user', 'status']),
            models.Index(fields=['video']),
        ]

    def __str__(self):
        return f"{self.video.title} - {self.get_status_display()}"

    def update_progress(self, progress):
        """更新渲染进度"""
        self.progress = min(100, max(0, progress))
        if self.progress == 100 and self.status == 'processing':
            self.status = 'completed'
            self.completed_at = timezone.now()
        self.save(update_fields=['progress', 'status', 'completed_at', 'updated_at'])

    def start_rendering(self):
        """开始渲染"""
        self.status = 'processing'
        self.started_at = timezone.now()
        self.save(update_fields=['status', 'started_at', 'updated_at'])

    def mark_failed(self, error_message=''):
        """标记为失败"""
        self.status = 'failed'
        self.error_message = error_message
        self.completed_at = timezone.now()
        self.save(update_fields=['status', 'error_message', 'completed_at', 'updated_at'])

    def cancel(self):
        """取消渲染"""
        if self.status in ['pending', 'processing']:
            self.status = 'cancelled'
            self.completed_at = timezone.now()
            self.save(update_fields=['status', 'completed_at', 'updated_at'])

    @property
    def duration_display(self):
        """获取时长显示"""
        if self.actual_duration:
            return f"{self.actual_duration}秒"
        elif self.estimated_duration:
            return f"预计{self.estimated_duration}秒"
        return "未知"
