"""
初始化第三方API配置管理命令
"""
from django.core.management.base import BaseCommand
from apps.config.models import SystemConfig


class Command(BaseCommand):
    help = '初始化第三方API服务配置'
    
    def handle(self, *args, **options):
        self.stdout.write('开始初始化第三方API配置...')
        
        # 第三方API配置列表
        third_party_configs = [
            # ==================== AI对话服务 ====================
            {
                'key': 'openai_api_key',
                'name': 'OpenAI API密钥',
                'description': 'OpenAI服务的API密钥',
                'config_type': 'ai_service',
                'value': '',
                'value_type': 'string',
                'default_value': '',
                'is_sensitive': True,
                'sort_order': 10,
            },
            {
                'key': 'openai_model',
                'name': 'OpenAI模型',
                'description': '使用的OpenAI模型版本',
                'config_type': 'ai_service',
                'value': 'gpt-4',
                'value_type': 'string',
                'default_value': 'gpt-4',
                'sort_order': 11,
            },
            {
                'key': 'openai_max_tokens',
                'name': 'OpenAI最大Token数',
                'description': '单次请求的最大Token数',
                'config_type': 'ai_service',
                'value': '2000',
                'value_type': 'integer',
                'default_value': '2000',
                'sort_order': 12,
            },
            
            # ==================== 语音服务 ====================
            {
                'key': 'azure_speech_key',
                'name': 'Azure语音服务密钥',
                'description': 'Azure Cognitive Services Speech API密钥',
                'config_type': 'ai_service',
                'value': '',
                'value_type': 'string',
                'default_value': '',
                'is_sensitive': True,
                'sort_order': 20,
            },
            {
                'key': 'azure_speech_region',
                'name': 'Azure语音服务区域',
                'description': 'Azure语音服务的区域设置',
                'config_type': 'ai_service',
                'value': 'eastasia',
                'value_type': 'string',
                'default_value': 'eastasia',
                'sort_order': 21,
            },
            {
                'key': 'elevenlabs_api_key',
                'name': 'ElevenLabs API密钥',
                'description': 'ElevenLabs语音克隆服务API密钥',
                'config_type': 'ai_service',
                'value': '',
                'value_type': 'string',
                'default_value': '',
                'is_sensitive': True,
                'sort_order': 22,
            },
            
            # ==================== 图像生成服务 ====================
            {
                'key': 'dalle_api_key',
                'name': 'DALL-E API密钥',
                'description': 'OpenAI DALL-E图像生成API密钥',
                'config_type': 'ai_service',
                'value': '',
                'value_type': 'string',
                'default_value': '',
                'is_sensitive': True,
                'sort_order': 30,
            },
            {
                'key': 'midjourney_api_key',
                'name': 'Midjourney API密钥',
                'description': 'Midjourney图像生成API密钥',
                'config_type': 'ai_service',
                'value': '',
                'value_type': 'string',
                'default_value': '',
                'is_sensitive': True,
                'sort_order': 31,
            },
            
            # ==================== 视频生成服务 ====================
            {
                'key': 'heygen_api_key',
                'name': 'HeyGen API密钥',
                'description': 'HeyGen数字人视频生成API密钥',
                'config_type': 'ai_service',
                'value': '',
                'value_type': 'string',
                'default_value': '',
                'is_sensitive': True,
                'sort_order': 40,
            },
            {
                'key': 'did_api_key',
                'name': 'D-ID API密钥',
                'description': 'D-ID照片数字人API密钥',
                'config_type': 'ai_service',
                'value': '',
                'value_type': 'string',
                'default_value': '',
                'is_sensitive': True,
                'sort_order': 41,
            },
            {
                'key': 'runwayml_api_key',
                'name': 'RunwayML API密钥',
                'description': 'RunwayML AI视频生成API密钥',
                'config_type': 'ai_service',
                'value': '',
                'value_type': 'string',
                'default_value': '',
                'is_sensitive': True,
                'sort_order': 42,
            },
            
            # ==================== 音乐生成服务 ====================
            {
                'key': 'suno_api_key',
                'name': 'Suno AI API密钥',
                'description': 'Suno AI音乐生成API密钥',
                'config_type': 'ai_service',
                'value': '',
                'value_type': 'string',
                'default_value': '',
                'is_sensitive': True,
                'sort_order': 50,
            },
            
            # ==================== 人脸处理服务 ====================
            {
                'key': 'facepp_api_key',
                'name': 'Face++ API密钥',
                'description': 'Face++人脸识别API密钥',
                'config_type': 'ai_service',
                'value': '',
                'value_type': 'string',
                'default_value': '',
                'is_sensitive': True,
                'sort_order': 60,
            },
            {
                'key': 'facepp_api_secret',
                'name': 'Face++ API密钥',
                'description': 'Face++人脸识别API密钥',
                'config_type': 'ai_service',
                'value': '',
                'value_type': 'string',
                'default_value': '',
                'is_sensitive': True,
                'sort_order': 61,
            },
            
            # ==================== 云存储服务 ====================
            {
                'key': 'aliyun_oss_access_key',
                'name': '阿里云OSS AccessKey',
                'description': '阿里云对象存储AccessKey ID',
                'config_type': 'storage',
                'value': '',
                'value_type': 'string',
                'default_value': '',
                'is_sensitive': True,
                'sort_order': 70,
            },
            {
                'key': 'aliyun_oss_secret_key',
                'name': '阿里云OSS SecretKey',
                'description': '阿里云对象存储AccessKey Secret',
                'config_type': 'storage',
                'value': '',
                'value_type': 'string',
                'default_value': '',
                'is_sensitive': True,
                'sort_order': 71,
            },
            {
                'key': 'aliyun_oss_bucket',
                'name': '阿里云OSS存储桶',
                'description': '阿里云OSS存储桶名称',
                'config_type': 'storage',
                'value': '',
                'value_type': 'string',
                'default_value': '',
                'sort_order': 72,
            },
            {
                'key': 'aliyun_oss_endpoint',
                'name': '阿里云OSS端点',
                'description': '阿里云OSS服务端点',
                'config_type': 'storage',
                'value': '',
                'value_type': 'string',
                'default_value': '',
                'sort_order': 73,
            },
            
            # ==================== 支付服务 ====================
            {
                'key': 'alipay_app_id',
                'name': '支付宝应用ID',
                'description': '支付宝开放平台应用ID',
                'config_type': 'payment',
                'value': '',
                'value_type': 'string',
                'default_value': '',
                'is_sensitive': True,
                'sort_order': 80,
            },
            {
                'key': 'alipay_private_key',
                'name': '支付宝应用私钥',
                'description': '支付宝应用RSA私钥',
                'config_type': 'payment',
                'value': '',
                'value_type': 'text',
                'default_value': '',
                'is_sensitive': True,
                'sort_order': 81,
            },
            {
                'key': 'alipay_public_key',
                'name': '支付宝公钥',
                'description': '支付宝RSA公钥',
                'config_type': 'payment',
                'value': '',
                'value_type': 'text',
                'default_value': '',
                'is_sensitive': True,
                'sort_order': 82,
            },
            {
                'key': 'wechat_pay_mch_id',
                'name': '微信支付商户号',
                'description': '微信支付商户号',
                'config_type': 'payment',
                'value': '',
                'value_type': 'string',
                'default_value': '',
                'is_sensitive': True,
                'sort_order': 83,
            },
            {
                'key': 'wechat_pay_api_key',
                'name': '微信支付API密钥',
                'description': '微信支付API密钥',
                'config_type': 'payment',
                'value': '',
                'value_type': 'string',
                'default_value': '',
                'is_sensitive': True,
                'sort_order': 84,
            },
            
            # ==================== 短信服务 ====================
            {
                'key': 'aliyun_sms_access_key',
                'name': '阿里云短信AccessKey',
                'description': '阿里云短信服务AccessKey ID',
                'config_type': 'notification',
                'value': '',
                'value_type': 'string',
                'default_value': '',
                'is_sensitive': True,
                'sort_order': 90,
            },
            {
                'key': 'aliyun_sms_secret_key',
                'name': '阿里云短信SecretKey',
                'description': '阿里云短信服务AccessKey Secret',
                'config_type': 'notification',
                'value': '',
                'value_type': 'string',
                'default_value': '',
                'is_sensitive': True,
                'sort_order': 91,
            },
            {
                'key': 'aliyun_sms_sign_name',
                'name': '阿里云短信签名',
                'description': '阿里云短信签名名称',
                'config_type': 'notification',
                'value': '',
                'value_type': 'string',
                'default_value': '',
                'sort_order': 92,
            },
            
            # ==================== 邮件服务 ====================
            {
                'key': 'sendgrid_api_key',
                'name': 'SendGrid API密钥',
                'description': 'SendGrid邮件服务API密钥',
                'config_type': 'notification',
                'value': '',
                'value_type': 'string',
                'default_value': '',
                'is_sensitive': True,
                'sort_order': 100,
            },
        ]
        
        created_count = 0
        updated_count = 0
        
        for config_data in third_party_configs:
            # 设置默认值
            if 'is_sensitive' not in config_data:
                config_data['is_sensitive'] = False
            if 'is_readonly' not in config_data:
                config_data['is_readonly'] = False
            if 'is_active' not in config_data:
                config_data['is_active'] = True
            
            config, created = SystemConfig.objects.get_or_create(
                key=config_data['key'],
                defaults=config_data
            )
            
            if created:
                created_count += 1
                self.stdout.write(
                    self.style.SUCCESS(f'创建第三方API配置: {config.name} ({config.key})')
                )
            else:
                # 更新描述和其他属性
                config.description = config_data['description']
                config.default_value = config_data['default_value']
                config.is_sensitive = config_data['is_sensitive']
                config.sort_order = config_data['sort_order']
                config.save()
                updated_count += 1
                self.stdout.write(
                    self.style.WARNING(f'更新第三方API配置: {config.name} ({config.key})')
                )
        
        self.stdout.write(
            self.style.SUCCESS(
                f'第三方API配置初始化完成: 创建 {created_count} 个，更新 {updated_count} 个'
            )
        )
        
        self.stdout.write(
            self.style.WARNING(
                '\n请在Django Admin后台 (/admin/config/systemconfig/) 中配置相应的API密钥'
            )
        )
