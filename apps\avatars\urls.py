"""
数字人形象相关API路由
"""
from django.urls import path, include, re_path
from rest_framework.routers import DefaultRouter
from . import views

router = DefaultRouter()
router.register(r'avatars', views.DigitalAvatarViewSet)
router.register(r'avatar-categories', views.AvatarCategoryViewSet)
router.register(r'avatar-tags', views.AvatarTagViewSet)
router.register(r'avatar-usage-logs', views.AvatarUsageLogViewSet)

urlpatterns = [
    path('', include(router.urls)),
    # 形象克隆相关的自定义路由
    re_path(r'^avatars/clone/(?P<task_id>\d+)/status/$', views.DigitalAvatarViewSet.as_view({'get': 'clone_status'}), name='avatar-clone-status'),
    re_path(r'^avatars/clone/(?P<task_id>\d+)/cancel/$', views.DigitalAvatarViewSet.as_view({'post': 'cancel_clone_task'}), name='avatar-clone-cancel'),
]
