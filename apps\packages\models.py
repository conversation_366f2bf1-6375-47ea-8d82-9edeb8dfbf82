"""
套餐系统模型
基于对https://hm.umi6.com/平台的逆向工程分析
"""
from django.db import models
from django.conf import settings
from django.utils.translation import gettext_lazy as _
from decimal import Decimal


class PackageCategory(models.Model):
    """套餐分类"""

    name = models.CharField(
        _('分类名称'),
        max_length=50,
        help_text=_('套餐分类的名称')
    )

    description = models.TextField(
        _('分类描述'),
        blank=True,
        help_text=_('套餐分类的描述')
    )

    sort_order = models.PositiveIntegerField(
        _('排序'),
        default=0,
        help_text=_('显示排序，数字越小越靠前')
    )

    is_active = models.BooleanField(
        _('是否启用'),
        default=True,
        help_text=_('是否启用此分类')
    )

    created_at = models.DateTimeField(
        _('创建时间'),
        auto_now_add=True
    )

    updated_at = models.DateTimeField(
        _('更新时间'),
        auto_now=True
    )

    class Meta:
        verbose_name = _('套餐分类')
        verbose_name_plural = _('套餐分类')
        db_table = 'package_categories'
        ordering = ['sort_order', 'name']

    def __str__(self):
        return self.name


class Package(models.Model):
    """套餐"""

    PACKAGE_TYPE_CHOICES = [
        ('computing_power', '算力套餐'),
        ('membership', '会员套餐'),
        ('voice_clone', '语音克隆套餐'),
        ('synthesis_time', '合成时长套餐'),
        ('combo', '组合套餐'),
    ]

    DURATION_TYPE_CHOICES = [
        ('permanent', '永久'),
        ('days', '天数'),
        ('months', '月数'),
        ('years', '年数'),
    ]

    category = models.ForeignKey(
        PackageCategory,
        on_delete=models.CASCADE,
        related_name='packages',
        verbose_name=_('套餐分类')
    )

    name = models.CharField(
        _('套餐名称'),
        max_length=100,
        help_text=_('套餐的名称')
    )

    description = models.TextField(
        _('套餐描述'),
        blank=True,
        help_text=_('套餐的详细描述')
    )

    package_type = models.CharField(
        _('套餐类型'),
        max_length=20,
        choices=PACKAGE_TYPE_CHOICES,
        help_text=_('套餐的类型')
    )

    # 价格信息
    price = models.DecimalField(
        _('价格'),
        max_digits=10,
        decimal_places=2,
        help_text=_('套餐价格（元）')
    )

    original_price = models.DecimalField(
        _('原价'),
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        help_text=_('套餐原价（元），用于显示折扣')
    )

    # 套餐内容
    computing_power_amount = models.PositiveIntegerField(
        _('算力数量'),
        default=0,
        help_text=_('套餐包含的算力点数')
    )

    bonus_computing_power = models.PositiveIntegerField(
        _('赠送算力'),
        default=0,
        help_text=_('购买套餐赠送的额外算力点数')
    )

    synthesis_duration = models.PositiveIntegerField(
        _('合成时长'),
        default=0,
        help_text=_('套餐包含的语音合成时长（秒）')
    )

    voice_clone_count = models.PositiveIntegerField(
        _('语音克隆数量'),
        default=0,
        help_text=_('套餐包含的语音克隆次数')
    )

    avatar_count = models.PositiveIntegerField(
        _('数字人形象数量'),
        default=0,
        help_text=_('套餐包含的数字人形象数量')
    )

    # 有效期
    duration_type = models.CharField(
        _('有效期类型'),
        max_length=20,
        choices=DURATION_TYPE_CHOICES,
        default='permanent',
        help_text=_('套餐有效期类型')
    )

    duration_value = models.PositiveIntegerField(
        _('有效期数值'),
        default=0,
        help_text=_('有效期的具体数值')
    )

    # 特性列表
    features = models.JSONField(
        _('特性列表'),
        default=list,
        blank=True,
        help_text=_('套餐包含的特性列表')
    )

    # 状态
    is_active = models.BooleanField(
        _('是否启用'),
        default=True,
        help_text=_('是否启用此套餐')
    )

    is_recommended = models.BooleanField(
        _('是否推荐'),
        default=False,
        help_text=_('是否为推荐套餐')
    )

    sort_order = models.PositiveIntegerField(
        _('排序'),
        default=0,
        help_text=_('显示排序，数字越小越靠前')
    )

    sales_count = models.PositiveIntegerField(
        _('销售数量'),
        default=0,
        help_text=_('套餐销售数量')
    )

    created_at = models.DateTimeField(
        _('创建时间'),
        auto_now_add=True
    )

    updated_at = models.DateTimeField(
        _('更新时间'),
        auto_now=True
    )

    class Meta:
        verbose_name = _('套餐')
        verbose_name_plural = _('套餐')
        db_table = 'packages'
        ordering = ['sort_order', '-created_at']
        indexes = [
            models.Index(fields=['package_type', 'is_active']),
            models.Index(fields=['category', 'is_active']),
        ]

    def __str__(self):
        return f"{self.name} - ¥{self.price}"

    @property
    def discount_percentage(self):
        """计算折扣百分比"""
        if self.original_price and self.original_price > self.price:
            return int((1 - self.price / self.original_price) * 100)
        return 0

    @property
    def total_computing_power(self):
        """总算力（包含赠送）"""
        return self.computing_power_amount + self.bonus_computing_power

    def increment_sales(self):
        """增加销售数量"""
        self.sales_count += 1
        self.save(update_fields=['sales_count'])


class PackageFeature(models.Model):
    """套餐功能特性"""

    FEATURE_TYPE_CHOICES = [
        ('basic', '基础功能'),
        ('advanced', '高级功能'),
        ('premium', '专业功能'),
        ('enterprise', '企业功能'),
        ('limit', '限制说明'),
        ('support', '支持服务'),
    ]

    name = models.CharField(
        _('功能名称'),
        max_length=100,
        help_text=_('功能特性的名称')
    )

    description = models.TextField(
        _('功能描述'),
        blank=True,
        help_text=_('功能特性的详细描述')
    )

    feature_type = models.CharField(
        _('功能类型'),
        max_length=20,
        choices=FEATURE_TYPE_CHOICES,
        default='basic',
        help_text=_('功能特性的类型')
    )

    icon = models.CharField(
        _('图标'),
        max_length=50,
        blank=True,
        help_text=_('Material Icons图标名称')
    )

    is_highlight = models.BooleanField(
        _('是否突出显示'),
        default=False,
        help_text=_('是否在套餐介绍中突出显示此功能')
    )

    sort_order = models.PositiveIntegerField(
        _('排序'),
        default=0,
        help_text=_('显示排序，数字越小越靠前')
    )

    is_active = models.BooleanField(
        _('是否启用'),
        default=True,
        help_text=_('是否启用此功能特性')
    )

    created_at = models.DateTimeField(
        _('创建时间'),
        auto_now_add=True
    )

    updated_at = models.DateTimeField(
        _('更新时间'),
        auto_now=True
    )

    class Meta:
        verbose_name = _('套餐功能')
        verbose_name_plural = _('套餐功能')
        db_table = 'package_features'
        ordering = ['sort_order', 'name']
        indexes = [
            models.Index(fields=['feature_type', 'is_active']),
            models.Index(fields=['is_highlight']),
        ]

    def __str__(self):
        return f"{self.name} ({self.get_feature_type_display()})"


class PackageFeatureRelation(models.Model):
    """套餐与功能的关联关系"""

    package = models.ForeignKey(
        'Package',
        on_delete=models.CASCADE,
        related_name='feature_relations',
        verbose_name=_('套餐')
    )

    feature = models.ForeignKey(
        PackageFeature,
        on_delete=models.CASCADE,
        related_name='package_relations',
        verbose_name=_('功能')
    )

    is_included = models.BooleanField(
        _('是否包含'),
        default=True,
        help_text=_('此套餐是否包含该功能')
    )

    limit_value = models.CharField(
        _('限制值'),
        max_length=100,
        blank=True,
        help_text=_('功能的限制值，如"每月100次"、"无限制"等')
    )

    custom_description = models.TextField(
        _('自定义描述'),
        blank=True,
        help_text=_('针对此套餐的功能自定义描述')
    )

    sort_order = models.PositiveIntegerField(
        _('排序'),
        default=0,
        help_text=_('在套餐中的显示排序')
    )

    created_at = models.DateTimeField(
        _('创建时间'),
        auto_now_add=True
    )

    class Meta:
        verbose_name = _('套餐功能关联')
        verbose_name_plural = _('套餐功能关联')
        db_table = 'package_feature_relations'
        unique_together = ['package', 'feature']
        ordering = ['sort_order', 'feature__sort_order']

    def __str__(self):
        status = "✓" if self.is_included else "✗"
        return f"{self.package.name} - {self.feature.name} ({status})"

    @property
    def display_description(self):
        """获取显示描述"""
        if self.custom_description:
            return self.custom_description
        elif self.limit_value:
            return f"{self.feature.description} ({self.limit_value})"
        else:
            return self.feature.description