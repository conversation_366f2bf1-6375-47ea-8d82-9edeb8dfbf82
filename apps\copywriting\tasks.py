"""
智能文案异步任务
"""
from celery import shared_task
from django.utils import timezone
from .models import Copywriting, CopywritingHistory, TextExtraction
import logging

logger = logging.getLogger(__name__)


@shared_task(bind=True, max_retries=3)
def generate_copywriting(self, copywriting_id):
    """生成智能文案任务"""
    try:
        copywriting = Copywriting.objects.get(id=copywriting_id)
        
        # 更新状态为生成中
        copywriting.status = 'generating'
        copywriting.save(update_fields=['status'])
        
        # 模拟AI文案生成过程
        import time
        time.sleep(2)  # 模拟AI处理时间
        
        # 根据场景类型生成不同的文案
        scene_templates = {
            'general': '这是一篇关于{theme}的通用文案。{theme}在当今社会具有重要的意义和价值。通过深入了解{theme}，我们可以更好地理解其背后的原理和应用。',
            'knowledge': '今天我们来了解一下{theme}。{theme}是一个非常有趣且值得深入探讨的知识点。让我们从基础概念开始，逐步深入了解{theme}的各个方面。',
            'emotion': '亲爱的朋友，关于{theme}这个话题，我想和你分享一些内心的感悟。{theme}不仅仅是一个概念，更是我们生活中不可或缺的一部分。',
            'ecommerce': '向您推荐一款优质的{theme}产品！这款产品具有卓越的品质和超高的性价比，绝对值得您的信赖和选择。现在购买还有特别优惠！',
            'blessing': '在这个特殊的时刻，我想对大家表达最真挚的祝福。愿{theme}带给每一个人幸福和快乐，愿我们都能在{theme}中找到属于自己的美好。',
            'health': '关于{theme}的健康知识，这是我们每个人都应该了解的重要内容。{theme}与我们的身体健康密切相关，让我们一起关注并重视{theme}。',
        }
        
        # 获取模板
        template = scene_templates.get(copywriting.scene_type, scene_templates['general'])
        theme = copywriting.theme_description[:20] if copywriting.theme_description else '主题'
        
        # 生成基础内容
        base_content = template.format(theme=theme)
        
        # 根据字数要求扩展内容
        target_length = copywriting.word_count
        generated_content = base_content
        
        # 扩展内容以达到目标字数
        expansion_templates = [
            f"深入分析{theme}的特点，我们可以发现其独特的价值所在。",
            f"从多个角度来看，{theme}都展现出了令人瞩目的特质。",
            f"专家们普遍认为，{theme}在未来将发挥更加重要的作用。",
            f"通过实践和研究，我们对{theme}有了更深刻的认识。",
            f"总的来说，{theme}是一个值得我们持续关注和学习的领域。",
        ]
        
        while len(generated_content) < target_length:
            for expansion in expansion_templates:
                if len(generated_content) >= target_length:
                    break
                generated_content += expansion
        
        # 截取到目标长度
        generated_content = generated_content[:target_length]
        actual_word_count = len(generated_content)
        
        # 更新文案状态
        copywriting.status = 'completed'
        copywriting.generated_content = generated_content
        copywriting.actual_word_count = actual_word_count
        copywriting.completed_at = timezone.now()
        copywriting.save(update_fields=[
            'status', 'generated_content', 'actual_word_count', 'completed_at'
        ])
        
        # 保存历史版本
        CopywritingHistory.objects.create(
            copywriting=copywriting,
            version=1,
            content=generated_content,
            word_count=actual_word_count,
            generation_params={
                'scene_type': copywriting.scene_type,
                'target_word_count': copywriting.word_count,
                'language': copywriting.language,
            }
        )
        
        logger.info(f"文案生成完成: {copywriting.title or copywriting.theme_description[:20]}")
        return f"文案生成完成，共{actual_word_count}字"
        
    except Copywriting.DoesNotExist:
        logger.error(f"文案不存在: {copywriting_id}")
        return f"文案不存在: {copywriting_id}"
    
    except Exception as exc:
        logger.error(f"文案生成失败: {exc}")
        
        # 更新失败状态
        try:
            copywriting = Copywriting.objects.get(id=copywriting_id)
            copywriting.status = 'failed'
            copywriting.error_message = str(exc)
            copywriting.save(update_fields=['status', 'error_message'])
        except:
            pass
        
        # 重试任务
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=60, exc=exc)
        
        return f"文案生成失败: {exc}"


@shared_task(bind=True, max_retries=3)
def extract_text_from_source(self, extraction_id):
    """从源文件提取文案"""
    try:
        extraction = TextExtraction.objects.get(id=extraction_id)
        
        # 更新状态为处理中
        extraction.status = 'processing'
        extraction.save(update_fields=['status'])
        
        # 模拟文案提取过程
        import time
        time.sleep(3)  # 模拟处理时间
        
        # 根据源类型模拟不同的提取结果
        if extraction.source_type == 'image':
            extracted_text = "这是从图片中识别出的文字内容。通过OCR技术，我们可以准确地提取图片中的文本信息。"
        elif extraction.source_type == 'video':
            extracted_text = "这是从视频中提取的字幕或语音转文字内容。视频包含了丰富的信息，通过AI技术可以有效提取其中的文本内容。"
        elif extraction.source_type == 'audio':
            extracted_text = "这是从音频文件中转换的文字内容。语音识别技术让我们能够将音频转换为可编辑的文本格式。"
        elif extraction.source_type == 'url':
            extracted_text = "这是从网页中提取的主要文本内容。通过智能解析，我们可以获取网页的核心信息和关键内容。"
        else:
            extracted_text = "这是从文档文件中提取的文本内容。支持多种文档格式的文本提取和内容分析。"
        
        # 模拟置信度分数
        confidence_score = 0.92
        
        # 更新提取结果
        extraction.status = 'completed'
        extraction.extracted_text = extracted_text
        extraction.confidence_score = confidence_score
        extraction.completed_at = timezone.now()
        extraction.save(update_fields=[
            'status', 'extracted_text', 'confidence_score', 'completed_at'
        ])
        
        logger.info(f"文案提取完成: {extraction.get_source_type_display()}")
        return f"文案提取完成，置信度: {confidence_score:.2%}"
        
    except TextExtraction.DoesNotExist:
        logger.error(f"文案提取任务不存在: {extraction_id}")
        return f"文案提取任务不存在: {extraction_id}"
    
    except Exception as exc:
        logger.error(f"文案提取失败: {exc}")
        
        # 更新失败状态
        try:
            extraction = TextExtraction.objects.get(id=extraction_id)
            extraction.status = 'failed'
            extraction.error_message = str(exc)
            extraction.save(update_fields=['status', 'error_message'])
        except:
            pass
        
        # 重试任务
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=60, exc=exc)
        
        return f"文案提取失败: {exc}"


@shared_task
def cleanup_expired_copywriting():
    """清理过期的文案"""
    from datetime import timedelta
    
    # 删除30天前的草稿文案
    expired_date = timezone.now() - timedelta(days=30)
    expired_copywriting = Copywriting.objects.filter(
        status='pending',
        created_at__lt=expired_date
    )
    
    count = expired_copywriting.count()
    expired_copywriting.delete()
    
    logger.info(f"清理了 {count} 个过期的文案")
    return f"清理了 {count} 个过期的文案"
