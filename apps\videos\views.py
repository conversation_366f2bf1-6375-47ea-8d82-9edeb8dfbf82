"""
视频生成相关视图
"""
from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import SearchFilter, OrderingFilter

from .models import VideoProduction, VideoTemplate, VideoShare
from .serializers import (
    VideoProductionSerializer, VideoTemplateSerializer, VideoShareSerializer
)


class VideoProductionViewSet(viewsets.ModelViewSet):
    """视频作品视图集"""
    queryset = VideoProduction.objects.all()
    serializer_class = VideoProductionSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['status', 'drive_mode', 'video_quality', 'is_public', 'is_favorite']
    search_fields = ['title', 'description', 'text_content']
    ordering_fields = ['created_at', 'updated_at', 'view_count']
    ordering = ['-created_at']
    
    def get_queryset(self):
        """普通用户只能查看自己的视频作品"""
        if self.request.user.is_staff:
            return self.queryset
        return self.queryset.filter(user=self.request.user)
    
    def perform_create(self, serializer):
        """创建视频作品时设置用户"""
        serializer.save(user=self.request.user)
    
    @action(detail=True, methods=['post'])
    def start_generation(self, request, pk=None):
        """开始生成视频"""
        video = self.get_object()
        user = request.user
        
        if video.status != 'draft':
            return Response(
                {'error': '只有草稿状态的视频才能开始生成'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # 计算算力消耗
        avatar_cost = video.avatar.computing_power_cost
        voice_cost = video.voice_model.computing_power_cost
        
        if video.drive_mode == 'text' and video.text_content:
            # 文本驱动：按字数计算
            char_count = len(video.text_content)
            duration_minutes = max(char_count / 180, 1)  # 假设每分钟180字
        else:
            # 语音驱动：按音频时长计算
            duration_minutes = 1  # 默认1分钟，实际应该从音频文件获取
        
        total_cost = avatar_cost + int(voice_cost * duration_minutes)
        
        # 检查算力是否足够
        if user.computing_power < total_cost:
            return Response(
                {'error': f'算力不足，需要 {total_cost} 算力'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # 消耗算力
        try:
            user.consume_computing_power(
                total_cost,
                f'生成视频: {video.title}'
            )
            
            # 更新视频状态
            video.status = 'pending'
            video.computing_power_consumed = total_cost
            video.save(update_fields=['status', 'computing_power_consumed'])
            
            return Response({
                'message': '视频生成任务已开始',
                'computing_power_consumed': total_cost,
                'remaining_power': user.computing_power
            })
            
        except ValueError as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
    
    @action(detail=True, methods=['post'])
    def toggle_favorite(self, request, pk=None):
        """切换收藏状态"""
        video = self.get_object()
        video.is_favorite = not video.is_favorite
        video.save(update_fields=['is_favorite'])
        
        return Response({
            'message': '收藏状态已更新',
            'is_favorite': video.is_favorite
        })
    
    @action(detail=True, methods=['post'])
    def increment_view(self, request, pk=None):
        """增加观看次数"""
        video = self.get_object()
        video.increment_view()
        
        return Response({
            'message': '观看次数已更新',
            'view_count': video.view_count
        })
    
    @action(detail=True, methods=['post'])
    def increment_download(self, request, pk=None):
        """增加下载次数"""
        video = self.get_object()
        video.increment_download()
        
        return Response({
            'message': '下载次数已更新',
            'download_count': video.download_count
        })
    
    @action(detail=False, methods=['post'])
    def generate(self, request):
        """生成数字人视频"""
        try:
            # 获取请求参数
            script = request.data.get('script')
            avatar_id = request.data.get('avatar_id')
            voice_id = request.data.get('voice_id')

            if not script:
                return Response({
                    'success': False,
                    'message': '请输入视频文案'
                }, status=status.HTTP_400_BAD_REQUEST)

            if not avatar_id:
                return Response({
                    'success': False,
                    'message': '请选择数字人形象'
                }, status=status.HTTP_400_BAD_REQUEST)

            # 检查数字人形象是否存在
            from apps.avatars.models import DigitalAvatar
            try:
                avatar = DigitalAvatar.objects.get(id=avatar_id, owner=request.user)
                # 提取百度曦灵的figure_id
                if avatar.video_url and avatar.video_url.startswith('baidu_xiling://figure/'):
                    figure_id = avatar.video_url.replace('baidu_xiling://figure/', '')
                else:
                    return Response({
                        'success': False,
                        'message': '该数字人形象不支持视频生成'
                    }, status=status.HTTP_400_BAD_REQUEST)
            except DigitalAvatar.DoesNotExist:
                return Response({
                    'success': False,
                    'message': '数字人形象不存在'
                }, status=status.HTTP_404_NOT_FOUND)

            # 创建任务记录
            from apps.tasks.models import TaskQueue
            task = TaskQueue.objects.create(
                name=f'视频生成: {script[:20]}...',
                task_type='video_generation',
                task_function='generate_baidu_video',
                created_by=request.user,
                kwargs={
                    'figure_id': figure_id,
                    'text': script,
                    'voice_id': voice_id or 'zh-CN-XiaoxiaoNeural',
                    'user_id': request.user.id
                }
            )

            # 启动异步任务
            from apps.tasks.tasks import generate_baidu_video_task
            generate_baidu_video_task.delay(
                task_id=task.id,
                figure_id=figure_id,
                text=script,
                voice_id=voice_id or 'zh-CN-XiaoxiaoNeural',
                user_id=request.user.id
            )

            return Response({
                'success': True,
                'message': '视频生成任务已提交，预计需要2-5分钟完成',
                'task_id': task.id,
                'estimated_time': '2-5分钟'
            })

        except Exception as e:
            return Response({
                'success': False,
                'message': f'生成视频失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['get'], url_path='generate/(?P<task_id>[^/.]+)/status')
    def generate_status(self, request, task_id=None):
        """获取视频生成状态"""
        try:
            from apps.tasks.models import TaskQueue
            task = TaskQueue.objects.get(id=task_id, created_by=request.user)

            return Response({
                'success': True,
                'task_id': task.id,
                'status': task.status,
                'progress': task.progress,
                'progress_message': task.progress_message,
                'created_at': task.created_at,
                'updated_at': task.updated_at,
                'result': task.result,
                'error_message': task.error_message
            })

        except TaskQueue.DoesNotExist:
            return Response({
                'success': False,
                'message': '任务不存在'
            }, status=status.HTTP_404_NOT_FOUND)

    @action(detail=True, methods=['post'])
    def create_share(self, request, pk=None):
        """创建分享链接"""
        video = self.get_object()
        
        # 生成分享令牌
        import uuid
        share_token = uuid.uuid4().hex
        
        share = VideoShare.objects.create(
            video=video,
            share_token=share_token,
            password=request.data.get('password', ''),
            expire_at=request.data.get('expire_at')
        )
        
        return Response({
            'message': '分享链接已创建',
            'share_token': share_token,
            'share_url': f'/share/{share_token}/'
        })
    
    @action(detail=False, methods=['get'])
    def my_favorites(self, request):
        """获取我的收藏视频"""
        videos = self.get_queryset().filter(is_favorite=True)
        page = self.paginate_queryset(videos)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(videos, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def public_videos(self, request):
        """获取公开视频"""
        videos = VideoProduction.objects.filter(
            is_public=True, 
            status='completed'
        ).order_by('-created_at')
        
        page = self.paginate_queryset(videos)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(videos, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def status(self, request, pk=None):
        """获取视频生成状态"""
        video = self.get_object()
        return Response({
            'status': video.status,
            'progress': video.progress,
            'error_message': video.error_message,
            'video_url': video.video_url,
            'thumbnail_url': video.thumbnail_url
        })

    @action(detail=True, methods=['get'])
    def download(self, request, pk=None):
        """下载视频"""
        video = self.get_object()

        if video.status != 'completed':
            return Response(
                {'error': '视频尚未生成完成'},
                status=status.HTTP_400_BAD_REQUEST
            )

        if not video.video_url:
            return Response(
                {'error': '视频文件不存在'},
                status=status.HTTP_404_NOT_FOUND
            )

        # 增加下载次数
        video.increment_download()

        return Response({
            'download_url': video.video_url,
            'file_name': f'{video.title}.mp4'
        })

    @action(detail=False, methods=['get'])
    def my_videos(self, request):
        """获取我的视频"""
        videos = self.get_queryset()
        page = self.paginate_queryset(videos)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(videos, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def preview(self, request, pk=None):
        """视频预览"""
        video = self.get_object()

        # 增加观看次数
        video.increment_view()

        return Response({
            'video_url': video.video_url,
            'thumbnail_url': video.thumbnail_url,
            'title': video.title,
            'description': video.description,
            'duration': video.duration,
            'view_count': video.view_count
        })


class VideoTemplateViewSet(viewsets.ReadOnlyModelViewSet):
    """视频模板视图集"""
    queryset = VideoTemplate.objects.filter(is_active=True)
    serializer_class = VideoTemplateSerializer
    permission_classes = [permissions.AllowAny]  # 允许匿名访问模板列表
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['category']
    search_fields = ['name', 'description']
    ordering_fields = ['sort_order', 'usage_count']
    ordering = ['sort_order', '-usage_count']
    
    @action(detail=True, methods=['post'])
    def use_template(self, request, pk=None):
        """使用模板创建视频"""
        template = self.get_object()
        user = request.user
        
        # 基于模板创建视频
        video_data = {
            'title': request.data.get('title', f'基于{template.name}的视频'),
            'description': request.data.get('description', ''),
            'avatar': template.default_avatar,
            'voice_model': template.default_voice,
            'text_content': template.template_content,
            'user': user
        }
        
        # 应用模板设置
        if template.settings:
            video_data.update(template.settings)
        
        video = VideoProduction.objects.create(**video_data)
        
        # 增加模板使用次数
        template.increment_usage()
        
        serializer = VideoProductionSerializer(video)
        return Response({
            'message': f'成功使用模板 {template.name} 创建视频',
            'video': serializer.data
        })

    @action(detail=False, methods=['get'], permission_classes=[permissions.AllowAny])
    def popular(self, request):
        """获取热门模板"""
        templates = self.get_queryset().filter(usage_count__gt=0).order_by('-usage_count')[:10]
        serializer = self.get_serializer(templates, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'], permission_classes=[permissions.AllowAny])
    def categories(self, request):
        """获取模板分类"""
        categories = VideoTemplate.CATEGORY_CHOICES
        return Response([
            {'value': choice[0], 'label': choice[1]}
            for choice in categories
        ])


class VideoShareViewSet(viewsets.ReadOnlyModelViewSet):
    """视频分享视图集"""
    queryset = VideoShare.objects.filter(is_active=True)
    serializer_class = VideoShareSerializer
    permission_classes = [permissions.AllowAny]
    lookup_field = 'share_token'
    
    @action(detail=True, methods=['post'])
    def access_share(self, request, share_token=None):
        """访问分享视频"""
        try:
            share = self.get_object()
            password = request.data.get('password', '')
            
            # 检查密码
            if share.password and share.password != password:
                return Response(
                    {'error': '访问密码错误'},
                    status=status.HTTP_403_FORBIDDEN
                )
            
            # 检查是否过期
            from django.utils import timezone
            if share.expire_at and share.expire_at < timezone.now():
                return Response(
                    {'error': '分享链接已过期'},
                    status=status.HTTP_410_GONE
                )
            
            # 增加访问次数
            share.increment_view()
            
            # 返回视频信息
            video_serializer = VideoProductionSerializer(share.video)
            return Response({
                'video': video_serializer.data,
                'share_info': {
                    'view_count': share.view_count,
                    'created_at': share.created_at
                }
            })
            
        except VideoShare.DoesNotExist:
            return Response(
                {'error': '分享链接不存在'},
                status=status.HTTP_404_NOT_FOUND
            )
