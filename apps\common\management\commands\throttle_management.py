"""
限流管理命令
"""
import time
from django.core.management.base import BaseCommand
from django.core.cache import cache
from django.test import RequestFactory
from django.contrib.auth import get_user_model
from apps.common.throttling import (
    AnonRateThrottle, UserRateThrottle, LoginRateThrottle,
    RegisterRateThrottle, UploadRateThrottle, AIGenerateRateThrottle
)

User = get_user_model()


class Command(BaseCommand):
    help = '限流管理工具'
    
    def add_arguments(self, parser):
        parser.add_argument(
            'action',
            choices=['test', 'clear', 'stats', 'simulate'],
            help='执行的操作'
        )
        parser.add_argument(
            '--throttle',
            type=str,
            choices=['anon', 'user', 'login', 'register', 'upload', 'ai_generate'],
            help='限流器类型'
        )
        parser.add_argument(
            '--requests',
            type=int,
            default=10,
            help='模拟请求数量'
        )
    
    def handle(self, *args, **options):
        action = options['action']
        
        if action == 'test':
            self.test_throttles()
        elif action == 'clear':
            self.clear_throttle_cache(options.get('throttle'))
        elif action == 'stats':
            self.show_throttle_stats()
        elif action == 'simulate':
            self.simulate_requests(options)
    
    def test_throttles(self):
        """测试所有限流器"""
        self.stdout.write('测试限流器功能...')
        self.stdout.write('-' * 50)
        
        throttle_classes = [
            ('匿名用户限流', AnonRateThrottle),
            ('认证用户限流', UserRateThrottle),
            ('登录限流', LoginRateThrottle),
            ('注册限流', RegisterRateThrottle),
            ('上传限流', UploadRateThrottle),
            ('AI生成限流', AIGenerateRateThrottle),
        ]
        
        factory = RequestFactory()
        
        for name, throttle_class in throttle_classes:
            try:
                throttle = throttle_class()
                
                # 设置限流参数
                if hasattr(throttle, 'scope') and throttle.scope in throttle.THROTTLE_RATES:
                    rate = throttle.THROTTLE_RATES[throttle.scope]
                    throttle.num_requests, throttle.duration = throttle.parse_rate(rate)
                    throttle.cache = cache
                
                # 创建测试请求
                request = factory.get('/test/')
                request.META['REMOTE_ADDR'] = '127.0.0.1'

                # 添加用户属性
                if 'user' in name.lower() or 'ai' in name.lower() or 'upload' in name.lower():
                    request.user = type('User', (), {'pk': 1, 'is_authenticated': True})()
                else:
                    request.user = type('User', (), {'is_authenticated': False})()
                    request.user.pk = None
                
                # 测试限流
                result = throttle.allow_request(request, None)
                
                if result:
                    status = self.style.SUCCESS('✓ 正常')
                else:
                    status = self.style.WARNING('⚠ 已限流')
                
                rate_info = f"({rate})" if hasattr(throttle, 'scope') else ""
                self.stdout.write(f'{name:15} | {status} {rate_info}')
                
            except Exception as e:
                self.stdout.write(f'{name:15} | {self.style.ERROR("✗ 错误")}: {str(e)}')
    
    def clear_throttle_cache(self, throttle_type=None):
        """清除限流缓存"""
        if throttle_type:
            pattern = f'throttle_{throttle_type}_*'
            try:
                if hasattr(cache, 'delete_pattern'):
                    cache.delete_pattern(pattern)
                    self.stdout.write(
                        self.style.SUCCESS(f'成功清除 {throttle_type} 限流缓存')
                    )
                else:
                    self.stdout.write(
                        self.style.WARNING('缓存后端不支持模式删除')
                    )
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'清除限流缓存失败: {str(e)}')
                )
        else:
            # 清除所有限流缓存
            try:
                if hasattr(cache, 'delete_pattern'):
                    cache.delete_pattern('throttle_*')
                    self.stdout.write(
                        self.style.SUCCESS('成功清除所有限流缓存')
                    )
                else:
                    # 手动清除已知的限流缓存
                    throttle_types = ['anon', 'user', 'login', 'register', 'upload', 'ai_generate']
                    for t_type in throttle_types:
                        cache.delete_many([f'throttle_{t_type}_127.0.0.1'])
                    
                    self.stdout.write(
                        self.style.SUCCESS('成功清除已知限流缓存')
                    )
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'清除限流缓存失败: {str(e)}')
                )
    
    def show_throttle_stats(self):
        """显示限流统计"""
        self.stdout.write('限流配置统计:')
        self.stdout.write('-' * 50)
        
        throttle_rates = {
            'anon': '100/hour',
            'user': '1000/hour',
            'login': '5/min',
            'register': '3/min',
            'upload': '10/min',
            'ai_generate': '20/hour',
            'burst': '10/min',
        }
        
        for scope, rate in throttle_rates.items():
            # 解析速率
            num, period = rate.split('/')
            duration = {'s': 1, 'm': 60, 'h': 3600, 'd': 86400}[period[0]]
            
            self.stdout.write(f'{scope:12} | {rate:10} | {num:3}次/{duration:5}秒')
        
        # 显示当前缓存状态
        self.stdout.write('\n当前限流状态:')
        self.stdout.write('-' * 50)
        
        test_keys = [
            'throttle_anon_127.0.0.1',
            'throttle_user_1',
            'throttle_login_127.0.0.1',
        ]
        
        for key in test_keys:
            value = cache.get(key)
            if value:
                count = len(value) if isinstance(value, list) else 'N/A'
                self.stdout.write(f'{key:25} | 请求数: {count}')
            else:
                self.stdout.write(f'{key:25} | 无缓存')
    
    def simulate_requests(self, options):
        """模拟请求测试限流"""
        throttle_type = options.get('throttle', 'anon')
        num_requests = options.get('requests', 10)
        
        self.stdout.write(f'模拟 {throttle_type} 限流器，发送 {num_requests} 个请求...')
        self.stdout.write('-' * 50)
        
        # 选择限流器
        throttle_map = {
            'anon': AnonRateThrottle,
            'user': UserRateThrottle,
            'login': LoginRateThrottle,
            'register': RegisterRateThrottle,
            'upload': UploadRateThrottle,
            'ai_generate': AIGenerateRateThrottle,
        }
        
        throttle_class = throttle_map.get(throttle_type)
        if not throttle_class:
            self.stdout.write(
                self.style.ERROR(f'未知的限流器类型: {throttle_type}')
            )
            return
        
        throttle = throttle_class()
        
        # 设置限流参数
        if hasattr(throttle, 'scope') and throttle.scope in throttle.THROTTLE_RATES:
            rate = throttle.THROTTLE_RATES[throttle.scope]
            throttle.num_requests, throttle.duration = throttle.parse_rate(rate)
            throttle.cache = cache
        
        factory = RequestFactory()
        allowed_count = 0
        throttled_count = 0
        
        for i in range(num_requests):
            # 创建测试请求
            request = factory.get(f'/test/{i}/')
            request.META['REMOTE_ADDR'] = '127.0.0.1'
            
            # 如果是用户限流，模拟用户
            if throttle_type == 'user':
                request.user = type('User', (), {'pk': 1, 'is_authenticated': True})()
            else:
                request.user = type('User', (), {'is_authenticated': False})()
            
            # 测试限流
            if throttle.allow_request(request, None):
                allowed_count += 1
                status = self.style.SUCCESS('✓')
            else:
                throttled_count += 1
                status = self.style.ERROR('✗')
                
                # 显示等待时间
                wait_time = throttle.wait()
                if wait_time:
                    self.stdout.write(f'请求 {i+1:2d} | {status} 被限流，等待时间: {wait_time:.2f}秒')
                else:
                    self.stdout.write(f'请求 {i+1:2d} | {status} 被限流')
                continue
            
            self.stdout.write(f'请求 {i+1:2d} | {status} 允许')
            
            # 短暂延迟
            time.sleep(0.1)
        
        self.stdout.write('-' * 50)
        self.stdout.write(f'模拟完成: 允许 {allowed_count} 个，限流 {throttled_count} 个')
        
        success_rate = (allowed_count / num_requests) * 100
        self.stdout.write(f'成功率: {success_rate:.1f}%')
