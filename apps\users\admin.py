"""
用户管理后台配置
基于Django Unfold主题优化的管理界面
"""
from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.utils.translation import gettext_lazy as _
from unfold.admin import ModelAdmin, TabularInline
from unfold.contrib.filters.admin import RangeDateFilter

from .models import User, ComputingPowerLog, UserPackage


class ComputingPowerLogInline(TabularInline):
    """算力日志内联编辑"""
    model = ComputingPowerLog
    extra = 0
    readonly_fields = ('created_at',)
    fields = ('operation_type', 'amount', 'balance_after', 'reason', 'created_at')
    
    def has_add_permission(self, request, obj=None):
        return False


class UserPackageInline(TabularInline):
    """用户套餐内联编辑"""
    model = UserPackage
    extra = 0
    readonly_fields = ('created_at',)
    fields = ('package_type', 'duration_days', 'computing_power_included', 
              'price', 'start_date', 'end_date', 'is_active', 'created_at')


@admin.register(User)
class UserAdmin(BaseUserAdmin, ModelAdmin):
    """用户管理"""
    
    # 列表页配置
    list_display = (
        'username', 'phone', 'email', 'display_name', 
        'computing_power', 'package_type', 'is_verified',
        'is_active', 'date_joined'
    )
    
    list_filter = (
        'is_active', 'is_verified', 'package_type',
        ('date_joined', RangeDateFilter),
        ('last_login', RangeDateFilter),
    )
    
    search_fields = ('username', 'phone', 'email', 'first_name', 'last_name')
    
    ordering = ('-date_joined',)
    
    # 详情页配置
    fieldsets = (
        (_('基本信息'), {
            'fields': ('username', 'password')
        }),
        (_('个人信息'), {
            'fields': ('first_name', 'last_name', 'email', 'phone', 'avatar')
        }),
        (_('平台信息'), {
            'fields': ('computing_power', 'total_computing_power', 'package_type', 
                      'package_expire_date', 'is_verified')
        }),
        (_('权限'), {
            'fields': ('is_active', 'is_staff', 'is_superuser', 'groups', 'user_permissions'),
            'classes': ('collapse',)
        }),
        (_('重要日期'), {
            'fields': ('last_login', 'date_joined', 'last_login_ip'),
            'classes': ('collapse',)
        }),
    )
    
    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': ('username', 'phone', 'email', 'password1', 'password2'),
        }),
    )
    
    readonly_fields = ('date_joined', 'last_login', 'total_computing_power')
    
    inlines = [ComputingPowerLogInline, UserPackageInline]
    
    # 自定义操作
    actions = ['activate_users', 'deactivate_users', 'verify_users']
    
    def activate_users(self, request, queryset):
        """批量激活用户"""
        updated = queryset.update(is_active=True)
        self.message_user(request, f'成功激活 {updated} 个用户')
    activate_users.short_description = _('激活选中的用户')
    
    def deactivate_users(self, request, queryset):
        """批量停用用户"""
        updated = queryset.update(is_active=False)
        self.message_user(request, f'成功停用 {updated} 个用户')
    deactivate_users.short_description = _('停用选中的用户')
    
    def verify_users(self, request, queryset):
        """批量验证用户"""
        updated = queryset.update(is_verified=True)
        self.message_user(request, f'成功验证 {updated} 个用户')
    verify_users.short_description = _('验证选中的用户')


@admin.register(ComputingPowerLog)
class ComputingPowerLogAdmin(ModelAdmin):
    """算力日志管理"""
    
    list_display = (
        'user', 'operation_type', 'amount', 'balance_after', 
        'reason', 'created_at'
    )
    
    list_filter = (
        'operation_type',
        ('created_at', RangeDateFilter),
    )
    
    search_fields = ('user__username', 'user__phone', 'reason')
    
    readonly_fields = ('created_at',)
    
    ordering = ('-created_at',)
    
    def has_add_permission(self, request):
        return False
    
    def has_change_permission(self, request, obj=None):
        return False


@admin.register(UserPackage)
class UserPackageAdmin(ModelAdmin):
    """用户套餐管理"""
    
    list_display = (
        'user', 'package_type', 'duration_days', 'computing_power_included',
        'price', 'start_date', 'end_date', 'is_active'
    )
    
    list_filter = (
        'package_type', 'is_active',
        ('start_date', RangeDateFilter),
        ('end_date', RangeDateFilter),
    )
    
    search_fields = ('user__username', 'user__phone')
    
    readonly_fields = ('created_at',)
    
    ordering = ('-created_at',)
    
    # 自定义操作
    actions = ['activate_packages', 'deactivate_packages']
    
    def activate_packages(self, request, queryset):
        """批量激活套餐"""
        updated = queryset.update(is_active=True)
        self.message_user(request, f'成功激活 {updated} 个套餐')
    activate_packages.short_description = _('激活选中的套餐')
    
    def deactivate_packages(self, request, queryset):
        """批量停用套餐"""
        updated = queryset.update(is_active=False)
        self.message_user(request, f'成功停用 {updated} 个套餐')
    deactivate_packages.short_description = _('停用选中的套餐')
