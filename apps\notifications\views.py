"""
通知视图
"""
from rest_framework import generics, status, permissions
from rest_framework.views import APIView
from rest_framework.response import Response
from django.utils import timezone
from .models import Notification, UserNotificationSettings
from .serializers import NotificationSerializer, UserNotificationSettingsSerializer
from .services import notification_service


class NotificationListView(generics.ListAPIView):
    """通知列表视图"""
    serializer_class = NotificationSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """获取当前用户的通知"""
        unread_only = self.request.query_params.get('unread_only', 'false').lower() == 'true'
        
        queryset = Notification.objects.filter(user=self.request.user)
        
        if unread_only:
            queryset = queryset.filter(is_read=False)
        
        return queryset.order_by('-created_at')


class MarkNotificationReadView(APIView):
    """标记通知为已读"""
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request, pk):
        """标记指定通知为已读"""
        try:
            notification = Notification.objects.get(
                id=pk, 
                user=request.user
            )
            notification.mark_as_read()
            
            return Response({
                'message': '通知已标记为已读',
                'notification_id': pk
            })
        except Notification.DoesNotExist:
            return Response(
                {'error': '通知不存在'},
                status=status.HTTP_404_NOT_FOUND
            )


class MarkAllReadView(APIView):
    """标记所有通知为已读"""
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request):
        """标记所有通知为已读"""
        notification_service.mark_all_read(request.user)
        
        return Response({
            'message': '所有通知已标记为已读'
        })


class UnreadCountView(APIView):
    """获取未读通知数量"""
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request):
        """获取未读通知数量"""
        count = Notification.objects.filter(
            user=request.user,
            is_read=False
        ).count()
        
        return Response({
            'unread_count': count
        })


class NotificationSettingsView(APIView):
    """通知设置视图"""
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request):
        """获取用户通知设置"""
        settings, created = UserNotificationSettings.objects.get_or_create(
            user=request.user
        )
        serializer = UserNotificationSettingsSerializer(settings)
        return Response(serializer.data)
    
    def put(self, request):
        """更新用户通知设置"""
        settings, created = UserNotificationSettings.objects.get_or_create(
            user=request.user
        )
        serializer = UserNotificationSettingsSerializer(
            settings, 
            data=request.data, 
            partial=True
        )
        
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        
        return Response(
            serializer.errors,
            status=status.HTTP_400_BAD_REQUEST
        )


class SendTestNotificationView(APIView):
    """发送测试通知（仅管理员）"""
    permission_classes = [permissions.IsAdminUser]
    
    def post(self, request):
        """发送测试通知"""
        user_id = request.data.get('user_id')
        message = request.data.get('message', '这是一条测试通知')
        
        if not user_id:
            return Response(
                {'error': '请提供用户ID'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            from django.contrib.auth import get_user_model
            User = get_user_model()
            user = User.objects.get(id=user_id)
            
            notification_service.create_notification(
                user=user,
                notification_type='system',
                title='测试通知',
                message=message,
                priority='normal'
            )
            
            return Response({
                'message': f'测试通知已发送给用户 {user.username}'
            })
            
        except User.DoesNotExist:
            return Response(
                {'error': '用户不存在'},
                status=status.HTTP_404_NOT_FOUND
            )
