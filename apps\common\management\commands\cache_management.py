"""
缓存管理命令
"""
from django.core.management.base import BaseCommand
from django.core.cache import cache
from apps.common.cache import cache_manager


class Command(BaseCommand):
    help = '缓存管理工具'
    
    def add_arguments(self, parser):
        parser.add_argument(
            'action',
            choices=['clear', 'stats', 'warm', 'test'],
            help='执行的操作'
        )
        parser.add_argument(
            '--prefix',
            type=str,
            help='缓存前缀（用于clear操作）'
        )
        parser.add_argument(
            '--pattern',
            type=str,
            help='缓存模式（用于clear操作）'
        )
    
    def handle(self, *args, **options):
        action = options['action']
        
        if action == 'clear':
            self.clear_cache(options)
        elif action == 'stats':
            self.show_stats()
        elif action == 'warm':
            self.warm_cache()
        elif action == 'test':
            self.test_cache()
    
    def clear_cache(self, options):
        """清除缓存"""
        prefix = options.get('prefix')
        pattern = options.get('pattern')
        
        if pattern:
            # 清除匹配模式的缓存
            result = cache_manager.delete_pattern(pattern)
            if result:
                self.stdout.write(
                    self.style.SUCCESS(f'成功清除匹配模式的缓存: {pattern}')
                )
            else:
                self.stdout.write(
                    self.style.WARNING(f'清除缓存模式失败或不支持: {pattern}')
                )
        elif prefix:
            # 清除指定前缀的缓存
            pattern = f"{cache_manager.PREFIXES.get(prefix, prefix)}:*"
            result = cache_manager.delete_pattern(pattern)
            if result:
                self.stdout.write(
                    self.style.SUCCESS(f'成功清除前缀缓存: {prefix}')
                )
            else:
                self.stdout.write(
                    self.style.WARNING(f'清除前缀缓存失败: {prefix}')
                )
        else:
            # 清除所有缓存
            cache.clear()
            self.stdout.write(
                self.style.SUCCESS('成功清除所有缓存')
            )
    
    def show_stats(self):
        """显示缓存统计"""
        self.stdout.write('缓存统计信息:')
        self.stdout.write('-' * 40)
        
        # 测试各个前缀的缓存
        for prefix, timeout in cache_manager.DEFAULT_TIMEOUT.items():
            test_key = cache_manager.make_key(prefix, 'test')
            cache.set(test_key, 'test_value', 10)
            
            if cache.get(test_key):
                status = self.style.SUCCESS('✓ 可用')
            else:
                status = self.style.ERROR('✗ 不可用')
            
            self.stdout.write(f'{prefix:12} | 默认超时: {timeout:4}s | {status}')
            cache.delete(test_key)
        
        # 显示缓存配置
        self.stdout.write('\n缓存配置:')
        self.stdout.write('-' * 40)
        
        try:
            from django.conf import settings
            cache_config = getattr(settings, 'CACHES', {}).get('default', {})
            backend = cache_config.get('BACKEND', '未知')
            location = cache_config.get('LOCATION', '未知')
            
            self.stdout.write(f'后端: {backend}')
            self.stdout.write(f'位置: {location}')
        except Exception as e:
            self.stdout.write(f'获取缓存配置失败: {str(e)}')
    
    def warm_cache(self):
        """预热缓存"""
        self.stdout.write('开始预热缓存...')
        
        try:
            # 预热数字人缓存
            from apps.avatars.models import DigitalAvatar
            avatars = DigitalAvatar.objects.filter(is_public=True, is_active=True)[:20]
            
            for avatar in avatars:
                cache_key = cache_manager.make_key('avatar', 'detail', avatar.id)
                cache.set(cache_key, {
                    'id': avatar.id,
                    'name': avatar.name,
                    'description': avatar.description,
                }, 3600)
            
            self.stdout.write(f'预热数字人缓存: {len(avatars)} 个')
            
            # 预热语音模型缓存
            from apps.voices.models import VoiceModel
            voices = VoiceModel.objects.filter(is_public=True, is_active=True)[:20]
            
            for voice in voices:
                cache_key = cache_manager.make_key('voice', 'detail', voice.id)
                cache.set(cache_key, {
                    'id': voice.id,
                    'name': voice.name,
                    'language': voice.language,
                    'gender': voice.gender,
                }, 3600)
            
            self.stdout.write(f'预热语音模型缓存: {len(voices)} 个')
            
            # 预热系统配置缓存
            from apps.config.models import config_manager as config_mgr
            configs = config_mgr.get_all()
            self.stdout.write(f'预热系统配置缓存: {len(configs)} 个')
            
            self.stdout.write(
                self.style.SUCCESS('缓存预热完成')
            )
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'缓存预热失败: {str(e)}')
            )
    
    def test_cache(self):
        """测试缓存功能"""
        self.stdout.write('测试缓存功能...')
        
        test_cases = [
            ('string', 'test_string_value'),
            ('integer', 12345),
            ('float', 123.45),
            ('boolean', True),
            ('list', [1, 2, 3, 'test']),
            ('dict', {'key': 'value', 'number': 42}),
        ]
        
        success_count = 0
        
        for data_type, test_value in test_cases:
            try:
                # 设置缓存
                test_key = f'test_{data_type}'
                cache.set(test_key, test_value, 60)
                
                # 获取缓存
                cached_value = cache.get(test_key)
                
                # 验证结果
                if cached_value == test_value:
                    self.stdout.write(f'✓ {data_type:8} 测试通过')
                    success_count += 1
                else:
                    self.stdout.write(f'✗ {data_type:8} 测试失败: 期望 {test_value}, 得到 {cached_value}')
                
                # 清理测试数据
                cache.delete(test_key)
                
            except Exception as e:
                self.stdout.write(f'✗ {data_type:8} 测试异常: {str(e)}')
        
        self.stdout.write(f'\n测试结果: {success_count}/{len(test_cases)} 通过')
        
        if success_count == len(test_cases):
            self.stdout.write(
                self.style.SUCCESS('所有缓存测试通过')
            )
        else:
            self.stdout.write(
                self.style.WARNING('部分缓存测试失败')
            )
