# Generated by Django 5.2.1 on 2025-07-16 12:31

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="NotificationTemplate",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "type",
                    models.CharField(max_length=20, unique=True, verbose_name="通知类型"),
                ),
                (
                    "title_template",
                    models.CharField(max_length=200, verbose_name="标题模板"),
                ),
                ("message_template", models.TextField(verbose_name="消息模板")),
                ("is_active", models.BooleanField(default=True, verbose_name="启用")),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
            ],
            options={
                "verbose_name": "通知模板",
                "verbose_name_plural": "通知模板",
            },
        ),
        migrations.CreateModel(
            name="UserNotificationSettings",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "email_notifications",
                    models.BooleanField(default=True, verbose_name="邮件通知"),
                ),
                (
                    "browser_notifications",
                    models.BooleanField(default=True, verbose_name="浏览器通知"),
                ),
                (
                    "video_progress_notifications",
                    models.BooleanField(default=True, verbose_name="视频进度通知"),
                ),
                (
                    "system_notifications",
                    models.BooleanField(default=True, verbose_name="系统通知"),
                ),
                (
                    "package_notifications",
                    models.BooleanField(default=True, verbose_name="套餐通知"),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="用户",
                    ),
                ),
            ],
            options={
                "verbose_name": "用户通知设置",
                "verbose_name_plural": "用户通知设置",
            },
        ),
        migrations.CreateModel(
            name="Notification",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "type",
                    models.CharField(
                        choices=[
                            ("system", "系统通知"),
                            ("video_progress", "视频生成进度"),
                            ("video_complete", "视频生成完成"),
                            ("voice_complete", "语音合成完成"),
                            ("copywriting_complete", "文案生成完成"),
                            ("package_expire", "套餐即将过期"),
                            ("computing_low", "算力不足"),
                            ("error", "错误通知"),
                        ],
                        max_length=20,
                        verbose_name="通知类型",
                    ),
                ),
                ("title", models.CharField(max_length=200, verbose_name="标题")),
                ("message", models.TextField(verbose_name="消息内容")),
                (
                    "priority",
                    models.CharField(
                        choices=[
                            ("low", "低"),
                            ("normal", "普通"),
                            ("high", "高"),
                            ("urgent", "紧急"),
                        ],
                        default="normal",
                        max_length=10,
                        verbose_name="优先级",
                    ),
                ),
                (
                    "extra_data",
                    models.TextField(
                        blank=True, help_text="JSON格式的额外数据", verbose_name="额外数据"
                    ),
                ),
                ("is_read", models.BooleanField(default=False, verbose_name="已读")),
                ("is_sent", models.BooleanField(default=False, verbose_name="已发送")),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "read_at",
                    models.DateTimeField(blank=True, null=True, verbose_name="阅读时间"),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="用户",
                    ),
                ),
            ],
            options={
                "verbose_name": "通知",
                "verbose_name_plural": "通知",
                "ordering": ["-created_at"],
                "indexes": [
                    models.Index(
                        fields=["user", "is_read"],
                        name="notificatio_user_id_427e4b_idx",
                    ),
                    models.Index(
                        fields=["type", "created_at"],
                        name="notificatio_type_8e213d_idx",
                    ),
                ],
            },
        ),
    ]
