"""
Azure语音服务集成
"""
import logging
import tempfile
import os
from typing import Dict, Any, Optional
from django.core.files.storage import default_storage
from apps.config.services import api_config

logger = logging.getLogger(__name__)

try:
    import azure.cognitiveservices.speech as speechsdk
    AZURE_AVAILABLE = True
except ImportError:
    AZURE_AVAILABLE = False
    logger.warning("Azure Speech SDK未安装，语音服务将不可用")


class AzureSpeechService:
    """Azure语音服务"""
    
    def __init__(self):
        self.speech_config = None
        self._initialize_service()
    
    def _initialize_service(self):
        """初始化Azure语音服务"""
        if not AZURE_AVAILABLE:
            return
        
        try:
            config = api_config.get_azure_speech_config()
            
            if not config['api_key'] or not config['region']:
                logger.warning("Azure语音服务配置不完整")
                return
            
            self.speech_config = speechsdk.SpeechConfig(
                subscription=config['api_key'],
                region=config['region']
            )
            
            # 设置默认语音
            self.speech_config.speech_synthesis_voice_name = "zh-CN-XiaoxiaoNeural"
            
            logger.info("Azure语音服务初始化成功")
            
        except Exception as e:
            logger.error(f"Azure语音服务初始化失败: {e}")
            self.speech_config = None
    
    def is_available(self) -> bool:
        """检查服务是否可用"""
        return (
            AZURE_AVAILABLE and 
            self.speech_config is not None and 
            api_config.validate_azure_speech_config()
        )
    
    def text_to_speech(
        self,
        text: str,
        voice_name: str = "zh-CN-XiaoxiaoNeural",
        output_format: str = "mp3",
        speech_rate: str = "0%",
        speech_pitch: str = "0%",
        speech_volume: str = "0%"
    ) -> Dict[str, Any]:
        """
        文本转语音

        Args:
            text: 要转换的文本
            voice_name: 语音名称
            output_format: 输出格式 (mp3, wav, ogg)
            speech_rate: 语音速度 (-50% to +100%)
            speech_pitch: 语音音调 (-50% to +50%)
            speech_volume: 语音音量 (-50% to +50%)

        Returns:
            转换结果
        """
        if not self.is_available():
            return {
                'success': False,
                'error': 'Azure语音服务不可用，请检查配置'
            }
        
        try:
            # 设置语音配置
            self.speech_config.speech_synthesis_voice_name = voice_name
            
            # 设置输出格式
            if output_format == "mp3":
                self.speech_config.set_speech_synthesis_output_format(
                    speechsdk.SpeechSynthesisOutputFormat.Audio16Khz32KBitRateMonoMp3
                )
            elif output_format == "wav":
                self.speech_config.set_speech_synthesis_output_format(
                    speechsdk.SpeechSynthesisOutputFormat.Audio16Khz16BitMonoPcm
                )
            
            # 创建临时文件
            with tempfile.NamedTemporaryFile(delete=False, suffix=f'.{output_format}') as temp_file:
                temp_file_path = temp_file.name
            
            # 配置音频输出
            audio_config = speechsdk.audio.AudioOutputConfig(filename=temp_file_path)
            
            # 创建语音合成器
            synthesizer = speechsdk.SpeechSynthesizer(
                speech_config=self.speech_config,
                audio_config=audio_config
            )
            
            # 构建SSML（如果需要调整语速和音调）
            if speech_rate != "0%" or speech_pitch != "0%":
                ssml_text = f"""
                <speak version="1.0" xmlns="http://www.w3.org/2001/10/synthesis" xml:lang="zh-CN">
                    <voice name="{voice_name}">
                        <prosody rate="{speech_rate}" pitch="{speech_pitch}">
                            {text}
                        </prosody>
                    </voice>
                </speak>
                """
                result = synthesizer.speak_ssml_async(ssml_text).get()
            else:
                result = synthesizer.speak_text_async(text).get()
            
            # 检查结果
            if result.reason == speechsdk.ResultReason.SynthesizingAudioCompleted:
                # 上传到存储
                import time
                file_name = f"speech/{voice_name}_{hash(text)}_{int(time.time())}.{output_format}"
                
                with open(temp_file_path, 'rb') as f:
                    file_path = default_storage.save(file_name, f)
                
                # 清理临时文件
                os.unlink(temp_file_path)
                
                return {
                    'success': True,
                    'audio_url': default_storage.url(file_path),
                    'file_path': file_path,
                    'voice_name': voice_name,
                    'text': text,
                    'duration': self._estimate_duration(text)
                }
            
            elif result.reason == speechsdk.ResultReason.Canceled:
                cancellation_details = speechsdk.CancellationDetails(result)
                error_msg = f"语音合成被取消: {cancellation_details.reason}"
                if cancellation_details.error_details:
                    error_msg += f" - {cancellation_details.error_details}"
                
                # 清理临时文件
                if os.path.exists(temp_file_path):
                    os.unlink(temp_file_path)
                
                return {
                    'success': False,
                    'error': error_msg
                }
            
        except Exception as e:
            logger.error(f"Azure语音合成失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_available_voices(self, language: str = "zh-CN") -> Dict[str, Any]:
        """
        获取可用的语音列表
        
        Args:
            language: 语言代码
        
        Returns:
            语音列表
        """
        if not self.is_available():
            return {'success': False, 'error': 'Azure语音服务不可用'}
        
        try:
            synthesizer = speechsdk.SpeechSynthesizer(speech_config=self.speech_config)
            result = synthesizer.get_voices_async(language).get()
            
            if result.reason == speechsdk.ResultReason.VoicesListRetrieved:
                voices = []
                for voice in result.voices:
                    voices.append({
                        'name': voice.name,
                        'display_name': voice.display_name,
                        'gender': voice.gender.name,
                        'language': voice.locale,
                        'style_list': voice.style_list
                    })
                
                return {
                    'success': True,
                    'voices': voices
                }
            else:
                return {
                    'success': False,
                    'error': '获取语音列表失败'
                }
                
        except Exception as e:
            logger.error(f"获取Azure语音列表失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _estimate_duration(self, text: str) -> int:
        """估算音频时长（秒）"""
        # 按照平均语速估算：每分钟约150-200字
        char_count = len(text)
        duration = max(char_count / 3, 1)  # 最少1秒
        return int(duration)


# 创建全局实例
azure_speech_service = AzureSpeechService()


def get_azure_speech_service() -> AzureSpeechService:
    """获取Azure语音服务实例"""
    return azure_speech_service


# 便捷函数
def synthesize_speech(
    text: str, 
    voice_name: str = "zh-CN-XiaoxiaoNeural",
    output_format: str = "mp3"
) -> Dict[str, Any]:
    """语音合成便捷函数"""
    return azure_speech_service.text_to_speech(text, voice_name, output_format)


def get_chinese_voices() -> Dict[str, Any]:
    """获取中文语音列表"""
    return azure_speech_service.get_available_voices("zh-CN")
