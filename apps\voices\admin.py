"""
语音系统管理后台配置
"""
from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from django.utils.html import format_html
from unfold.admin import ModelAdmin, TabularInline
from unfold.contrib.filters.admin import RangeDateFilter, ChoicesDropdownFilter

from .models import VoiceModel, VoiceCategory, VoiceTag, VoiceCloneRequest, VoiceSynthesisTask


@admin.register(VoiceModel)
class VoiceModelAdmin(ModelAdmin):
    """语音模型管理"""
    
    list_display = (
        'name', 'language', 'gender', 'voice_type', 'is_cloned',
        'is_public', 'is_active', 'computing_power_cost', 'usage_count', 'created_at'
    )
    
    list_filter = (
        ('language', ChoicesDropdownFilter),
        ('gender', ChoicesDropdownFilter),
        ('voice_type', ChoicesDropdownFilter),
        'is_cloned', 'is_public', 'is_active',
        ('created_at', RangeDateFilter),
    )
    
    search_fields = ('name', 'description', 'owner__username')
    
    readonly_fields = ('usage_count', 'created_at', 'updated_at', 'audio_preview')
    
    fieldsets = (
        (_('基本信息'), {
            'fields': ('name', 'description', 'language', 'gender', 'voice_type')
        }),
        (_('音频文件'), {
            'fields': ('audio_sample', 'audio_sample_url', 'audio_preview')
        }),
        (_('设置'), {
            'fields': ('is_cloned', 'is_public', 'is_active', 'owner', 'computing_power_cost', 'sort_order')
        }),
        (_('分类和标签'), {
            'fields': ('categories', 'tags'),
            'classes': ('collapse',)
        }),
        (_('默认参数'), {
            'fields': ('default_volume', 'default_pitch', 'default_speed'),
            'classes': ('collapse',)
        }),
        (_('统计信息'), {
            'fields': ('usage_count', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    filter_horizontal = ('categories', 'tags')

    ordering = ('sort_order', '-created_at')

    actions = ['activate_voices', 'deactivate_voices', 'make_public', 'make_private']
    
    def audio_preview(self, obj):
        """音频预览"""
        if obj.audio_sample:
            return format_html(
                '<audio controls style="width: 300px;"><source src="{}" type="audio/mpeg"></audio>',
                obj.audio_sample.url
            )
        elif obj.audio_sample_url:
            return format_html(
                '<audio controls style="width: 300px;"><source src="{}" type="audio/mpeg"></audio>',
                obj.audio_sample_url
            )
        return _('无音频')
    audio_preview.short_description = _('音频预览')
    
    def activate_voices(self, request, queryset):
        """批量启用语音模型"""
        updated = queryset.update(is_active=True)
        self.message_user(request, f'成功启用 {updated} 个语音模型')
    activate_voices.short_description = _('启用选中的语音模型')
    
    def deactivate_voices(self, request, queryset):
        """批量停用语音模型"""
        updated = queryset.update(is_active=False)
        self.message_user(request, f'成功停用 {updated} 个语音模型')
    deactivate_voices.short_description = _('停用选中的语音模型')
    
    def make_public(self, request, queryset):
        """设为公共语音"""
        updated = queryset.update(is_public=True, owner=None)
        self.message_user(request, f'成功将 {updated} 个语音模型设为公共')
    make_public.short_description = _('设为公共语音')
    
    def make_private(self, request, queryset):
        """设为私有语音"""
        updated = queryset.update(is_public=False)
        self.message_user(request, f'成功将 {updated} 个语音模型设为私有')
    make_private.short_description = _('设为私有语音')


@admin.register(VoiceCloneRequest)
class VoiceCloneRequestAdmin(ModelAdmin):
    """语音克隆请求管理"""
    
    list_display = (
        'user', 'name', 'status', 'progress', 'audio_duration',
        'computing_power_consumed', 'created_at', 'completed_at'
    )
    
    list_filter = (
        ('status', ChoicesDropdownFilter),
        ('created_at', RangeDateFilter),
        ('completed_at', RangeDateFilter),
    )
    
    search_fields = ('user__username', 'user__phone', 'name', 'description')
    
    readonly_fields = ('created_at', 'updated_at', 'audio_preview')
    
    fieldsets = (
        (_('基本信息'), {
            'fields': ('user', 'name', 'description')
        }),
        (_('源音频'), {
            'fields': ('source_audio', 'audio_duration', 'audio_preview')
        }),
        (_('处理状态'), {
            'fields': ('status', 'progress', 'error_message')
        }),
        (_('结果'), {
            'fields': ('result_voice', 'computing_power_consumed')
        }),
        (_('时间信息'), {
            'fields': ('created_at', 'updated_at', 'completed_at'),
            'classes': ('collapse',)
        }),
    )
    
    ordering = ('-created_at',)
    
    actions = ['approve_requests', 'reject_requests']
    
    def audio_preview(self, obj):
        """音频预览"""
        if obj.source_audio:
            return format_html(
                '<audio controls style="width: 300px;"><source src="{}" type="audio/mpeg"></audio>',
                obj.source_audio.url
            )
        return _('无音频')
    audio_preview.short_description = _('源音频预览')
    
    def approve_requests(self, request, queryset):
        """批量通过请求"""
        updated = queryset.filter(status='pending').update(status='processing')
        self.message_user(request, f'成功通过 {updated} 个克隆请求')
    approve_requests.short_description = _('通过选中的克隆请求')
    
    def reject_requests(self, request, queryset):
        """批量拒绝请求"""
        updated = queryset.filter(status='pending').update(status='cancelled')
        self.message_user(request, f'成功拒绝 {updated} 个克隆请求')
    reject_requests.short_description = _('拒绝选中的克隆请求')


@admin.register(VoiceSynthesisTask)
class VoiceSynthesisTaskAdmin(ModelAdmin):
    """语音合成任务管理"""
    
    list_display = (
        'user', 'voice_model', 'status', 'progress', 'audio_duration',
        'computing_power_consumed', 'created_at', 'completed_at'
    )
    
    list_filter = (
        ('status', ChoicesDropdownFilter),
        'voice_model__language',
        ('created_at', RangeDateFilter),
        ('completed_at', RangeDateFilter),
    )
    
    search_fields = (
        'user__username', 'user__phone', 'voice_model__name', 'text_content'
    )
    
    readonly_fields = ('created_at', 'updated_at', 'audio_preview', 'text_preview')
    
    fieldsets = (
        (_('基本信息'), {
            'fields': ('user', 'voice_model', 'text_preview')
        }),
        (_('语音参数'), {
            'fields': ('volume', 'pitch', 'speed')
        }),
        (_('处理状态'), {
            'fields': ('status', 'progress', 'error_message')
        }),
        (_('结果'), {
            'fields': ('result_audio', 'result_audio_url', 'audio_duration', 'audio_preview', 'computing_power_consumed')
        }),
        (_('时间信息'), {
            'fields': ('created_at', 'updated_at', 'completed_at'),
            'classes': ('collapse',)
        }),
    )
    
    ordering = ('-created_at',)
    
    def text_preview(self, obj):
        """文本预览"""
        if obj.text_content:
            preview = obj.text_content[:100]
            if len(obj.text_content) > 100:
                preview += '...'
            return format_html('<div style="max-width: 300px; word-wrap: break-word;">{}</div>', preview)
        return _('无文本')
    text_preview.short_description = _('文本内容')
    
    def audio_preview(self, obj):
        """音频预览"""
        if obj.result_audio:
            return format_html(
                '<audio controls style="width: 300px;"><source src="{}" type="audio/mpeg"></audio>',
                obj.result_audio.url
            )
        elif obj.result_audio_url:
            return format_html(
                '<audio controls style="width: 300px;"><source src="{}" type="audio/mpeg"></audio>',
                obj.result_audio_url
            )
        return _('无音频')
    audio_preview.short_description = _('合成音频')
    
    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False


@admin.register(VoiceCategory)
class VoiceCategoryAdmin(ModelAdmin):
    """语音分类管理"""

    list_display = ('name', 'icon', 'sort_order', 'is_active', 'voice_count', 'created_at')

    list_filter = ('is_active', ('created_at', RangeDateFilter))

    search_fields = ('name', 'description')

    readonly_fields = ('created_at', 'voice_count')

    fieldsets = (
        (_('基本信息'), {
            'fields': ('name', 'description', 'icon')
        }),
        (_('设置'), {
            'fields': ('sort_order', 'is_active')
        }),
        (_('统计信息'), {
            'fields': ('voice_count', 'created_at'),
            'classes': ('collapse',)
        }),
    )

    ordering = ('sort_order', 'name')

    def voice_count(self, obj):
        """关联的语音模型数量"""
        return obj.voices.count()
    voice_count.short_description = _('语音数量')


@admin.register(VoiceTag)
class VoiceTagAdmin(ModelAdmin):
    """语音标签管理"""

    list_display = ('name', 'color_preview', 'usage_count', 'voice_count', 'created_at')

    list_filter = (('created_at', RangeDateFilter),)

    search_fields = ('name',)

    readonly_fields = ('usage_count', 'created_at', 'voice_count')

    fieldsets = (
        (_('基本信息'), {
            'fields': ('name', 'color')
        }),
        (_('统计信息'), {
            'fields': ('usage_count', 'voice_count', 'created_at'),
            'classes': ('collapse',)
        }),
    )

    ordering = ('-usage_count', 'name')

    def color_preview(self, obj):
        """颜色预览"""
        return format_html(
            '<span style="display: inline-block; width: 20px; height: 20px; '
            'background-color: {}; border: 1px solid #ccc; border-radius: 3px;"></span>',
            obj.color
        )
    color_preview.short_description = _('颜色')

    def voice_count(self, obj):
        """关联的语音模型数量"""
        return obj.voices.count()
    voice_count.short_description = _('语音数量')
