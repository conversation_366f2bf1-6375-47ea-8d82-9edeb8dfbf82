"""
性能监控API
"""
import json
import logging
from datetime import datetime, timedelta
from django.utils import timezone
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from django.core.cache import cache
from .performance_monitor import GlobalPerformanceStats

logger = logging.getLogger(__name__)


class PerformanceMetricsView(APIView):
    """性能指标收集API"""
    
    permission_classes = []  # 允许前端上报
    
    def post(self, request):
        """接收性能指标数据"""
        try:
            metric_type = request.data.get('type')
            metric_data = request.data.get('data', {})
            client = request.data.get('client', 'unknown')
            
            # 记录性能指标
            self.record_metric(metric_type, metric_data, client)
            
            return Response({
                'success': True,
                'message': '性能指标已记录'
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"性能指标记录失败: {e}")
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_400_BAD_REQUEST)
    
    def record_metric(self, metric_type, data, client):
        """记录性能指标"""
        timestamp = timezone.now()
        
        # 构建指标记录
        metric_record = {
            'type': metric_type,
            'data': data,
            'client': client,
            'timestamp': timestamp.isoformat(),
            'user_agent': self.request.META.get('HTTP_USER_AGENT', ''),
            'ip': self.get_client_ip()
        }
        
        # 根据指标类型进行不同处理
        if metric_type == 'api_performance':
            self.handle_api_performance(data)
        elif metric_type == 'page_performance':
            self.handle_page_performance(data)
        elif metric_type == 'component_performance':
            self.handle_component_performance(data)
        elif metric_type == 'frontend_error':
            self.handle_frontend_error(data)
        
        # 存储到缓存（用于实时监控）
        cache_key = f"performance_metrics:{metric_type}:{timestamp.strftime('%Y%m%d%H')}"
        cached_metrics = cache.get(cache_key, [])
        cached_metrics.append(metric_record)
        
        # 只保留最近1000条记录
        if len(cached_metrics) > 1000:
            cached_metrics = cached_metrics[-1000:]
        
        cache.set(cache_key, cached_metrics, 3600)  # 1小时过期
        
        logger.debug(f"性能指标已记录: {metric_type}")
    
    def handle_api_performance(self, data):
        """处理API性能数据"""
        duration = data.get('duration', 0)
        success = data.get('success', True)
        
        # 更新全局统计
        GlobalPerformanceStats.record_api_call(duration, success)
        
        # 记录慢API
        if duration > 2000:  # 超过2秒
            logger.warning(f"慢API检测: {data.get('url')} - {duration}ms")
    
    def handle_page_performance(self, data):
        """处理页面性能数据"""
        load_time = data.get('loadTime', 0)
        
        if load_time > 5000:  # 超过5秒
            logger.warning(f"页面加载缓慢: {data.get('url')} - {load_time}ms")
    
    def handle_component_performance(self, data):
        """处理组件性能数据"""
        render_time = data.get('renderTime', 0)
        component = data.get('component', 'Unknown')
        
        if render_time > 1000:  # 超过1秒
            logger.warning(f"组件渲染缓慢: {component} - {render_time}ms")
    
    def handle_frontend_error(self, data):
        """处理前端错误"""
        error_message = data.get('message', '')
        component = data.get('context', {}).get('component', 'Unknown')
        
        logger.error(f"前端错误: {component} - {error_message}")
    
    def get_client_ip(self):
        """获取客户端IP"""
        x_forwarded_for = self.request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = self.request.META.get('REMOTE_ADDR')
        return ip


class PerformanceAnalyticsView(APIView):
    """性能分析API"""
    
    permission_classes = []  # 可以根据需要添加权限
    
    def get(self, request):
        """获取性能分析数据"""
        try:
            time_range = request.query_params.get('range', '1h')  # 1h, 24h, 7d
            metric_type = request.query_params.get('type', 'all')
            
            analytics_data = self.get_analytics_data(time_range, metric_type)
            
            return Response({
                'success': True,
                'data': analytics_data,
                'timestamp': timezone.now().isoformat()
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"性能分析获取失败: {e}")
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def get_analytics_data(self, time_range, metric_type):
        """获取分析数据"""
        now = timezone.now()
        
        # 计算时间范围
        if time_range == '1h':
            start_time = now - timedelta(hours=1)
            cache_pattern = f"performance_metrics:*:{now.strftime('%Y%m%d%H')}"
        elif time_range == '24h':
            start_time = now - timedelta(hours=24)
            cache_pattern = f"performance_metrics:*:{now.strftime('%Y%m%d')}*"
        else:  # 7d
            start_time = now - timedelta(days=7)
            cache_pattern = f"performance_metrics:*"
        
        # 从缓存获取数据
        all_metrics = []
        cache_keys = cache.keys(cache_pattern) if hasattr(cache, 'keys') else []
        
        for key in cache_keys:
            metrics = cache.get(key, [])
            all_metrics.extend(metrics)
        
        # 过滤时间范围
        filtered_metrics = [
            m for m in all_metrics 
            if datetime.fromisoformat(m['timestamp'].replace('Z', '+00:00')) >= start_time
        ]
        
        # 按类型过滤
        if metric_type != 'all':
            filtered_metrics = [m for m in filtered_metrics if m['type'] == metric_type]
        
        # 生成分析报告
        return self.generate_analytics_report(filtered_metrics)
    
    def generate_analytics_report(self, metrics):
        """生成分析报告"""
        if not metrics:
            return {
                'total_metrics': 0,
                'summary': {},
                'trends': {},
                'alerts': []
            }
        
        # 按类型分组
        metrics_by_type = {}
        for metric in metrics:
            metric_type = metric['type']
            if metric_type not in metrics_by_type:
                metrics_by_type[metric_type] = []
            metrics_by_type[metric_type].append(metric)
        
        # 生成摘要
        summary = {}
        alerts = []
        
        for metric_type, type_metrics in metrics_by_type.items():
            if metric_type == 'api_performance':
                durations = [m['data'].get('duration', 0) for m in type_metrics]
                avg_duration = sum(durations) / len(durations) if durations else 0
                max_duration = max(durations) if durations else 0
                error_count = sum(1 for m in type_metrics if not m['data'].get('success', True))
                
                summary[metric_type] = {
                    'total_requests': len(type_metrics),
                    'avg_duration': round(avg_duration, 2),
                    'max_duration': max_duration,
                    'error_count': error_count,
                    'error_rate': round(error_count / len(type_metrics) * 100, 2) if type_metrics else 0
                }
                
                # 生成告警
                if avg_duration > 1000:
                    alerts.append({
                        'type': 'warning',
                        'message': f'API平均响应时间过长: {avg_duration:.0f}ms'
                    })
                
                if error_count > len(type_metrics) * 0.05:  # 错误率超过5%
                    alerts.append({
                        'type': 'error',
                        'message': f'API错误率过高: {error_count / len(type_metrics) * 100:.1f}%'
                    })
            
            elif metric_type == 'page_performance':
                load_times = [m['data'].get('loadTime', 0) for m in type_metrics]
                avg_load_time = sum(load_times) / len(load_times) if load_times else 0
                
                summary[metric_type] = {
                    'total_page_loads': len(type_metrics),
                    'avg_load_time': round(avg_load_time, 2),
                    'max_load_time': max(load_times) if load_times else 0
                }
                
                if avg_load_time > 3000:
                    alerts.append({
                        'type': 'warning',
                        'message': f'页面加载时间过长: {avg_load_time:.0f}ms'
                    })
            
            elif metric_type == 'frontend_error':
                summary[metric_type] = {
                    'total_errors': len(type_metrics),
                    'error_types': list(set(m['data'].get('message', '') for m in type_metrics))
                }
                
                if len(type_metrics) > 10:  # 错误数量过多
                    alerts.append({
                        'type': 'error',
                        'message': f'前端错误数量过多: {len(type_metrics)}个'
                    })
        
        return {
            'total_metrics': len(metrics),
            'summary': summary,
            'alerts': alerts,
            'global_stats': GlobalPerformanceStats.get_stats()
        }


class PerformanceRealtimeView(APIView):
    """实时性能监控API"""
    
    permission_classes = []
    
    def get(self, request):
        """获取实时性能数据"""
        try:
            # 获取最近5分钟的数据
            now = timezone.now()
            cache_key = f"performance_metrics:*:{now.strftime('%Y%m%d%H')}"
            
            recent_metrics = []
            cache_keys = cache.keys(cache_key) if hasattr(cache, 'keys') else []
            
            for key in cache_keys:
                metrics = cache.get(key, [])
                # 只取最近5分钟的数据
                recent_metrics.extend([
                    m for m in metrics 
                    if datetime.fromisoformat(m['timestamp'].replace('Z', '+00:00')) >= now - timedelta(minutes=5)
                ])
            
            # 实时统计
            realtime_stats = {
                'current_time': now.isoformat(),
                'active_users': len(set(m.get('ip') for m in recent_metrics)),
                'requests_per_minute': len([m for m in recent_metrics if m['type'] == 'api_performance']),
                'avg_response_time': self.calculate_avg_response_time(recent_metrics),
                'error_count': len([m for m in recent_metrics if m['type'] == 'frontend_error']),
                'global_stats': GlobalPerformanceStats.get_stats()
            }
            
            return Response({
                'success': True,
                'data': realtime_stats
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"实时性能数据获取失败: {e}")
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def calculate_avg_response_time(self, metrics):
        """计算平均响应时间"""
        api_metrics = [m for m in metrics if m['type'] == 'api_performance']
        if not api_metrics:
            return 0
        
        durations = [m['data'].get('duration', 0) for m in api_metrics]
        return round(sum(durations) / len(durations), 2) if durations else 0
