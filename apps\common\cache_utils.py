"""
缓存工具类
"""
from django.core.cache import cache
from django.conf import settings
import hashlib
import json
from functools import wraps
import logging

logger = logging.getLogger(__name__)


class CacheManager:
    """缓存管理器"""
    
    # 缓存键前缀
    PREFIX = 'ai_digital_human'
    
    # 缓存时间配置 (秒)
    CACHE_TIMES = {
        'user_info': 300,           # 用户信息 5分钟
        'voice_models': 1800,       # 语音模型 30分钟
        'avatars': 1800,            # 数字人形象 30分钟
        'video_templates': 3600,    # 视频模板 1小时
        'system_config': 600,       # 系统配置 10分钟
        'ai_response': 86400,       # AI响应 24小时
    }
    
    @classmethod
    def make_key(cls, category: str, identifier: str) -> str:
        """生成缓存键"""
        return f"{cls.PREFIX}:{category}:{identifier}"
    
    @classmethod
    def hash_key(cls, data: dict) -> str:
        """生成数据哈希键"""
        json_str = json.dumps(data, sort_keys=True)
        return hashlib.md5(json_str.encode()).hexdigest()
    
    @classmethod
    def get(cls, category: str, identifier: str, default=None):
        """获取缓存"""
        key = cls.make_key(category, identifier)
        try:
            return cache.get(key, default)
        except Exception as e:
            logger.error(f"缓存获取失败: {key} - {e}")
            return default
    
    @classmethod
    def set(cls, category: str, identifier: str, value, timeout=None):
        """设置缓存"""
        key = cls.make_key(category, identifier)
        if timeout is None:
            timeout = cls.CACHE_TIMES.get(category, 300)
        
        try:
            cache.set(key, value, timeout)
            logger.debug(f"缓存设置成功: {key}")
        except Exception as e:
            logger.error(f"缓存设置失败: {key} - {e}")
    
    @classmethod
    def delete(cls, category: str, identifier: str):
        """删除缓存"""
        key = cls.make_key(category, identifier)
        try:
            cache.delete(key)
            logger.debug(f"缓存删除成功: {key}")
        except Exception as e:
            logger.error(f"缓存删除失败: {key} - {e}")
    
    @classmethod
    def clear_category(cls, category: str):
        """清除分类缓存"""
        try:
            # 这里需要根据缓存后端实现
            # Redis可以使用模式匹配删除
            pattern = f"{cls.PREFIX}:{category}:*"
            logger.info(f"清除分类缓存: {pattern}")
        except Exception as e:
            logger.error(f"清除分类缓存失败: {category} - {e}")


def cache_result(category: str, timeout=None, key_func=None):
    """缓存装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 生成缓存键
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                # 使用函数名和参数生成键
                key_data = {
                    'func': func.__name__,
                    'args': str(args),
                    'kwargs': str(kwargs)
                }
                cache_key = CacheManager.hash_key(key_data)
            
            # 尝试从缓存获取
            cached_result = CacheManager.get(category, cache_key)
            if cached_result is not None:
                logger.debug(f"缓存命中: {category}:{cache_key}")
                return cached_result
            
            # 执行函数并缓存结果
            result = func(*args, **kwargs)
            CacheManager.set(category, cache_key, result, timeout)
            logger.debug(f"缓存更新: {category}:{cache_key}")
            
            return result
        return wrapper
    return decorator


def invalidate_cache(category: str, identifier: str = None):
    """缓存失效装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            result = func(*args, **kwargs)
            
            if identifier:
                CacheManager.delete(category, identifier)
            else:
                CacheManager.clear_category(category)
            
            return result
        return wrapper
    return decorator


# 常用缓存函数
def cache_user_info(user_id: int, data: dict):
    """缓存用户信息"""
    CacheManager.set('user_info', str(user_id), data)


def get_cached_user_info(user_id: int):
    """获取缓存的用户信息"""
    return CacheManager.get('user_info', str(user_id))


def cache_voice_models(data: list):
    """缓存语音模型列表"""
    CacheManager.set('voice_models', 'list', data)


def get_cached_voice_models():
    """获取缓存的语音模型列表"""
    return CacheManager.get('voice_models', 'list')


def cache_avatars(data: list):
    """缓存数字人形象列表"""
    CacheManager.set('avatars', 'list', data)


def get_cached_avatars():
    """获取缓存的数字人形象列表"""
    return CacheManager.get('avatars', 'list')


def cache_ai_response(prompt_hash: str, response: dict):
    """缓存AI响应"""
    CacheManager.set('ai_response', prompt_hash, response)


def get_cached_ai_response(prompt_hash: str):
    """获取缓存的AI响应"""
    return CacheManager.get('ai_response', prompt_hash)
