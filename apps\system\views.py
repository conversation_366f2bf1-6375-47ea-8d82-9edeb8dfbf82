"""
系统监控和统计视图
提供百度曦灵数字人服务的监控面板
"""

from django.shortcuts import render
from django.http import JsonResponse
from django.contrib.admin.views.decorators import staff_member_required
from django.utils.decorators import method_decorator
from django.views.generic import TemplateView
from django.db.models import Count, Q
from datetime import datetime, timedelta
import json

from apps.videos.services.baidu_xiling_logger import baidu_logger
from apps.tasks.models import TaskQueue
from apps.avatars.models import DigitalAvatar
from apps.videos.models import VideoGeneration


@method_decorator(staff_member_required, name='dispatch')
class SystemMonitorView(TemplateView):
    """系统监控面板"""
    template_name = 'system/monitor.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # 获取基本统计信息
        context.update({
            'total_avatars': DigitalAvatar.objects.count(),
            'total_videos': VideoGeneration.objects.count(),
            'active_tasks': TaskQueue.objects.filter(status='running').count(),
            'failed_tasks': TaskQueue.objects.filter(status='failed').count(),
        })
        
        return context


@staff_member_required
def api_stats(request):
    """API统计信息"""
    try:
        # 获取百度曦灵API统计
        stats = baidu_logger.get_stats()
        
        # 获取任务统计
        task_stats = get_task_stats()
        
        # 获取用户统计
        user_stats = get_user_stats()
        
        # 获取成本统计
        cost_stats = get_cost_stats()
        
        return JsonResponse({
            'success': True,
            'data': {
                'api_stats': stats,
                'task_stats': task_stats,
                'user_stats': user_stats,
                'cost_stats': cost_stats,
                'last_updated': datetime.now().isoformat()
            }
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })


def get_task_stats():
    """获取任务统计"""
    now = datetime.now()
    today = now.date()
    week_ago = today - timedelta(days=7)
    
    return {
        'total_tasks': TaskQueue.objects.count(),
        'completed_tasks': TaskQueue.objects.filter(status='completed').count(),
        'failed_tasks': TaskQueue.objects.filter(status='failed').count(),
        'running_tasks': TaskQueue.objects.filter(status='running').count(),
        'today_tasks': TaskQueue.objects.filter(created_at__date=today).count(),
        'week_tasks': TaskQueue.objects.filter(created_at__date__gte=week_ago).count(),
        'task_types': list(
            TaskQueue.objects.values('task_type')
            .annotate(count=Count('id'))
            .order_by('-count')
        ),
        'success_rate': calculate_success_rate()
    }


def get_user_stats():
    """获取用户统计"""
    from django.contrib.auth import get_user_model
    User = get_user_model()
    
    now = datetime.now()
    today = now.date()
    week_ago = today - timedelta(days=7)
    
    return {
        'total_users': User.objects.count(),
        'active_users': User.objects.filter(last_login__date=today).count(),
        'new_users_week': User.objects.filter(date_joined__date__gte=week_ago).count(),
        'users_with_avatars': User.objects.filter(digitalavatar__isnull=False).distinct().count(),
        'users_with_videos': User.objects.filter(videogeneration__isnull=False).distinct().count(),
    }


def get_cost_stats():
    """获取成本统计"""
    # 计算本月成本
    now = datetime.now()
    month_start = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
    
    # 数字人创建成本
    avatar_cost = DigitalAvatar.objects.filter(
        created_at__gte=month_start
    ).aggregate(
        total_cost=Count('id')
    )['total_cost'] * 15  # 假设每个数字人成本15元
    
    # 视频生成成本
    video_cost = VideoGeneration.objects.filter(
        created_at__gte=month_start,
        status='completed'
    ).aggregate(
        total_cost=Count('id')
    )['total_cost'] * 5  # 假设每个视频成本5元
    
    return {
        'monthly_avatar_cost': avatar_cost,
        'monthly_video_cost': video_cost,
        'total_monthly_cost': avatar_cost + video_cost,
        'estimated_heygen_cost': (avatar_cost + video_cost) * 4,  # HeyGen成本约4倍
        'cost_savings': (avatar_cost + video_cost) * 3,  # 节省的成本
        'savings_percentage': 75
    }


def calculate_success_rate():
    """计算任务成功率"""
    total_tasks = TaskQueue.objects.filter(
        status__in=['completed', 'failed']
    ).count()
    
    if total_tasks == 0:
        return 100
    
    completed_tasks = TaskQueue.objects.filter(status='completed').count()
    return round((completed_tasks / total_tasks) * 100, 2)


@staff_member_required
def system_health(request):
    """系统健康检查"""
    try:
        health_data = {
            'timestamp': datetime.now().isoformat(),
            'status': 'healthy',
            'checks': {}
        }
        
        # 检查数据库连接
        try:
            TaskQueue.objects.count()
            health_data['checks']['database'] = {'status': 'ok', 'message': '数据库连接正常'}
        except Exception as e:
            health_data['checks']['database'] = {'status': 'error', 'message': str(e)}
            health_data['status'] = 'unhealthy'
        
        # 检查Redis连接
        try:
            from django.core.cache import cache
            cache.set('health_check', 'ok', 10)
            if cache.get('health_check') == 'ok':
                health_data['checks']['redis'] = {'status': 'ok', 'message': 'Redis连接正常'}
            else:
                health_data['checks']['redis'] = {'status': 'error', 'message': 'Redis读写异常'}
                health_data['status'] = 'unhealthy'
        except Exception as e:
            health_data['checks']['redis'] = {'status': 'error', 'message': str(e)}
            health_data['status'] = 'unhealthy'
        
        # 检查百度曦灵API
        try:
            from apps.videos.services.digital_human_service import digital_human_service
            xiling_service = digital_human_service.baidu_xiling
            if xiling_service.is_available():
                health_data['checks']['baidu_xiling'] = {'status': 'ok', 'message': '百度曦灵API可用'}
            else:
                health_data['checks']['baidu_xiling'] = {'status': 'warning', 'message': 'API密钥未配置'}
        except Exception as e:
            health_data['checks']['baidu_xiling'] = {'status': 'error', 'message': str(e)}
        
        # 检查Celery任务队列
        try:
            running_tasks = TaskQueue.objects.filter(status='running').count()
            if running_tasks < 10:  # 假设正常情况下运行任务不超过10个
                health_data['checks']['celery'] = {'status': 'ok', 'message': f'任务队列正常，{running_tasks}个运行中任务'}
            else:
                health_data['checks']['celery'] = {'status': 'warning', 'message': f'任务队列繁忙，{running_tasks}个运行中任务'}
        except Exception as e:
            health_data['checks']['celery'] = {'status': 'error', 'message': str(e)}
            health_data['status'] = 'unhealthy'
        
        return JsonResponse({
            'success': True,
            'data': health_data
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e),
            'status': 'unhealthy'
        })


@staff_member_required
def performance_metrics(request):
    """性能指标"""
    try:
        # 获取最近24小时的任务统计
        now = datetime.now()
        hours_24_ago = now - timedelta(hours=24)
        
        # 按小时统计任务数量
        hourly_stats = []
        for i in range(24):
            hour_start = hours_24_ago + timedelta(hours=i)
            hour_end = hour_start + timedelta(hours=1)
            
            task_count = TaskQueue.objects.filter(
                created_at__gte=hour_start,
                created_at__lt=hour_end
            ).count()
            
            completed_count = TaskQueue.objects.filter(
                created_at__gte=hour_start,
                created_at__lt=hour_end,
                status='completed'
            ).count()
            
            hourly_stats.append({
                'hour': hour_start.strftime('%H:00'),
                'total_tasks': task_count,
                'completed_tasks': completed_count,
                'success_rate': round((completed_count / task_count * 100), 2) if task_count > 0 else 0
            })
        
        # 获取平均处理时间
        recent_tasks = TaskQueue.objects.filter(
            status='completed',
            created_at__gte=hours_24_ago
        ).values('created_at', 'updated_at')
        
        avg_processing_time = 0
        if recent_tasks:
            total_time = sum([
                (task['updated_at'] - task['created_at']).total_seconds()
                for task in recent_tasks
            ])
            avg_processing_time = round(total_time / len(recent_tasks), 2)
        
        return JsonResponse({
            'success': True,
            'data': {
                'hourly_stats': hourly_stats,
                'avg_processing_time': avg_processing_time,
                'total_tasks_24h': sum([stat['total_tasks'] for stat in hourly_stats]),
                'total_completed_24h': sum([stat['completed_tasks'] for stat in hourly_stats]),
                'overall_success_rate_24h': calculate_success_rate()
            }
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })
