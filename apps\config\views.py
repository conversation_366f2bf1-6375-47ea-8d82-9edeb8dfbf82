"""
配置管理视图
"""
from rest_framework import viewsets, permissions, status
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.decorators import action
from django.contrib.auth import get_user_model
from .models import SystemConfig
from .serializers import SystemConfigSerializer
from .services import api_config

User = get_user_model()


class APIConfigViewSet(viewsets.ModelViewSet):
    """API配置管理"""
    queryset = SystemConfig.objects.all()
    serializer_class = SystemConfigSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """根据用户权限过滤配置"""
        if self.request.user.is_superuser:
            return SystemConfig.objects.all()
        else:
            # 普通用户只能看到非敏感的配置
            return SystemConfig.objects.filter(is_sensitive=False)
    
    @action(detail=False, methods=['get'])
    def public_configs(self, request):
        """获取公开配置"""
        configs = SystemConfig.objects.filter(is_sensitive=False)
        serializer = self.get_serializer(configs, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def ai_service_configs(self, request):
        """获取AI服务配置"""
        configs = {
            'openai': api_config.get_openai_config(),
            'azure_speech': api_config.get_azure_speech_config(),
            'elevenlabs': api_config.get_elevenlabs_config(),
        }
        return Response(configs)


class SystemConfigView(APIView):
    """系统配置视图"""
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request):
        """获取系统配置"""
        config = {
            'platform_name': api_config.get_config('platform_name', 'AI数字人SaaS平台'),
            'platform_version': api_config.get_config('platform_version', '1.0.0'),
            'max_file_size': api_config.get_config('max_file_size', 100),
            'supported_formats': {
                'video': ['mp4', 'mov', 'avi'],
                'audio': ['mp3', 'wav', 'aac'],
                'image': ['jpg', 'jpeg', 'png', 'gif']
            },
            'features': {
                'voice_synthesis': True,
                'avatar_creation': True,
                'video_generation': True,
                'ai_tools': True
            }
        }
        return Response(config)
