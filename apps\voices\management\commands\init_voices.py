"""
语音模型数据初始化命令
基于原项目 https://hm.umi6.com/ 的真实数据
"""

from django.core.management.base import BaseCommand
from django.db import transaction
from apps.voices.models import VoiceModel


class Command(BaseCommand):
    help = '初始化语音模型数据'

    def add_arguments(self, parser):
        parser.add_argument(
            '--clear',
            action='store_true',
            help='清除现有数据后重新初始化',
        )

    def handle(self, *args, **options):
        if options['clear']:
            self.stdout.write('清除现有语音模型数据...')
            VoiceModel.objects.all().delete()

        with transaction.atomic():
            self.create_voice_models()

        self.stdout.write(
            self.style.SUCCESS('语音模型数据初始化完成！')
        )

    def create_voice_models(self):
        """创建语音模型数据 - 基于原项目的完整数据"""
        voice_models_data = [
            # 英语语音
            {
                'name': 'Ethan',
                'description': '专业的英语男声，适合商务和正式场合',
                'language': 'en',
                'gender': 'male',
                'voice_type': '英语男声',
                'is_public': True,
                'is_active': True,
                'sort_order': 1,
                'tags': ['英语', '男声', '商务']
            },
            {
                'name': 'Lam',
                'description': '清晰的英语男声，适合教育和培训',
                'language': 'en',
                'gender': 'male',
                'voice_type': '英语男声',
                'is_public': True,
                'is_active': True,
                'sort_order': 2,
                'tags': ['英语', '男声', '教育']
            },
            {
                'name': 'Alayna',
                'description': '温柔的英语女声，适合客服和咨询',
                'language': 'en',
                'gender': 'female',
                'voice_type': '英语女声',
                'is_public': True,
                'is_active': True,
                'sort_order': 3,
                'tags': ['英语', '女声', '温柔']
            },
            {
                'name': 'Miles',
                'description': '活力的英语男声，适合娱乐和广告',
                'language': 'en',
                'gender': 'male',
                'voice_type': '英语男声',
                'is_public': True,
                'is_active': True,
                'sort_order': 4,
                'tags': ['英语', '男声', '活力']
            },
            {
                'name': 'Byron',
                'description': '磁性的英语男声，适合播报和解说',
                'language': 'en',
                'gender': 'male',
                'voice_type': '英语男声',
                'is_public': True,
                'is_active': True,
                'sort_order': 5,
                'tags': ['英语', '男声', '磁性']
            },
            {
                'name': 'Mary',
                'description': '甜美的英语女声，适合教育和儿童内容',
                'language': 'en',
                'gender': 'female',
                'voice_type': '英语女声',
                'is_public': True,
                'is_active': True,
                'sort_order': 6,
                'tags': ['英语', '女声', '甜美']
            },
            {
                'name': 'Alfie',
                'description': '年轻的英语男声，适合时尚和潮流内容',
                'language': 'en',
                'gender': 'male',
                'voice_type': '英语男声',
                'is_public': True,
                'is_active': True,
                'sort_order': 7,
                'tags': ['英语', '男声', '年轻']
            },
            {
                'name': 'Mia',
                'description': '优雅的英语女声，适合高端品牌和奢侈品',
                'language': 'en',
                'gender': 'female',
                'voice_type': '英语女声',
                'is_public': True,
                'is_active': True,
                'sort_order': 8,
                'tags': ['英语', '女声', '优雅']
            },
            {
                'name': 'Lizzie',
                'description': '亲切的英语女声，适合生活和健康内容',
                'language': 'en',
                'gender': 'female',
                'voice_type': '英语女声',
                'is_public': True,
                'is_active': True,
                'sort_order': 9,
                'tags': ['英语', '女声', '亲切']
            },
            {
                'name': 'Robert',
                'description': '权威的英语男声，适合新闻和政务',
                'language': 'en',
                'gender': 'male',
                'voice_type': '英语男声',
                'is_public': True,
                'is_active': True,
                'sort_order': 10,
                'tags': ['英语', '男声', '权威']
            },
            # 中文语音
            {
                'name': '希希',
                'description': '通用主播声音，适合各种场景',
                'language': 'zh',
                'gender': 'female',
                'voice_type': '通用主播',
                'is_public': True,
                'is_active': True,
                'sort_order': 11,
                'tags': ['中文', '女声', '主播']
            },
            {
                'name': '小龙',
                'description': '新闻男声，适合新闻播报',
                'language': 'zh',
                'gender': 'male',
                'voice_type': '新闻男声',
                'is_public': True,
                'is_active': True,
                'sort_order': 12,
                'tags': ['中文', '男声', '新闻']
            },
            {
                'name': '大鹏',
                'description': '磁性男声，适合商务和广告',
                'language': 'zh',
                'gender': 'male',
                'voice_type': '磁性男声',
                'is_public': True,
                'is_active': True,
                'sort_order': 13,
                'tags': ['中文', '男声', '磁性']
            },
            {
                'name': '小豪',
                'description': '通用主播声音，适合多种内容',
                'language': 'zh',
                'gender': 'male',
                'voice_type': '通用主播',
                'is_public': True,
                'is_active': True,
                'sort_order': 14,
                'tags': ['中文', '男声', '主播']
            },
            {
                'name': '小梦',
                'description': '通用主播女声，适合多种场景',
                'language': 'zh',
                'gender': 'female',
                'voice_type': '通用主播',
                'is_public': True,
                'is_active': True,
                'sort_order': 15,
                'tags': ['中文', '女声', '主播']
            },
            {
                'name': '小雨',
                'description': '故事解说声音，适合故事和解说',
                'language': 'zh',
                'gender': 'female',
                'voice_type': '故事解说',
                'is_public': True,
                'is_active': True,
                'sort_order': 16,
                'tags': ['中文', '女声', '故事']
            },
            {
                'name': '静静',
                'description': '科普讲解声音，适合教育和科普',
                'language': 'zh',
                'gender': 'female',
                'voice_type': '科普讲解',
                'is_public': True,
                'is_active': True,
                'sort_order': 17,
                'tags': ['中文', '女声', '科普']
            },
            {
                'name': '阿泽',
                'description': '音频播报声音，适合播报和通知',
                'language': 'zh',
                'gender': 'male',
                'voice_type': '音频播报',
                'is_public': True,
                'is_active': True,
                'sort_order': 18,
                'tags': ['中文', '男声', '播报']
            },
            {
                'name': '潇潇',
                'description': '通用女声，适合各种场景',
                'language': 'zh',
                'gender': 'female',
                'voice_type': '通用女声',
                'is_public': True,
                'is_active': True,
                'sort_order': 19,
                'tags': ['中文', '女声', '通用']
            },
            {
                'name': '通用女声',
                'description': '标准的通用女声',
                'language': 'zh',
                'gender': 'female',
                'voice_type': '通用场景',
                'is_public': True,
                'is_active': True,
                'sort_order': 20,
                'tags': ['中文', '女声', '通用']
            }
        ]

        for voice_data in voice_models_data:
            # 提取标签
            tags = voice_data.pop('tags', [])
            
            # 创建语音模型
            voice_model, created = VoiceModel.objects.get_or_create(
                name=voice_data['name'],
                defaults=voice_data
            )
            
            if created:
                self.stdout.write(f'创建语音模型: {voice_model.name}')
                
                # 设置标签
                if tags:
                    voice_model.tags = tags
                    voice_model.save()
