"""
运行任务执行器管理命令
"""
import signal
import sys
from django.core.management.base import BaseCommand
from apps.tasks.executor import task_executor


class Command(BaseCommand):
    help = '运行异步任务执行器'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--workers',
            type=int,
            default=4,
            help='工作进程数量'
        )
        parser.add_argument(
            '--worker-id',
            type=str,
            help='工作进程ID'
        )
    
    def handle(self, *args, **options):
        workers = options['workers']
        worker_id = options.get('worker_id')
        
        self.stdout.write(
            self.style.SUCCESS(f'启动任务执行器，工作进程数: {workers}')
        )
        
        # 设置任务执行器参数
        task_executor.max_workers = workers
        if worker_id:
            task_executor.worker_id = worker_id
        
        # 设置信号处理器
        def signal_handler(signum, frame):
            self.stdout.write(
                self.style.WARNING('收到停止信号，正在关闭任务执行器...')
            )
            task_executor.stop()
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        try:
            # 启动任务执行器
            task_executor.start()
            
            # 保持运行
            while task_executor.running:
                import time
                time.sleep(1)
                
        except KeyboardInterrupt:
            self.stdout.write(
                self.style.WARNING('收到中断信号，正在关闭任务执行器...')
            )
        finally:
            task_executor.stop()
            self.stdout.write(
                self.style.SUCCESS('任务执行器已停止')
            )
