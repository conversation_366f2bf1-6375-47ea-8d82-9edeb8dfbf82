"""
AI数字人SaaS平台 URL Configuration
基于对https://hm.umi6.com/平台的逆向工程分析
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from django.views.generic import RedirectView
from apps.common.health_check import HealthCheckView, QuickHealthCheckView, SystemStatsView
from apps.common.performance_api import PerformanceMetricsView, PerformanceAnalyticsView, PerformanceRealtimeView

urlpatterns = [
    # Admin interface with Unfold theme
    path('admin/', admin.site.urls),

    # Dashboard
    path('dashboard/', include('apps.dashboard.urls')),

    # API endpoints
    path('api/v1/', include('apps.users.urls')),
    path('api/v1/', include('apps.avatars.urls')),
    path('api/v1/', include('apps.voices.urls')),
    path('api/v1/', include('apps.videos.urls')),
    path('api/v1/', include('apps.copywriting.urls')),
    path('api/v1/packages/', include('apps.packages.urls')),
    path('api/v1/', include('apps.notifications.urls')),  # 通知API
    path('api/v1/uploads/', include('apps.uploads.urls')),  # 文件上传API
    path('api/v1/ai-tools/', include('apps.ai_tools.urls')),  # AI工具API
    path('api/v1/digital-works/', include('digital_works.urls')),  # 数字作品API
    path('api/v1/config/', include('apps.config.urls')),  # 配置管理API
    path('api/v1/', include('apps.common.urls')),

    # 系统健康检查
    path('health/', HealthCheckView.as_view(), name='health-check'),
    path('health/quick/', QuickHealthCheckView.as_view(), name='quick-health-check'),
    path('health/stats/', SystemStatsView.as_view(), name='system-stats'),

    # 性能监控
    path('api/v1/performance/metrics/', PerformanceMetricsView.as_view(), name='performance-metrics'),
    path('api/v1/performance/analytics/', PerformanceAnalyticsView.as_view(), name='performance-analytics'),
    path('api/v1/performance/realtime/', PerformanceRealtimeView.as_view(), name='performance-realtime'),

    # Redirect root to admin dashboard
    path('', RedirectView.as_view(url='/dashboard/dashboard/', permanent=False)),
]

# Serve media files in development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATICFILES_DIRS[0])
    
    # Debug toolbar
    if 'debug_toolbar' in settings.INSTALLED_APPS:
        import debug_toolbar
        urlpatterns = [
            path('__debug__/', include(debug_toolbar.urls)),
        ] + urlpatterns
