"""
控制台管理后台配置
"""
from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from django.utils.html import format_html
from django.urls import path, reverse
from django.shortcuts import render
from django.db.models import Sum, Count
from django.utils import timezone
from datetime import datetime, timedelta
from unfold.admin import ModelAdmin
from unfold.contrib.filters.admin import RangeDateFilter, ChoicesDropdownFilter

from .models import SystemStatistics, DailyStatistics, SystemMonitor, SystemLog, ServiceStatus
from apps.users.models import User, ComputingPowerLog
from apps.avatars.models import DigitalAvatar
from apps.videos.models import VideoProduction
from apps.packages.models import Package


class DashboardAdminMixin:
    """控制台管理混入类"""
    
    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path('dashboard/', self.admin_site.admin_view(self.dashboard_view), name='dashboard'),
        ]
        return custom_urls + urls
    
    def dashboard_view(self, request):
        """控制台视图"""
        # 获取统计数据
        today = timezone.now().date()
        yesterday = today - timedelta(days=1)
        last_week = today - timedelta(days=7)
        last_month = today - timedelta(days=30)
        
        # 用户统计
        total_users = User.objects.count()
        active_users = User.objects.filter(is_active=True).count()
        new_users_today = User.objects.filter(date_joined__date=today).count()
        new_users_week = User.objects.filter(date_joined__date__gte=last_week).count()
        
        # 数字人统计
        total_avatars = DigitalAvatar.objects.count()
        public_avatars = DigitalAvatar.objects.filter(is_public=True).count()
        private_avatars = DigitalAvatar.objects.filter(is_public=False).count()
        avatars_today = DigitalAvatar.objects.filter(created_at__date=today).count()
        
        # 视频统计
        total_videos = VideoProduction.objects.count()
        videos_today = VideoProduction.objects.filter(created_at__date=today).count()
        videos_week = VideoProduction.objects.filter(created_at__date__gte=last_week).count()
        
        # 算力统计
        total_computing_power = ComputingPowerLog.objects.filter(
            operation_type='consume'
        ).aggregate(total=Sum('amount'))['total'] or 0
        
        computing_power_today = ComputingPowerLog.objects.filter(
            operation_type='consume',
            created_at__date=today
        ).aggregate(total=Sum('amount'))['total'] or 0
        
        # 套餐统计
        active_packages = Package.objects.filter(is_active=True).count()
        
        # 最近7天的数据趋势
        daily_stats = []
        for i in range(7):
            date = today - timedelta(days=i)
            stats = {
                'date': date.strftime('%m-%d'),
                'users': User.objects.filter(date_joined__date=date).count(),
                'videos': VideoProduction.objects.filter(created_at__date=date).count(),
                'computing_power': ComputingPowerLog.objects.filter(
                    operation_type='consume',
                    created_at__date=date
                ).aggregate(total=Sum('amount'))['total'] or 0,
            }
            daily_stats.append(stats)
        daily_stats.reverse()
        
        # 用户分布统计
        user_distribution = {
            'verified': User.objects.filter(is_verified=True).count(),
            'unverified': User.objects.filter(is_verified=False).count(),
            'basic': User.objects.filter(package_type='basic').count(),
            'premium': User.objects.filter(package_type='premium').count(),
            'enterprise': User.objects.filter(package_type='enterprise').count(),
        }
        
        # 数字人分类统计
        avatar_stats = DigitalAvatar.objects.values('scene_type').annotate(
            count=Count('id')
        ).order_by('-count')
        
        context = {
            'title': '控制台',
            'site_title': 'AI数字人SaaS平台',
            'site_header': 'AI数字人SaaS平台',
            'has_permission': True,
            
            # 统计卡片数据
            'stats_cards': [
                {
                    'title': '总用户数',
                    'value': total_users,
                    'icon': 'people',
                    'color': 'primary',
                    'change': f'+{new_users_today}',
                    'change_label': '今日新增'
                },
                {
                    'title': '活跃用户',
                    'value': active_users,
                    'icon': 'person_check',
                    'color': 'success',
                    'change': f'{(active_users/total_users*100):.1f}%' if total_users > 0 else '0%',
                    'change_label': '活跃率'
                },
                {
                    'title': '数字人总数',
                    'value': total_avatars,
                    'icon': 'smart_toy',
                    'color': 'info',
                    'change': f'+{avatars_today}',
                    'change_label': '今日新增'
                },
                {
                    'title': '视频总数',
                    'value': total_videos,
                    'icon': 'video_library',
                    'color': 'warning',
                    'change': f'+{videos_today}',
                    'change_label': '今日生成'
                },
                {
                    'title': '算力消耗',
                    'value': f'{total_computing_power:,}',
                    'icon': 'memory',
                    'color': 'secondary',
                    'change': f'+{computing_power_today:,}',
                    'change_label': '今日消耗'
                },
                {
                    'title': '活跃套餐',
                    'value': active_packages,
                    'icon': 'card_membership',
                    'color': 'purple',
                    'change': '100%',
                    'change_label': '可用率'
                },
            ],
            
            # 图表数据
            'daily_stats': daily_stats,
            'user_distribution': user_distribution,
            'avatar_stats': list(avatar_stats),
            
            # 快速统计
            'quick_stats': {
                'new_users_week': new_users_week,
                'videos_week': videos_week,
                'public_avatars': public_avatars,
                'private_avatars': private_avatars,
            }
        }
        
        return render(request, 'admin/dashboard/dashboard.html', context)


@admin.register(SystemStatistics)
class SystemStatisticsAdmin(ModelAdmin, DashboardAdminMixin):
    """系统统计管理"""
    
    list_display = (
        'total_users', 'active_users', 'total_avatars', 'total_videos',
        'total_computing_power', 'updated_at'
    )
    
    readonly_fields = (
        'total_users', 'active_users', 'new_users_today',
        'total_avatars', 'public_avatars', 'private_avatars',
        'total_videos', 'videos_today',
        'total_computing_power', 'computing_power_today',
        'total_revenue', 'revenue_today',
        'active_packages', 'updated_at'
    )
    
    def has_add_permission(self, request):
        return False
    
    def has_delete_permission(self, request, obj=None):
        return False


@admin.register(DailyStatistics)
class DailyStatisticsAdmin(ModelAdmin):
    """每日统计管理"""
    
    list_display = (
        'date', 'new_users', 'active_users', 'videos_created',
        'avatars_created', 'computing_power_consumed', 'revenue'
    )
    
    list_filter = (
        ('date', RangeDateFilter),
    )
    
    readonly_fields = ('created_at',)
    
    ordering = ('-date',)
    
    def has_delete_permission(self, request, obj=None):
        return False


@admin.register(SystemMonitor)
class SystemMonitorAdmin(ModelAdmin):
    """系统监控管理"""

    list_display = ('monitor_type', 'value', 'unit', 'status', 'created_at')
    list_filter = (
        ('monitor_type', ChoicesDropdownFilter),
        ('status', ChoicesDropdownFilter),
        ('created_at', RangeDateFilter),
    )
    search_fields = ('message',)
    readonly_fields = ('created_at',)
    ordering = ('-created_at',)

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False


@admin.register(SystemLog)
class SystemLogAdmin(ModelAdmin):
    """系统日志管理"""

    list_display = ('level', 'module', 'message_preview', 'user_id', 'ip_address', 'created_at')
    list_filter = (
        ('level', ChoicesDropdownFilter),
        'module',
        ('created_at', RangeDateFilter),
    )
    search_fields = ('message', 'module', 'ip_address')
    readonly_fields = ('created_at',)
    ordering = ('-created_at',)

    def message_preview(self, obj):
        """消息预览"""
        return obj.message[:100] + '...' if len(obj.message) > 100 else obj.message
    message_preview.short_description = _('消息预览')

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False


@admin.register(ServiceStatus)
class ServiceStatusAdmin(ModelAdmin):
    """服务状态管理"""

    list_display = ('service_name', 'status', 'response_time', 'uptime_percentage', 'last_check')
    list_filter = (
        ('service_name', ChoicesDropdownFilter),
        ('status', ChoicesDropdownFilter),
        ('last_check', RangeDateFilter),
    )
    readonly_fields = ('last_check',)
    ordering = ('service_name',)

    def has_delete_permission(self, request, obj=None):
        return False
