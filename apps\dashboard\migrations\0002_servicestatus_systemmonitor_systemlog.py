# Generated by Django 5.2.1 on 2025-07-16 12:23

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("dashboard", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="ServiceStatus",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "service_name",
                    models.CharField(
                        choices=[
                            ("django", "Django应用"),
                            ("database", "数据库"),
                            ("redis", "Redis缓存"),
                            ("celery_worker", "Celery Worker"),
                            ("celery_beat", "Celery Beat"),
                            ("nginx", "Nginx"),
                            ("ai_service", "AI服务"),
                        ],
                        max_length=20,
                        unique=True,
                        verbose_name="服务名称",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("online", "在线"),
                            ("offline", "离线"),
                            ("error", "错误"),
                            ("maintenance", "维护中"),
                        ],
                        default="offline",
                        max_length=20,
                        verbose_name="状态",
                    ),
                ),
                (
                    "response_time",
                    models.FloatField(blank=True, null=True, verbose_name="响应时间(ms)"),
                ),
                ("error_message", models.TextField(blank=True, verbose_name="错误信息")),
                (
                    "last_check",
                    models.DateTimeField(auto_now=True, verbose_name="最后检查时间"),
                ),
                (
                    "uptime_percentage",
                    models.FloatField(default=0.0, verbose_name="可用性百分比"),
                ),
            ],
            options={
                "verbose_name": "服务状态",
                "verbose_name_plural": "服务状态",
            },
        ),
        migrations.CreateModel(
            name="SystemMonitor",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "monitor_type",
                    models.CharField(
                        choices=[
                            ("cpu", "CPU使用率"),
                            ("memory", "内存使用率"),
                            ("disk", "磁盘使用率"),
                            ("network", "网络流量"),
                            ("database", "数据库连接"),
                            ("redis", "Redis状态"),
                            ("celery", "Celery队列"),
                        ],
                        max_length=20,
                        verbose_name="监控类型",
                    ),
                ),
                ("value", models.FloatField(verbose_name="监控值")),
                (
                    "unit",
                    models.CharField(default="%", max_length=10, verbose_name="单位"),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("normal", "正常"),
                            ("warning", "警告"),
                            ("critical", "严重"),
                        ],
                        default="normal",
                        max_length=10,
                        verbose_name="状态",
                    ),
                ),
                ("message", models.TextField(blank=True, verbose_name="详细信息")),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="记录时间"),
                ),
            ],
            options={
                "verbose_name": "系统监控",
                "verbose_name_plural": "系统监控",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="SystemLog",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "level",
                    models.CharField(
                        choices=[
                            ("debug", "调试"),
                            ("info", "信息"),
                            ("warning", "警告"),
                            ("error", "错误"),
                            ("critical", "严重错误"),
                        ],
                        max_length=10,
                        verbose_name="日志级别",
                    ),
                ),
                ("module", models.CharField(max_length=50, verbose_name="模块")),
                ("message", models.TextField(verbose_name="日志消息")),
                (
                    "extra_data",
                    models.TextField(
                        blank=True, help_text="JSON格式的额外数据", verbose_name="额外数据"
                    ),
                ),
                (
                    "user_id",
                    models.IntegerField(blank=True, null=True, verbose_name="用户ID"),
                ),
                (
                    "ip_address",
                    models.GenericIPAddressField(
                        blank=True, null=True, verbose_name="IP地址"
                    ),
                ),
                ("user_agent", models.TextField(blank=True, verbose_name="用户代理")),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
            ],
            options={
                "verbose_name": "系统日志",
                "verbose_name_plural": "系统日志",
                "ordering": ["-created_at"],
                "indexes": [
                    models.Index(
                        fields=["level", "created_at"],
                        name="dashboard_s_level_e7fe0d_idx",
                    ),
                    models.Index(
                        fields=["module", "created_at"],
                        name="dashboard_s_module_103982_idx",
                    ),
                ],
            },
        ),
    ]
