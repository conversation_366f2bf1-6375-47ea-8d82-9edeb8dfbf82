"""
初始化系统配置管理命令
"""
from django.core.management.base import BaseCommand
from apps.config.models import SystemConfig


class Command(BaseCommand):
    help = '初始化系统默认配置'
    
    def handle(self, *args, **options):
        self.stdout.write('开始初始化系统配置...')
        
        # 默认配置列表
        default_configs = [
            # 通用配置
            {
                'key': 'site_name',
                'name': '网站名称',
                'description': '网站的显示名称',
                'config_type': 'general',
                'value': 'AI数字人SaaS平台',
                'value_type': 'string',
                'default_value': 'AI数字人SaaS平台',
            },
            {
                'key': 'site_description',
                'name': '网站描述',
                'description': '网站的描述信息',
                'config_type': 'general',
                'value': '专业的AI数字人生成和管理平台',
                'value_type': 'string',
                'default_value': '专业的AI数字人生成和管理平台',
            },
            {
                'key': 'maintenance_mode',
                'name': '维护模式',
                'description': '是否开启维护模式',
                'config_type': 'general',
                'value': 'false',
                'value_type': 'boolean',
                'default_value': 'false',
            },
            
            # 算力配置
            {
                'key': 'default_computing_power',
                'name': '默认算力',
                'description': '新用户默认获得的算力',
                'config_type': 'computing',
                'value': '100',
                'value_type': 'integer',
                'default_value': '100',
            },
            {
                'key': 'computing_power_price',
                'name': '算力单价',
                'description': '每单位算力的价格（元）',
                'config_type': 'computing',
                'value': '0.01',
                'value_type': 'float',
                'default_value': '0.01',
            },
            {
                'key': 'max_computing_power',
                'name': '最大算力',
                'description': '单个用户最大算力限制',
                'config_type': 'computing',
                'value': '10000',
                'value_type': 'integer',
                'default_value': '10000',
            },
            
            # AI服务配置
            {
                'key': 'ai_service_provider',
                'name': 'AI服务提供商',
                'description': '当前使用的AI服务提供商',
                'config_type': 'ai_service',
                'value': 'openai',
                'value_type': 'string',
                'default_value': 'openai',
            },
            {
                'key': 'ai_api_key',
                'name': 'AI API密钥',
                'description': 'AI服务的API密钥',
                'config_type': 'ai_service',
                'value': '',
                'value_type': 'string',
                'default_value': '',
                'is_sensitive': True,
            },
            {
                'key': 'ai_api_base_url',
                'name': 'AI API基础URL',
                'description': 'AI服务的API基础URL',
                'config_type': 'ai_service',
                'value': 'https://api.openai.com/v1',
                'value_type': 'string',
                'default_value': 'https://api.openai.com/v1',
            },
            
            # 存储配置
            {
                'key': 'storage_provider',
                'name': '存储提供商',
                'description': '文件存储服务提供商',
                'config_type': 'storage',
                'value': 'local',
                'value_type': 'string',
                'default_value': 'local',
            },
            {
                'key': 'max_file_size',
                'name': '最大文件大小',
                'description': '允许上传的最大文件大小（MB）',
                'config_type': 'storage',
                'value': '50',
                'value_type': 'integer',
                'default_value': '50',
            },
            {
                'key': 'allowed_file_types',
                'name': '允许的文件类型',
                'description': '允许上传的文件类型列表',
                'config_type': 'storage',
                'value': '["jpg", "jpeg", "png", "gif", "mp4", "mp3", "wav"]',
                'value_type': 'json',
                'default_value': '["jpg", "jpeg", "png", "gif", "mp4", "mp3", "wav"]',
            },
            
            # 通知配置
            {
                'key': 'email_notifications',
                'name': '邮件通知',
                'description': '是否启用邮件通知',
                'config_type': 'notification',
                'value': 'true',
                'value_type': 'boolean',
                'default_value': 'true',
            },
            {
                'key': 'sms_notifications',
                'name': '短信通知',
                'description': '是否启用短信通知',
                'config_type': 'notification',
                'value': 'false',
                'value_type': 'boolean',
                'default_value': 'false',
            },
            
            # 安全配置
            {
                'key': 'password_min_length',
                'name': '密码最小长度',
                'description': '用户密码的最小长度',
                'config_type': 'security',
                'value': '8',
                'value_type': 'integer',
                'default_value': '8',
            },
            {
                'key': 'login_attempts_limit',
                'name': '登录尝试限制',
                'description': '允许的最大登录失败次数',
                'config_type': 'security',
                'value': '5',
                'value_type': 'integer',
                'default_value': '5',
            },
            {
                'key': 'session_timeout',
                'name': '会话超时时间',
                'description': '用户会话超时时间（分钟）',
                'config_type': 'security',
                'value': '1440',
                'value_type': 'integer',
                'default_value': '1440',
            },
            
            # 功能开关
            {
                'key': 'enable_user_registration',
                'name': '启用用户注册',
                'description': '是否允许新用户注册',
                'config_type': 'feature',
                'value': 'true',
                'value_type': 'boolean',
                'default_value': 'true',
            },
            {
                'key': 'enable_avatar_creation',
                'name': '启用数字人创建',
                'description': '是否允许用户创建自定义数字人',
                'config_type': 'feature',
                'value': 'true',
                'value_type': 'boolean',
                'default_value': 'true',
            },
            {
                'key': 'enable_voice_cloning',
                'name': '启用语音克隆',
                'description': '是否允许用户进行语音克隆',
                'config_type': 'feature',
                'value': 'true',
                'value_type': 'boolean',
                'default_value': 'true',
            },
        ]
        
        created_count = 0
        updated_count = 0
        
        for config_data in default_configs:
            config, created = SystemConfig.objects.get_or_create(
                key=config_data['key'],
                defaults=config_data
            )
            
            if created:
                created_count += 1
                self.stdout.write(
                    self.style.SUCCESS(f'创建配置: {config.name} ({config.key})')
                )
            else:
                # 更新描述和默认值
                config.description = config_data['description']
                config.default_value = config_data['default_value']
                if 'is_sensitive' in config_data:
                    config.is_sensitive = config_data['is_sensitive']
                config.save()
                updated_count += 1
                self.stdout.write(
                    self.style.WARNING(f'更新配置: {config.name} ({config.key})')
                )
        
        self.stdout.write(
            self.style.SUCCESS(
                f'配置初始化完成: 创建 {created_count} 个，更新 {updated_count} 个'
            )
        )
