"""
智能文案模型
基于对https://hm.umi6.com/平台的逆向工程分析
"""
from django.db import models
from django.conf import settings
from django.utils.translation import gettext_lazy as _


class Copywriting(models.Model):
    """智能文案生成"""
    
    SCENE_TYPE_CHOICES = [
        ('general', '通用'),
        ('knowledge', '知识科普'),
        ('emotion', '情感专家'),
        ('ecommerce', '电商营销'),
        ('blessing', '祝福问候'),
        ('health', '健康大师'),
    ]
    
    WORD_COUNT_CHOICES = [
        (100, '100字 ~ 60秒'),
        (300, '300字 ~ 120秒'),
        (500, '500字 ~ 300秒'),
        (1000, '1000字 ~ 600秒'),
    ]
    
    STATUS_CHOICES = [
        ('pending', '待生成'),
        ('generating', '生成中'),
        ('completed', '已完成'),
        ('failed', '生成失败'),
    ]
    
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='copywritings',
        verbose_name=_('用户')
    )
    
    title = models.CharField(
        _('文案标题'),
        max_length=200,
        blank=True,
        help_text=_('文案的标题或主题')
    )
    
    scene_type = models.CharField(
        _('场景类型'),
        max_length=20,
        choices=SCENE_TYPE_CHOICES,
        default='general',
        help_text=_('文案生成的场景类型')
    )
    
    theme_description = models.TextField(
        _('主题描述'),
        help_text=_('文案生成的主题描述和要求')
    )
    
    word_count = models.PositiveIntegerField(
        _('字数要求'),
        choices=WORD_COUNT_CHOICES,
        default=300,
        help_text=_('期望生成的文案字数')
    )
    
    language = models.CharField(
        _('语言'),
        max_length=20,
        default='zh-cn',
        choices=[
            ('zh-cn', '中文（简体）'),
            ('zh-tw', '中文（繁体）'),
            ('en-us', '英语'),
        ],
        help_text=_('文案生成的语言')
    )
    
    status = models.CharField(
        _('生成状态'),
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending',
        help_text=_('文案生成的状态')
    )
    
    generated_content = models.TextField(
        _('生成内容'),
        blank=True,
        help_text=_('AI生成的文案内容')
    )
    
    actual_word_count = models.PositiveIntegerField(
        _('实际字数'),
        null=True,
        blank=True,
        help_text=_('生成文案的实际字数')
    )
    
    error_message = models.TextField(
        _('错误信息'),
        blank=True,
        help_text=_('生成失败时的错误信息')
    )
    
    computing_power_consumed = models.PositiveIntegerField(
        _('消耗算力'),
        default=0,
        help_text=_('本次生成消耗的算力点数')
    )
    
    is_saved_as_draft = models.BooleanField(
        _('保存为草稿'),
        default=False,
        help_text=_('是否保存为草稿')
    )
    
    created_at = models.DateTimeField(
        _('创建时间'),
        auto_now_add=True
    )
    
    updated_at = models.DateTimeField(
        _('更新时间'),
        auto_now=True
    )
    
    completed_at = models.DateTimeField(
        _('完成时间'),
        null=True,
        blank=True,
        help_text=_('文案生成完成的时间')
    )

    class Meta:
        verbose_name = _('智能文案')
        verbose_name_plural = _('智能文案')
        db_table = 'copywritings'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'status']),
            models.Index(fields=['scene_type', 'created_at']),
            models.Index(fields=['status', 'created_at']),
        ]

    def __str__(self):
        return f"{self.user.display_name} - {self.title or self.theme_description[:50]}"

    @property
    def estimated_duration(self):
        """估算视频时长（秒）"""
        duration_map = {
            100: 60,
            300: 120,
            500: 300,
            1000: 600,
        }
        return duration_map.get(self.word_count, 120)


class CopywritingTemplate(models.Model):
    """文案模板"""
    
    name = models.CharField(
        _('模板名称'),
        max_length=100,
        help_text=_('文案模板的名称')
    )
    
    description = models.TextField(
        _('模板描述'),
        blank=True,
        help_text=_('模板的详细描述')
    )
    
    scene_type = models.CharField(
        _('适用场景'),
        max_length=20,
        choices=Copywriting.SCENE_TYPE_CHOICES,
        help_text=_('模板适用的场景类型')
    )
    
    template_content = models.TextField(
        _('模板内容'),
        help_text=_('文案模板的内容，支持变量替换')
    )
    
    variables = models.JSONField(
        _('模板变量'),
        default=dict,
        blank=True,
        help_text=_('模板中使用的变量定义')
    )
    
    is_active = models.BooleanField(
        _('是否启用'),
        default=True,
        help_text=_('是否启用此模板')
    )
    
    usage_count = models.PositiveIntegerField(
        _('使用次数'),
        default=0,
        help_text=_('模板被使用的次数')
    )
    
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='created_templates',
        verbose_name=_('创建者')
    )
    
    created_at = models.DateTimeField(
        _('创建时间'),
        auto_now_add=True
    )
    
    updated_at = models.DateTimeField(
        _('更新时间'),
        auto_now=True
    )

    class Meta:
        verbose_name = _('文案模板')
        verbose_name_plural = _('文案模板')
        db_table = 'copywriting_templates'
        ordering = ['-usage_count', 'name']

    def __str__(self):
        return f"{self.name} ({self.get_scene_type_display()})"

    def increment_usage(self):
        """增加使用次数"""
        self.usage_count += 1
        self.save(update_fields=['usage_count'])


class CopywritingHistory(models.Model):
    """文案生成历史"""
    
    copywriting = models.ForeignKey(
        Copywriting,
        on_delete=models.CASCADE,
        related_name='history',
        verbose_name=_('文案')
    )
    
    version = models.PositiveIntegerField(
        _('版本号'),
        default=1,
        help_text=_('文案的版本号')
    )
    
    content = models.TextField(
        _('文案内容'),
        help_text=_('此版本的文案内容')
    )
    
    word_count = models.PositiveIntegerField(
        _('字数'),
        help_text=_('此版本文案的字数')
    )
    
    generation_params = models.JSONField(
        _('生成参数'),
        default=dict,
        blank=True,
        help_text=_('生成此版本时使用的参数')
    )
    
    created_at = models.DateTimeField(
        _('创建时间'),
        auto_now_add=True
    )

    class Meta:
        verbose_name = _('文案历史')
        verbose_name_plural = _('文案历史')
        db_table = 'copywriting_history'
        ordering = ['-version']
        unique_together = ['copywriting', 'version']

    def __str__(self):
        return f"{self.copywriting} - 版本 {self.version}"


class TextExtraction(models.Model):
    """文案提取"""
    
    SOURCE_TYPE_CHOICES = [
        ('image', '图片'),
        ('video', '视频'),
        ('audio', '音频'),
        ('url', '网页链接'),
        ('file', '文档文件'),
    ]
    
    STATUS_CHOICES = [
        ('pending', '待处理'),
        ('processing', '处理中'),
        ('completed', '已完成'),
        ('failed', '处理失败'),
    ]
    
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='text_extractions',
        verbose_name=_('用户')
    )
    
    source_type = models.CharField(
        _('来源类型'),
        max_length=20,
        choices=SOURCE_TYPE_CHOICES,
        help_text=_('文案提取的来源类型')
    )
    
    source_file = models.FileField(
        _('来源文件'),
        upload_to='extractions/sources/',
        null=True,
        blank=True,
        help_text=_('上传的来源文件')
    )
    
    source_url = models.URLField(
        _('来源链接'),
        null=True,
        blank=True,
        help_text=_('来源的网页链接')
    )
    
    status = models.CharField(
        _('处理状态'),
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending',
        help_text=_('文案提取的处理状态')
    )
    
    extracted_text = models.TextField(
        _('提取文本'),
        blank=True,
        help_text=_('提取出的文本内容')
    )
    
    confidence_score = models.FloatField(
        _('置信度'),
        null=True,
        blank=True,
        help_text=_('文本提取的置信度分数（0-1）')
    )
    
    error_message = models.TextField(
        _('错误信息'),
        blank=True,
        help_text=_('处理失败时的错误信息')
    )
    
    computing_power_consumed = models.PositiveIntegerField(
        _('消耗算力'),
        default=0,
        help_text=_('本次提取消耗的算力点数')
    )
    
    created_at = models.DateTimeField(
        _('创建时间'),
        auto_now_add=True
    )
    
    updated_at = models.DateTimeField(
        _('更新时间'),
        auto_now=True
    )
    
    completed_at = models.DateTimeField(
        _('完成时间'),
        null=True,
        blank=True,
        help_text=_('提取完成的时间')
    )

    class Meta:
        verbose_name = _('文案提取')
        verbose_name_plural = _('文案提取')
        db_table = 'text_extractions'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'status']),
            models.Index(fields=['source_type', 'created_at']),
            models.Index(fields=['status', 'created_at']),
        ]

    def __str__(self):
        return f"{self.user.display_name} - {self.get_source_type_display()} 提取"
