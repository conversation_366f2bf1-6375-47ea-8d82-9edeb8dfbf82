# Generated by Django 5.2.4 on 2025-07-16 06:39

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="ToolCategory",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.Char<PERSON>ield(max_length=100, verbose_name="分类名称")),
                (
                    "key",
                    models.Char<PERSON>ield(max_length=50, unique=True, verbose_name="分类标识"),
                ),
                ("icon", models.CharField(max_length=100, verbose_name="图标名称")),
                ("color", models.Char<PERSON>ield(max_length=20, verbose_name="颜色")),
                ("sort_order", models.IntegerField(default=0, verbose_name="排序")),
                ("is_active", models.Boolean<PERSON>ield(default=True, verbose_name="是否启用")),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
            ],
            options={
                "verbose_name": "AI工具分类",
                "verbose_name_plural": "AI工具分类",
                "db_table": "ai_tool_categories",
                "ordering": ["sort_order", "id"],
            },
        ),
        migrations.CreateModel(
            name="AITool",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=200, verbose_name="工具名称")),
                ("description", models.TextField(verbose_name="工具描述")),
                ("icon", models.CharField(max_length=100, verbose_name="图标名称")),
                ("gradient", models.CharField(max_length=200, verbose_name="渐变色")),
                ("tags", models.JSONField(default=list, verbose_name="标签")),
                ("usage_count", models.IntegerField(default=0, verbose_name="使用次数")),
                (
                    "rating",
                    models.DecimalField(
                        decimal_places=1, default=0.0, max_digits=3, verbose_name="评分"
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("available", "可用"),
                            ("coming_soon", "即将推出"),
                            ("maintenance", "维护中"),
                            ("disabled", "已禁用"),
                        ],
                        default="available",
                        max_length=20,
                        verbose_name="状态",
                    ),
                ),
                ("is_new", models.BooleanField(default=False, verbose_name="是否新工具")),
                ("is_pro", models.BooleanField(default=False, verbose_name="是否专业版")),
                ("sort_order", models.IntegerField(default=0, verbose_name="排序")),
                (
                    "route_path",
                    models.CharField(blank=True, max_length=200, verbose_name="路由路径"),
                ),
                (
                    "api_endpoint",
                    models.CharField(blank=True, max_length=200, verbose_name="API端点"),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "category",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="ai_tools.toolcategory",
                        verbose_name="分类",
                    ),
                ),
            ],
            options={
                "verbose_name": "AI工具",
                "verbose_name_plural": "AI工具",
                "db_table": "ai_tools",
                "ordering": ["sort_order", "id"],
            },
        ),
        migrations.CreateModel(
            name="ToolUsageLog",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("session_id", models.CharField(max_length=100, verbose_name="会话ID")),
                ("input_data", models.JSONField(default=dict, verbose_name="输入数据")),
                ("output_data", models.JSONField(default=dict, verbose_name="输出数据")),
                (
                    "processing_time",
                    models.FloatField(default=0.0, verbose_name="处理时间(秒)"),
                ),
                ("cost_points", models.IntegerField(default=0, verbose_name="消耗算力")),
                (
                    "status",
                    models.CharField(
                        default="success", max_length=20, verbose_name="状态"
                    ),
                ),
                ("error_message", models.TextField(blank=True, verbose_name="错误信息")),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "tool",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="ai_tools.aitool",
                        verbose_name="工具",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="用户",
                    ),
                ),
            ],
            options={
                "verbose_name": "工具使用记录",
                "verbose_name_plural": "工具使用记录",
                "db_table": "ai_tool_usage_logs",
                "ordering": ["-created_at"],
            },
        ),
    ]
