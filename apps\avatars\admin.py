"""
数字人形象管理后台配置
"""
from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from django.utils.html import format_html
from unfold.admin import ModelAdmin, TabularInline
from unfold.contrib.filters.admin import RangeDateFilter, ChoicesDropdownFilter

from .models import DigitalAvatar, AvatarCategory, AvatarTag, AvatarUsageLog


class AvatarUsageLogInline(TabularInline):
    """形象使用日志内联编辑"""
    model = AvatarUsageLog
    extra = 0
    readonly_fields = ('created_at',)
    fields = ('user', 'computing_power_consumed', 'purpose', 'created_at')
    
    def has_add_permission(self, request, obj=None):
        return False


@admin.register(DigitalAvatar)
class DigitalAvatarAdmin(ModelAdmin):
    """数字人形象管理"""
    
    list_display = (
        'name', 'model_version', 'scene_type', 'is_public', 'is_active',
        'computing_power_cost', 'usage_count', 'owner', 'created_at'
    )
    
    list_filter = (
        ('model_version', ChoicesDropdownFilter),
        ('scene_type', ChoicesDropdownFilter),
        'is_public', 'is_active',
        ('created_at', RangeDateFilter),
    )
    
    search_fields = ('name', 'description', 'owner__username')
    
    readonly_fields = ('usage_count', 'created_at', 'updated_at', 'video_thumbnail_preview')
    
    fieldsets = (
        (_('基本信息'), {
            'fields': ('name', 'description', 'model_version', 'scene_type')
        }),
        (_('媒体文件'), {
            'fields': ('thumbnail', 'video_preview', 'video_url', 'video_thumbnail_preview')
        }),
        (_('设置'), {
            'fields': ('is_public', 'is_active', 'owner', 'computing_power_cost', 'sort_order')
        }),
        (_('分类和标签'), {
            'fields': ('categories', 'tags'),
            'classes': ('collapse',)
        }),
        (_('统计信息'), {
            'fields': ('usage_count', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    filter_horizontal = ('categories', 'tags')
    
    ordering = ('sort_order', '-created_at')
    
    inlines = [AvatarUsageLogInline]
    
    actions = ['activate_avatars', 'deactivate_avatars', 'make_public', 'make_private', 'export_to_csv', 'export_to_json']
    
    def video_thumbnail_preview(self, obj):
        """视频缩略图预览"""
        if obj.video_thumbnail_url:
            return format_html(
                '<img src="{}" style="max-width: 200px; max-height: 150px;" />',
                obj.video_thumbnail_url
            )
        return _('无缩略图')
    video_thumbnail_preview.short_description = _('视频缩略图')
    
    def activate_avatars(self, request, queryset):
        """批量启用数字人形象"""
        updated = queryset.update(is_active=True)
        self.message_user(request, f'成功启用 {updated} 个数字人形象')
    activate_avatars.short_description = _('启用选中的数字人形象')
    
    def deactivate_avatars(self, request, queryset):
        """批量停用数字人形象"""
        updated = queryset.update(is_active=False)
        self.message_user(request, f'成功停用 {updated} 个数字人形象')
    deactivate_avatars.short_description = _('停用选中的数字人形象')
    
    def make_public(self, request, queryset):
        """设为公共形象"""
        updated = queryset.update(is_public=True, owner=None)
        self.message_user(request, f'成功将 {updated} 个数字人形象设为公共')
    make_public.short_description = _('设为公共形象')
    
    def make_private(self, request, queryset):
        """设为私有形象"""
        updated = queryset.update(is_public=False)
        self.message_user(request, f'成功将 {updated} 个数字人形象设为私有')
    make_private.short_description = _('设为私有形象')

    def export_to_csv(self, request, queryset):
        """导出为CSV"""
        import csv
        from django.http import HttpResponse

        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="avatars.csv"'
        response.write('\ufeff')  # BOM for Excel

        writer = csv.writer(response)
        writer.writerow([
            'ID', '名称', '描述', '模型版本', '场景类型', '算力消耗',
            '是否公开', '是否激活', '使用次数', '排序', '创建时间'
        ])

        for avatar in queryset:
            writer.writerow([
                avatar.id,
                avatar.name,
                avatar.description,
                avatar.model_version,
                avatar.scene_type,
                avatar.computing_power_cost,
                '是' if avatar.is_public else '否',
                '是' if avatar.is_active else '否',
                avatar.usage_count,
                avatar.sort_order,
                avatar.created_at.strftime('%Y-%m-%d %H:%M:%S')
            ])

        return response
    export_to_csv.short_description = _('导出选中项为CSV')

    def export_to_json(self, request, queryset):
        """导出为JSON"""
        import json
        from django.http import HttpResponse

        data = []
        for avatar in queryset:
            data.append({
                'id': avatar.id,
                'name': avatar.name,
                'description': avatar.description,
                'model_version': avatar.model_version,
                'scene_type': avatar.scene_type,
                'computing_power_cost': avatar.computing_power_cost,
                'is_public': avatar.is_public,
                'is_active': avatar.is_active,
                'usage_count': avatar.usage_count,
                'sort_order': avatar.sort_order,
                'thumbnail': avatar.thumbnail.url if avatar.thumbnail else '',
                'video_preview': avatar.video_preview.url if avatar.video_preview else '',
                'video_url': avatar.video_url,
                'categories': [cat.name for cat in avatar.categories.all()],
                'tags': [tag.name for tag in avatar.tags.all()],
                'created_at': avatar.created_at.isoformat(),
            })

        response = HttpResponse(
            json.dumps(data, ensure_ascii=False, indent=2),
            content_type='application/json'
        )
        response['Content-Disposition'] = 'attachment; filename="avatars.json"'

        return response
    export_to_json.short_description = _('导出选中项为JSON')


@admin.register(AvatarCategory)
class AvatarCategoryAdmin(ModelAdmin):
    """数字人形象分类管理"""
    
    list_display = ('name', 'icon', 'sort_order', 'is_active', 'avatar_count', 'created_at')
    
    list_filter = ('is_active', ('created_at', RangeDateFilter))
    
    search_fields = ('name', 'description')
    
    readonly_fields = ('created_at', 'avatar_count')
    
    fieldsets = (
        (_('基本信息'), {
            'fields': ('name', 'description', 'icon')
        }),
        (_('设置'), {
            'fields': ('sort_order', 'is_active')
        }),
        (_('统计信息'), {
            'fields': ('avatar_count', 'created_at'),
            'classes': ('collapse',)
        }),
    )
    
    ordering = ('sort_order', 'name')
    
    def avatar_count(self, obj):
        """关联的数字人形象数量"""
        return obj.avatars.count()
    avatar_count.short_description = _('形象数量')


@admin.register(AvatarTag)
class AvatarTagAdmin(ModelAdmin):
    """数字人形象标签管理"""
    
    list_display = ('name', 'color_preview', 'usage_count', 'avatar_count', 'created_at')
    
    list_filter = (('created_at', RangeDateFilter),)
    
    search_fields = ('name',)
    
    readonly_fields = ('usage_count', 'created_at', 'avatar_count')
    
    fieldsets = (
        (_('基本信息'), {
            'fields': ('name', 'color')
        }),
        (_('统计信息'), {
            'fields': ('usage_count', 'avatar_count', 'created_at'),
            'classes': ('collapse',)
        }),
    )
    
    ordering = ('-usage_count', 'name')
    
    def color_preview(self, obj):
        """颜色预览"""
        return format_html(
            '<span style="display: inline-block; width: 20px; height: 20px; '
            'background-color: {}; border: 1px solid #ccc; border-radius: 3px;"></span>',
            obj.color
        )
    color_preview.short_description = _('颜色')
    
    def avatar_count(self, obj):
        """关联的数字人形象数量"""
        return obj.avatars.count()
    avatar_count.short_description = _('形象数量')


@admin.register(AvatarUsageLog)
class AvatarUsageLogAdmin(ModelAdmin):
    """数字人形象使用日志管理"""
    
    list_display = (
        'avatar', 'user', 'computing_power_consumed', 'purpose', 'created_at'
    )
    
    list_filter = (
        ('created_at', RangeDateFilter),
        'avatar__model_version',
        'avatar__scene_type',
    )
    
    search_fields = (
        'avatar__name', 'user__username', 'user__phone', 'purpose'
    )
    
    readonly_fields = ('created_at',)
    
    ordering = ('-created_at',)
    
    def has_add_permission(self, request):
        return False
    
    def has_change_permission(self, request, obj=None):
        return False
