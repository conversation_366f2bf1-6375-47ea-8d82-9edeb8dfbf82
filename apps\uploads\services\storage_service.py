"""
文件存储服务
支持本地存储、阿里云OSS、AWS S3等多种存储方式
"""
import os
import logging
import hashlib
import mimetypes
from datetime import datetime
from typing import Dict, Any, Optional, BinaryIO
from django.conf import settings
from django.core.files.storage import default_storage
from django.core.files.base import ContentFile
from apps.config.services import api_config

logger = logging.getLogger(__name__)

try:
    import oss2
    OSS_AVAILABLE = True
except ImportError:
    OSS_AVAILABLE = False
    logger.warning("阿里云OSS SDK未安装")

try:
    import boto3
    from botocore.exceptions import ClientError
    AWS_AVAILABLE = True
except ImportError:
    AWS_AVAILABLE = False
    logger.warning("AWS SDK未安装")


class AliyunOSSService:
    """阿里云OSS存储服务"""
    
    def __init__(self):
        self.auth = None
        self.bucket = None
        self._initialize_service()
    
    def _initialize_service(self):
        """初始化OSS服务"""
        if not OSS_AVAILABLE:
            return
        
        try:
            config = api_config.get_aliyun_oss_config()
            
            if not all([config['access_key'], config['secret_key'], 
                       config['bucket'], config['endpoint']]):
                logger.warning("阿里云OSS配置不完整")
                return
            
            self.auth = oss2.Auth(config['access_key'], config['secret_key'])
            self.bucket = oss2.Bucket(
                self.auth, 
                config['endpoint'], 
                config['bucket']
            )
            
            # 测试连接
            self.bucket.get_bucket_info()
            logger.info("阿里云OSS服务初始化成功")
            
        except Exception as e:
            logger.error(f"阿里云OSS服务初始化失败: {e}")
            self.bucket = None
    
    def is_available(self) -> bool:
        """检查服务是否可用"""
        return OSS_AVAILABLE and self.bucket is not None
    
    def upload_file(
        self,
        file_content: BinaryIO,
        file_path: str,
        content_type: str = None
    ) -> Dict[str, Any]:
        """
        上传文件到OSS
        
        Args:
            file_content: 文件内容
            file_path: 文件路径
            content_type: 文件类型
        
        Returns:
            上传结果
        """
        if not self.is_available():
            return {
                'success': False,
                'error': '阿里云OSS服务不可用'
            }
        
        try:
            # 设置文件元数据
            headers = {}
            if content_type:
                headers['Content-Type'] = content_type
            
            # 上传文件
            result = self.bucket.put_object(
                file_path,
                file_content,
                headers=headers
            )
            
            if result.status == 200:
                # 生成文件URL
                file_url = f"https://{self.bucket.bucket_name}.{self.bucket.endpoint.replace('http://', '').replace('https://', '')}/{file_path}"
                
                return {
                    'success': True,
                    'file_url': file_url,
                    'file_path': file_path,
                    'etag': result.etag
                }
            else:
                return {
                    'success': False,
                    'error': f'上传失败，状态码: {result.status}'
                }
                
        except Exception as e:
            logger.error(f"OSS文件上传失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def delete_file(self, file_path: str) -> Dict[str, Any]:
        """删除OSS文件"""
        if not self.is_available():
            return {
                'success': False,
                'error': '阿里云OSS服务不可用'
            }
        
        try:
            result = self.bucket.delete_object(file_path)
            return {
                'success': True,
                'message': '文件删除成功'
            }
        except Exception as e:
            logger.error(f"OSS文件删除失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_file_url(self, file_path: str, expires: int = 3600) -> str:
        """获取文件访问URL"""
        if not self.is_available():
            return None
        
        try:
            # 生成签名URL
            url = self.bucket.sign_url('GET', file_path, expires)
            return url
        except Exception as e:
            logger.error(f"生成OSS文件URL失败: {e}")
            return None


class AWSS3Service:
    """AWS S3存储服务"""
    
    def __init__(self):
        self.s3_client = None
        self.bucket_name = None
        self._initialize_service()
    
    def _initialize_service(self):
        """初始化S3服务"""
        if not AWS_AVAILABLE:
            return
        
        try:
            config = api_config.get_aws_s3_config()
            
            if not all([config['access_key'], config['secret_key'], config['bucket']]):
                logger.warning("AWS S3配置不完整")
                return
            
            self.s3_client = boto3.client(
                's3',
                aws_access_key_id=config['access_key'],
                aws_secret_access_key=config['secret_key'],
                region_name=config.get('region', 'us-east-1')
            )
            
            self.bucket_name = config['bucket']
            
            # 测试连接
            self.s3_client.head_bucket(Bucket=self.bucket_name)
            logger.info("AWS S3服务初始化成功")
            
        except Exception as e:
            logger.error(f"AWS S3服务初始化失败: {e}")
            self.s3_client = None
    
    def is_available(self) -> bool:
        """检查服务是否可用"""
        return AWS_AVAILABLE and self.s3_client is not None
    
    def upload_file(
        self,
        file_content: BinaryIO,
        file_path: str,
        content_type: str = None
    ) -> Dict[str, Any]:
        """上传文件到S3"""
        if not self.is_available():
            return {
                'success': False,
                'error': 'AWS S3服务不可用'
            }
        
        try:
            extra_args = {}
            if content_type:
                extra_args['ContentType'] = content_type
            
            self.s3_client.upload_fileobj(
                file_content,
                self.bucket_name,
                file_path,
                ExtraArgs=extra_args
            )
            
            file_url = f"https://{self.bucket_name}.s3.amazonaws.com/{file_path}"
            
            return {
                'success': True,
                'file_url': file_url,
                'file_path': file_path
            }
            
        except ClientError as e:
            logger.error(f"S3文件上传失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }


class LocalStorageService:
    """本地存储服务"""
    
    def __init__(self):
        self.media_root = settings.MEDIA_ROOT
        self.media_url = settings.MEDIA_URL
    
    def is_available(self) -> bool:
        """检查服务是否可用"""
        return True
    
    def upload_file(
        self,
        file_content: BinaryIO,
        file_path: str,
        content_type: str = None
    ) -> Dict[str, Any]:
        """保存文件到本地"""
        try:
            # 使用Django的默认存储
            saved_path = default_storage.save(file_path, ContentFile(file_content.read()))
            file_url = default_storage.url(saved_path)
            
            return {
                'success': True,
                'file_url': file_url,
                'file_path': saved_path
            }
            
        except Exception as e:
            logger.error(f"本地文件保存失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def delete_file(self, file_path: str) -> Dict[str, Any]:
        """删除本地文件"""
        try:
            if default_storage.exists(file_path):
                default_storage.delete(file_path)
            
            return {
                'success': True,
                'message': '文件删除成功'
            }
        except Exception as e:
            logger.error(f"本地文件删除失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }


class StorageService:
    """统一存储服务接口"""
    
    def __init__(self):
        self.oss = AliyunOSSService()
        self.s3 = AWSS3Service()
        self.local = LocalStorageService()
        self.default_provider = self._get_default_provider()
    
    def _get_default_provider(self) -> str:
        """获取默认存储提供商"""
        # 优先级：OSS > S3 > Local
        if self.oss.is_available():
            return 'oss'
        elif self.s3.is_available():
            return 's3'
        else:
            return 'local'
    
    def upload_file(
        self,
        file_content: BinaryIO,
        file_name: str,
        folder: str = 'uploads',
        provider: str = None
    ) -> Dict[str, Any]:
        """
        上传文件
        
        Args:
            file_content: 文件内容
            file_name: 文件名
            folder: 文件夹
            provider: 存储提供商
        
        Returns:
            上传结果
        """
        provider = provider or self.default_provider
        
        # 生成文件路径
        file_path = self._generate_file_path(file_name, folder)
        
        # 检测文件类型
        content_type = mimetypes.guess_type(file_name)[0]
        
        # 选择存储服务
        if provider == 'oss' and self.oss.is_available():
            return self.oss.upload_file(file_content, file_path, content_type)
        elif provider == 's3' and self.s3.is_available():
            return self.s3.upload_file(file_content, file_path, content_type)
        else:
            return self.local.upload_file(file_content, file_path, content_type)
    
    def _generate_file_path(self, file_name: str, folder: str) -> str:
        """生成文件路径"""
        # 获取文件扩展名
        name, ext = os.path.splitext(file_name)
        
        # 生成时间戳路径
        now = datetime.now()
        date_path = now.strftime('%Y/%m/%d')
        
        # 生成文件hash
        file_hash = hashlib.md5(f"{file_name}{now.timestamp()}".encode()).hexdigest()[:8]
        
        # 组合最终路径
        final_name = f"{name}_{file_hash}{ext}"
        return f"{folder}/{date_path}/{final_name}"
    
    def delete_file(self, file_path: str, provider: str = None) -> Dict[str, Any]:
        """删除文件"""
        provider = provider or self.default_provider
        
        if provider == 'oss':
            return self.oss.delete_file(file_path)
        elif provider == 's3':
            # S3删除实现
            return {'success': False, 'error': 'S3删除功能待实现'}
        else:
            return self.local.delete_file(file_path)


# 创建全局实例
storage_service = StorageService()


def upload_file(file_content: BinaryIO, file_name: str, folder: str = 'uploads') -> Dict[str, Any]:
    """上传文件的便捷函数"""
    return storage_service.upload_file(file_content, file_name, folder)


def delete_file(file_path: str) -> Dict[str, Any]:
    """删除文件的便捷函数"""
    return storage_service.delete_file(file_path)
