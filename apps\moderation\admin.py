"""
内容审核管理后台配置
"""
from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from django.utils.html import format_html
from django.urls import reverse
from django.utils import timezone
from unfold.admin import ModelAdmin
from unfold.contrib.filters.admin import RangeDateFilter, ChoicesDropdownFilter
from .models import ModerationRule, ModerationRequest, ModerationLog, ContentFlag
from .services import moderation_service


@admin.register(ModerationRule)
class ModerationRuleAdmin(ModelAdmin):
    """审核规则管理"""
    
    list_display = (
        'name', 'rule_type', 'severity', 'content_types_display', 
        'is_active', 'created_at'
    )
    
    list_filter = (
        ('rule_type', ChoicesDropdownFilter),
        ('severity', ChoicesDropdownFilter),
        'is_active',
        ('created_at', RangeDateFilter),
    )
    
    search_fields = ('name', 'description')
    
    readonly_fields = ('created_at', 'updated_at')
    
    fieldsets = (
        (_('基本信息'), {
            'fields': ('name', 'rule_type', 'description')
        }),
        (_('规则配置'), {
            'fields': ('config', 'content_types')
        }),
        (_('设置'), {
            'fields': ('severity', 'is_active')
        }),
        (_('时间信息'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    filter_horizontal = ('content_types',)
    
    def content_types_display(self, obj):
        """显示适用的内容类型"""
        types = obj.content_types.all()
        if types:
            return ', '.join([ct.name for ct in types])
        return '-'
    content_types_display.short_description = _('适用内容类型')


class ModerationLogInline(admin.TabularInline):
    """审核日志内联"""
    model = ModerationLog
    extra = 0
    readonly_fields = ('action', 'user', 'details', 'notes', 'created_at')
    
    def has_add_permission(self, request, obj=None):
        return False


@admin.register(ModerationRequest)
class ModerationRequestAdmin(ModelAdmin):
    """审核请求管理"""
    
    list_display = (
        'content_display', 'submitted_by', 'status', 'priority',
        'auto_review_score', 'reviewer', 'created_at'
    )
    
    list_filter = (
        ('status', ChoicesDropdownFilter),
        ('priority', ChoicesDropdownFilter),
        'content_type',
        ('created_at', RangeDateFilter),
        ('reviewed_at', RangeDateFilter),
    )
    
    search_fields = ('submitted_by__username', 'reviewer__username', 'review_notes')
    
    readonly_fields = (
        'content_type', 'object_id', 'content_link', 'submitted_by',
        'auto_review_score', 'auto_review_details', 'created_at', 'reviewed_at'
    )
    
    fieldsets = (
        (_('审核内容'), {
            'fields': ('content_type', 'object_id', 'content_link', 'submitted_by')
        }),
        (_('审核状态'), {
            'fields': ('status', 'priority', 'reviewer')
        }),
        (_('审核结果'), {
            'fields': ('review_notes', 'rejection_reason')
        }),
        (_('自动审核'), {
            'fields': ('auto_review_score', 'auto_review_details'),
            'classes': ('collapse',)
        }),
        (_('时间信息'), {
            'fields': ('created_at', 'reviewed_at'),
            'classes': ('collapse',)
        }),
    )
    
    inlines = [ModerationLogInline]
    
    actions = ['approve_requests', 'reject_requests', 'assign_to_me']
    
    def content_display(self, obj):
        """显示审核内容"""
        if obj.content_object:
            return f'{obj.content_type.name}: {str(obj.content_object)[:50]}'
        return f'{obj.content_type.name}: [已删除]'
    content_display.short_description = _('审核内容')
    
    def content_link(self, obj):
        """内容链接"""
        if obj.content_object:
            try:
                url = reverse(
                    f'admin:{obj.content_type.app_label}_{obj.content_type.model}_change',
                    args=[obj.object_id]
                )
                return format_html('<a href="{}" target="_blank">查看内容</a>', url)
            except:
                pass
        return '-'
    content_link.short_description = _('内容链接')
    
    def approve_requests(self, request, queryset):
        """批量通过审核"""
        updated = 0
        for req in queryset.filter(status='pending'):
            req.approve(request.user, '批量通过')
            updated += 1
        
        self.message_user(request, f'成功通过 {updated} 个审核请求')
    approve_requests.short_description = _('批量通过审核')
    
    def reject_requests(self, request, queryset):
        """批量拒绝审核"""
        updated = 0
        for req in queryset.filter(status='pending'):
            req.reject(request.user, '批量拒绝', '管理员批量操作')
            updated += 1
        
        self.message_user(request, f'成功拒绝 {updated} 个审核请求')
    reject_requests.short_description = _('批量拒绝审核')
    
    def assign_to_me(self, request, queryset):
        """分配给我"""
        updated = queryset.filter(status='pending').update(reviewer=request.user)
        self.message_user(request, f'成功分配 {updated} 个审核请求给您')
    assign_to_me.short_description = _('分配给我')


@admin.register(ContentFlag)
class ContentFlagAdmin(ModelAdmin):
    """内容举报管理"""
    
    list_display = (
        'content_display', 'reporter', 'flag_type', 'is_resolved',
        'resolved_by', 'created_at'
    )
    
    list_filter = (
        ('flag_type', ChoicesDropdownFilter),
        'is_resolved',
        'content_type',
        ('created_at', RangeDateFilter),
        ('resolved_at', RangeDateFilter),
    )
    
    search_fields = ('reporter__username', 'reason', 'resolution_notes')
    
    readonly_fields = (
        'content_type', 'object_id', 'content_link', 'reporter',
        'created_at', 'resolved_at'
    )
    
    fieldsets = (
        (_('举报内容'), {
            'fields': ('content_type', 'object_id', 'content_link', 'reporter')
        }),
        (_('举报信息'), {
            'fields': ('flag_type', 'reason')
        }),
        (_('处理结果'), {
            'fields': ('is_resolved', 'resolved_by', 'resolution_notes')
        }),
        (_('时间信息'), {
            'fields': ('created_at', 'resolved_at'),
            'classes': ('collapse',)
        }),
    )
    
    actions = ['resolve_flags', 'submit_for_review']
    
    def content_display(self, obj):
        """显示举报内容"""
        if obj.content_object:
            return f'{obj.content_type.name}: {str(obj.content_object)[:50]}'
        return f'{obj.content_type.name}: [已删除]'
    content_display.short_description = _('举报内容')
    
    def content_link(self, obj):
        """内容链接"""
        if obj.content_object:
            try:
                url = reverse(
                    f'admin:{obj.content_type.app_label}_{obj.content_type.model}_change',
                    args=[obj.object_id]
                )
                return format_html('<a href="{}" target="_blank">查看内容</a>', url)
            except:
                pass
        return '-'
    content_link.short_description = _('内容链接')
    
    def resolve_flags(self, request, queryset):
        """批量处理举报"""
        updated = 0
        for flag in queryset.filter(is_resolved=False):
            flag.resolve(request.user, '管理员批量处理')
            updated += 1
        
        self.message_user(request, f'成功处理 {updated} 个举报')
    resolve_flags.short_description = _('批量处理举报')
    
    def submit_for_review(self, request, queryset):
        """提交审核"""
        submitted = 0
        for flag in queryset:
            if flag.content_object:
                moderation_service.submit_for_review(
                    flag.content_object,
                    request.user,
                    priority='high'
                )
                submitted += 1
        
        self.message_user(request, f'成功提交 {submitted} 个内容进行审核')
    submit_for_review.short_description = _('提交审核')
